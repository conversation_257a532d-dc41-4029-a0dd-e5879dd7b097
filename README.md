## Tickster Events WordPress plugin ##
- pulls events from Tickster API and saves them in the custom database table DB_PREFIX_tickster_events
- supports adding/updating events
- doesn't support deleting events

## Setup ##
- `composer install`
- rename .env.example in plugin root to .env
- add TICKSTER_API_KEY to .env file
- change TICKSTER_LANGUAGE_CODE and TICKSTER_API_VERSION if needed
- add TICKSTER_EVENTS_QUERY according to the Tickster documentation/client requirements
- change UPDATE_EVENTS_TOKEN to .env file (random alphanumeric string - characters and numbers only, no symbols)
- activate plugin
- use `Tickster_Events_Public::get_events()` function to list upcoming events
- set CRON job to periodically run update events endpoint

## REST API endpoints ##
- update events from Tickster API by calling: `tickster/v1/update/UPDATE_EVENTS_TOKEN`
- UPDATE_EVENTS_TOKEN is taken from .env file and serve as a basic endpoint protection