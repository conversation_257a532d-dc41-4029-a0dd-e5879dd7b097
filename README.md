# [Bathe](https://ixkaito.github.io/bathe/) [![GitHub release](https://img.shields.io/github/v/release/ixkaito/bathe?color=ed64a6)](https://github.com/wp-bathe/bathe/releases) [![license](https://img.shields.io/badge/license-GPL--2.0%2B-orange)](https://github.com/wp-bathe/bathe/blob/master/LICENSE) [![Test](https://github.com/ixkaito/bathe/workflows/Test/badge.svg)](https://github.com/ixkaito/bathe/actions) [![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/ixkaito/bathe/pulls)

Bathe is the simplest WordPress starter theme including full setup for Tailwind CSS, Sass, PostCSS, Autoprefixer, Webpack, TypeScript, Browsersync, imagemin, Prettier, stylelint, ESLint.

## Documentation

You can find the Bathe documentation [on the website](https://ixkaito.github.io/bathe/).

## Copyright / License

© 2020 the contributors of Bathe under the [GPL version 2.0](https://raw.githubusercontent.com/wp-bathe/bathe/master/LICENSE) or later.

# Dev Flow
- `node v16.19.0`
- `npm v8.19.3`
- `composer install`
- `npm install`
- remove .example extension from .env.example file
- change browsersync proxy and port in .env file if neccessary
- `npm run dev`

# Env variables
- .env file support for browsersync settings, API keys and other credentials
- variables are accessible in PHP with `$_ENV['VAR_NAME']` or `$_SERVER['VAR_NAME']`