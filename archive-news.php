<?php
/**
 * The template for displaying news.
 */

$context = Timber::get_context();

// Get current page number
$paged = get_query_var('paged') ? get_query_var('paged') : 1;

// Set the title with page number for paginated pages
if ($paged > 1) {
    $context['title'] = sprintf(__('Nyheter - Sida %d', 'solvalla'), $paged);
} else {
    $context['title'] = __('Nyheter', 'solvalla');
}
$context['posts'] = new Timber\PostQuery();

// Add ACF fields to context.
foreach( $context['posts'] as $key => $post ) {
    $context['posts'][$key]->thumbnail_crop = get_field( 'thumbnail', $post->ID );
}

Timber::render( 'archive.twig', $context );
