{"name": "bathe", "version": "2.0.0", "description": "The simplest WordPress starter theme including full setup for Tailwind CSS, Sass, PostCSS, Autoprefixer, Webpack, TypeScript, Browsersync, imagemin, Prettier, stylelint, ESLint.", "browserslist": ["> 1%", "last 2 versions", "Firefox ESR"], "config": {"image": {"src": "_src/images", "dist": "assets/images"}, "js": {"entry": ["./_src/js/**/*.js"], "output": "./assets/js"}, "css": {"src": "_src/sass", "dist": "assets/css", "style": "compressed", "map": "--map"}}, "scripts": {"dev": "npm-run-all -p imagemin css webpack -p browser watch", "browser": "cross-conf-env dotenv -- cross-var browser-sync \"%BROWSER_SYNC_PROXY%\" -f \"**/*.php\" \"$npm_package_config_css_dist\" \"$npm_package_config_js_output\" \"$npm_package_config_image_dist\" --port %BROWSER_SYNC_PORT%", "build": "run-p imagemin css webpack", "css": "run-p sass", "imagemin": "cross-conf-env node imagemin.mjs \"$npm_package_config_image_src/**/*\" $npm_package_config_image_dist", "presass": "cross-conf-env stylelint \"$npm_package_config_css_src/**/*\" --fix", "sass": "cross-conf-env sass $npm_package_config_css_src:$npm_package_config_css_dist --style=compressed", "postsass": "cross-conf-env postcss $npm_package_config_css_dist/*.css --use=autoprefixer --dir=$npm_package_config_css_dist $npm_package_config_css_map", "webpack": "cross-conf-env webpack $npm_package_config_js_entry -o $npm_package_config_js_output", "watch:imagemin": "cross-conf-env onchange \"$npm_package_config_image_src/**/*\" -e \".DS_Store\" -- npm run imagemin", "watch:sass": "cross-conf-env onchange \"$npm_package_config_css_src/**/*\" -e \".DS_Store\" -- npm run sass", "watch:webpack": "npm run webpack -- --watch", "watch": "run-p watch:*"}, "repository": {"type": "git", "url": "https://github.com/wp-bathe/bathe.git"}, "keywords": ["WordPress", "Browsersync", "Sass", "PostCSS", "Autoprefixer", "imagemin", "Webpack", "stylelint", "ESLint"], "author": "<PERSON>e", "license": "GPL-2.0+", "bugs": {"url": "https://github.com/wp-bathe/bathe/issues"}, "homepage": "https://github.com/wp-bathe/bathe", "engines": {"node": ">= 16.14.0", "npm": ">= 8.3.1"}, "devDependencies": {"@babel/core": "^7.17.8", "@babel/preset-env": "^7.16.11", "@ixkaito/imagemin": "^0.1.0", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "autoprefixer": "^10.4.4", "babel-loader": "^8.2.4", "browser-sync": "^2.27.9", "copy-webpack-plugin": "^11.0.0", "cross-conf-env": "^1.2.1", "cross-var": "^1.1.0", "css-loader": "^6.7.1", "dotenv-cli": "^7.0.0", "eslint": "^8.12.0", "eslint-webpack-plugin": "^3.1.1", "extra-watch-webpack-plugin": "^1.0.3", "globby": "^13.1.1", "imagemin-gifsicle": "^7.0.0", "imagemin-jpegtran": "^7.0.0", "imagemin-optipng": "^8.0.0", "imagemin-svgo": "^10.0.1", "mini-css-extract-plugin": "^2.6.0", "npm-run-all": "^4.1.5", "onchange": "^7.1.0", "postcss-cli": "^9.1.0", "postcss-loader": "^6.2.1", "sass": "^1.49.10", "sass-loader": "^12.6.0", "style-loader": "^3.3.1", "stylelint": "^14.6.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-sass-guidelines": "^9.0.1", "ts-loader": "^9.2.8", "typescript": "^4.6.3", "webpack": "^5.70.0", "webpack-cli": "^4.9.2"}, "dependencies": {"@popperjs/core": "^2.11.6", "axios": "^1.3.4", "bootstrap": "^5.2.3", "gsap": "^3.11.4", "lightbox2": "^2.11.4", "lozad": "^1.16.0", "magnific-popup": "^1.1.0", "plyr": "^3.7.8", "slick-carousel": "^1.8.1", "smartcrop": "^2.0.5"}}