{#
/**
* Block Name: Tickster Calendar
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

{% block blockContent %}

	{% if is_preview %}
		{% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Tickster Calendar Block</strong>' } %}
	{% else %}

		{% if events is not empty %}
			<section class="block--tickster-calendar content-block" id="{{ block.id }}">
				<div class="container-fluid calendar">

					{% set current_month = 0 %}
					{% for event in events|slice( 0, fields.number ) %}

						{# Months separator #}
						{% if current_month != event.event_date|date("m-Y") %}

							<div class="calendar__separator">
								<div>{{ event.event_date|date("F")|capitalize|e }}</div>
							</div>

							{% set current_month = event.event_date|date("m-Y") %}

						{% endif %}

						{# Calendar event #}
						<div
							class="calendar__event">
							{# Image #}
							{% if event.local_thumbnail_url is not empty %}
								<a class="block pb-3 my-2 calendar__event__img my-lg-0 pb-lg-0 d-block" href="{{ event.link_replacement ? event.link_replacement.url|e('esc_url') : event.local_url|e('esc_url') }}">
									<img class="w-100 d-block img-fluid" src="{{ event.local_thumbnail_url|e('esc_url') }}" width="781" height="439" alt="{{ event.event_name|e('html_attr') }}">
								</a>
							{% endif %}

							{# Image #}
							{# {% if event.local_thumbnail_url is not empty %}
								<img class="calendar__event__img img-fluid" src="{{ event.local_thumbnail_url|e('esc_url') }}" alt="{{ event.event_name|e('html_attr') }}">
								<a class="pb-3 my-2 calendar__event__img--mobile w-100 my-lg-0 pb-lg-0 d-block d-lg-none" href="{{ event.local_url|e('esc_url') }}">
									<img class="w-100 d-block img-fluid" src="{{ event.local_thumbnail_url|e('esc_url') }}" alt="{{ event.event_name|e('html_attr') }}">
								</a>
							{% endif %} #}

							<div
								class="calendar__event__content">
								{# Event details #}
								<div
									class="calendar__event__details">
									{# Title #}
									<h4>
										<a class="calendar__event__link text-decoration-none text-cod-grey text-uppercase" href="{{ event.link_replacement ? event.link_replacement.url|e('esc_url') : event.local_url|e('esc_url') }}">
											{{ event.event_name|e('wp_kses_post') }}
											-
											{{ event.event_date|date("j")|e }}
											{{ event.event_date|date("F")|capitalize|e }}
										</a>
									</h4>

									{# Meta information #}
									<div class="calendar__event__meta row">
										<div class="col-md-12 col-lg">
											<div>{{ __( 'Datum', 'solvalla' ) }}</div>
											<div>{{ event.event_date|date("j")|e }}
												{{ event.event_date|date("F")|capitalize|e }}
												-
												{{ event.event_date|date("H:i")|e }}</div>
										</div>
										<div class="col-md-12 col-lg">
											<div>{{ __( 'Entreérna öppnar', 'solvalla' ) }}</div>
											<div>{{ event.doors_open|date("H:i")|e }}</div>
										</div>
									</div>

								</div>


								{# Event links #}
								<div
									class="calendar__event__links">

									{# Travprogram #}
									{% if event.links is not empty %}
										{% for link in event.links %}
											{% if link.text == 'Travprogram' %}
												<div class="link-styled link-styled--program link-styled--chathams-blue">
													<a class="calendar__event__link link-mine-shaft" href="{{ link.url|e('esc_url') }}" target="_blank">
														{{ link.text }}
													</a>
												</div>
											{% endif %}
										{% endfor %}
									{% endif %}

									{# Buy tickets #}
									{% if event.event_state == 'saleEnded' or event.event_state == 'SaleEnded' or 'fri entré' in event.event_description or 'Fri entré' in event.event_description %}
										{# <div class="link-styled link-styled--ticket link-styled--tall-poppy">
																					<a class="calendar__event__link link-mine-shaft" href="javascript:;">
																						{{ __( 'Slut på försäljning', 'solvalla' ) }}
																					</a>
																				</div> #}
									{% elseif event.stock_status in ['instock', 'fewleft', 'InStock', 'FewLeft'] %}
										<div class="link-styled link-styled--ticket link-styled--galliano">
											<a class="calendar__event__link link-mine-shaft" href="{{ event.tickets_url|e('esc_url') }}" target="_blank">
												{{ __( 'Köp biljetter', 'solvalla' ) }}
											</a>
										</div>
									{% else %}
										<div class="link-styled link-styled--ticket link-styled--tall-poppy">
											<a class="calendar__event__link link-mine-shaft" href="javascript:;">
												{{ __( 'Stängt för försäljning', 'solvalla' ) }}
											</a>
										</div>
									{% endif %}

									{# Read more #}
									<div class="col link-styled link-styled--arrow-top">
										<a class="calendar__event__link link-mine-shaft" href="{{ event.link_replacement ? event.link_replacement.url|e('esc_url') : event.local_url|e('esc_url') }}">
											{{ event.link_replacement and event.link_replacement.title ? event.link_replacement.title : __( 'Läs mer', 'solvalla' ) }}
										</a>
									</div>


								</div>
							</div>

						</div>

					{% endfor %}

				</div>

			</section>

		{% endif %}

	{% endif %}

{% endblock %}
