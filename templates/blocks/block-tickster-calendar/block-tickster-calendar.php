<?php
/**
* Block Name: Tickster Calendar
*/

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

// Get Tickster events from database.
$context['events'] = [];
$context['events'] = Tickster_Events_Public::get_events();

// Add manual event for August 19 at Kongressen
$manual_event = (object) [
    'id' => 'manual_kongressen_aug19',
    'event_name' => 'August 19 at Kongressen',
    'event_date' => '2024-08-19 17:30:00',
    'doors_open' => '2024-08-19 17:00:00',
    'event_end' => '2024-08-19 22:00:00',
    'slug' => '20240819',
    'event_state' => 'active',
    'stock_status' => 'instock',
    'event_description' => 'Welcome drink from 17:00, Auction starts at 17:30',
    'image_url' => '',
    'thumbnail_url' => '',
    'local_thumbnail_url' => '',
    'tags' => ['Event'],
    'links' => [],
    'packages' => [],
    'tickets_url' => 'https://secure.tickster.com/n9clyr6k1cc517g',
    'local_url' => '/event/kongressen-aug19',
    'link_replacement' => false,
    'is_manual_event' => true,
    'manual_open_label' => 'Öppnar',
    'manual_start_label' => 'Auktionen startar'
];

// Insert manual event at the beginning of the events array
array_unshift($context['events'], $manual_event);

$image_helper = new Timber\ImageHelper();

foreach( $context['events'] as $key => $event ) {

    // Assign correct category slug to events
    if( is_array( $event->tags ) && in_array( 'Event', $event->tags ) ) {
        $context['events'][$key]->local_url = '/event/' . $event->slug;
    } elseif( is_array( $event->tags ) && in_array( 'Racing', $event->tags ) ) {
        $context['events'][$key]->local_url = '/travtavlingar/' . $event->slug;
    }

    // Cache external images locally
    $context['events'][$key]->local_thumbnail_url = @$image_helper::resize( $context['events'][$key]->image_url, 781 );
    
    // Check for link replacements in options
    $context['events'][$key]->link_replacement = false;
    // Get option value from ACF
    $tickster_replacements = get_field('tickster_replace_event_details', 'option');
    
    if( is_array($tickster_replacements) && !empty($tickster_replacements) ) {
        foreach( $tickster_replacements as $replacement ) {
            if( isset( $replacement['which_event'] ) && $replacement['which_event'] == $event->id && !empty( $replacement['las_mer_link'] ) ) {
                $context['events'][$key]->link_replacement = $replacement['las_mer_link'];
                break;
            }
        }
    }
}

Timber::render( 'templates/blocks/block-tickster-calendar/block-tickster-calendar.twig', $context );
