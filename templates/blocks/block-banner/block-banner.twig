{#
/**
* Block Name: Block banner
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {# {% if is_preview and fields.title is empty %} #}
    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Banner Block</strong>' } %}
    {% else %}

        {% if fields.banner_ID and fields.background_image and fields.background_image_mobile %}
            <section class="block--banner">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12 d-flex justify-content-{{ ( fields.banner_alignment is not empty ) ? fields.banner_alignment : 'center' }}">
                            {% if fields.banner_link.url %}
                                <a class="js-banner" data-banner data-post-id="{{ fields.banner_ID }}" href="{{ fields.banner_link.url|e('esc_url') }}" target="{{ fields.banner_link.target|e('html_attr') }}" title="{{ fields.banner_link.title|e('html_attr') }}">
                            {% endif %}
                                    <img data-lightbox="gallery" width="{{ fields.background_image.width }}" height="{{ fields.background_image.height }}" src="" data-src="{{ Image(fields.background_image_mobile).src }}" data-image-md="{{ Image(fields.background_image).src }}" data-mobile-width="{{ fields.background_image_mobile.width }}" data-mobile-height="{{ fields.background_image_mobile.height }}" class="block-banner__image img-fluid" alt="banner">
                            {% if fields.banner_link.url %}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </section>
        {% endif %}

    {% endif %}

{% endblock %}
