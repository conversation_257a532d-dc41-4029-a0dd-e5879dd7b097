<?php
/**
* Block Name: Block banner
*/

$context = Timber::context();

$context['block'] = $block;
$context['is_preview'] = $is_preview;
$banners_taxonomy = get_field( 'banner_block' );

if ( ! $banners_taxonomy ) {
    $banners_group_id = false;
} else {
    $taxonomy = 'groupped';
    $term_id = $banners_taxonomy->term_id;
    $banners_group = get_posts([
        'post_type' => 'banners',
        'tax_query' => [
            [
                'taxonomy' => $taxonomy,
                'field' => 'term_id',
                'terms' => $term_id,
            ],
        ],
        'orderby' => 'rand',
        'posts_per_page' => 1,
        'fields' => 'ids',
    ]);
    if( $banners_group ) {
        $banners_group_id = $banners_group[0];
    } else {
        $banners_group_id = false;
    }
}

// add ACF fields to context
$fields = [
    'background_image'          => ( $banners_group_id && $banners_group_id != '' ) ? get_field( 'background_image', $banners_group_id ) : '',
    'banner_link'               => ( $banners_group_id && $banners_group_id != '' ) ? get_field( 'button', $banners_group_id ) : '',
    'background_image_mobile'   => ( $banners_group_id && $banners_group_id != '' ) ? get_field( 'background_image_mobile', $banners_group_id ) : '',
    'banner_alignment'          => ( $banners_group_id && $banners_group_id != '' ) ? get_field( 'banner_alignment' ) : '',
    'banner_ID'                 => ( $banners_group_id && $banners_group_id != '' ) ? $banners_group_id : false,
];

$context['fields'] = $fields;

Timber::render( 'templates/blocks/block-banner/block-banner.twig', $context );