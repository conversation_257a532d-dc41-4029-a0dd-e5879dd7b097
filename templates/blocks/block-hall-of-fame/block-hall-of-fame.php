<?php
/**
* Block Name: Hall of Fame
*/

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

// Add ACF fields to context.
$number = get_field( 'number' );
$fields = [
    'number' => ( $number && $number > 0 ) ? $number : -1,
];
$context['fields'] = $fields;

// Get posts.
$posts = Timber::get_posts([
    'post_type'     => 'hall-of-fame',
    'numberposts'   => $fields['number'],
]);
$context['posts'] = $posts;

// Add ACF fields to context.
foreach( $posts as $key => $post ) {
    $context['posts'][$key]->thumbnail_crop = get_field( 'thumbnail', $post->ID );
}

Timber::render( 'templates/blocks/block-hall-of-fame/block-hall-of-fame.twig', $context );
