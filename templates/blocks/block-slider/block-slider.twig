{#
/**
* Block Name: Tickster Slider
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

{% block blockContent %}

	{% if is_preview %}
		{% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Tickster Slider Block</strong>' } %}
	{% else %}

		{% if events is not empty %}
			<section
				class="block--slider" id="{{ post.id }}">

				{# Slider #}
				<div
					id="home-carousel" class="carousel slide carousel-fade content-block" data-bs-ride="carousel" data-bs-interval="7000" data-bs-pause="false">

					{# Slider content #}
					<div class="carousel-inner">
						{% for event in events %}
							<div data-bs-interval="{{ event.slide_autoplay_delay ?? '7000' }}" class="carousel-item top-image {{ loop.index == 1 ? 'active ' : '' }}{{ event.type == 'tickster' ? 'top-image-tickster ' : '' }}{{ event.type == 'custom' and event.text_position == 'left' ? 'focus-point-right' : 'focus-point-left' }}" style="--button_acf_background:{{event.button_background ?? '#fff'}};--button_acf_text_color:{{event.button_text_color ?? '#000'}};--button_acf_border_hover_color:{{event.button_border_hover_color ?? '#fff'}};--button_acf_text_hover_color:{{event.button_text_hover_color ?? '#fff'}};--button_acf_heading_color:{{event.heading_color ?? '#fff'}};--button_acf_small_text_color:{{event.small_text_color ?? '#fff'}};--overlay_opacity:{{event.add_overlay and event.overlay_opacity ? event.overlay_opacity : 0}};--small_text_font_size:{{event.small_text_font_size ?? 1}};--heading_font_size:{{event.heading_font_size ?? 1}}">

								{% if event.video_link == '' and event.video_link_webm == '' %}
									{# Background image #}
									{% if event.type == 'tickster' %}
										{% set mobile_image_url = event.image_mobile ? event.image_mobile.url : fields.image_mobile_placeholder %}
                                        {% set mobile_replacement_image_url = '' %}
										{% for custom_slide in custom_settings_for_tickster_slides %}
											{% if event.id|trim == custom_slide.which_slide|trim %}
												{% set mobile_replacement_image_url = custom_slide.slide_mobile_image_replacement.sizes.medium_large %}
											{% endif %}
										{% endfor %}
										<img class="top-image__desktop img-fluid" width="1920" height="1080" src="{{ event.local_image_url }}" alt="{{ event.event_name|e('html_attr') }}">
										<img class="top-image__tablet img-fluid" width="991" height="800" src="{{ fields.image_tablet_placeholder }}" alt="{{ event.event_name|e('html_attr') }}">
										{% if mobile_replacement_image_url is not empty %}
										    <img style="object-fit: cover; aspect-ratio: 800/991" width="800" height="991" class="top-image__mobile-custom" src="{{ mobile_replacement_image_url }}" alt="{{ event.event_name|e('html_attr') }}">
                                        {% else %}
										    <img class="top-image__mobile_large img-fluid" width="680" height="880" src="{{ fields.image_mobile_large_placeholder }}" alt="{{ event.event_name|e('html_attr') }}">
											<img class="top-image__mobile img-fluid" width="600" height="1200" src="{{ mobile_image_url }}" alt="{{ event.event_name|e('html_attr') }}">
										{% endif %}
									{% else %}
										<img class="top-image__desktop img-fluid" width="1920" height="1080" src="{{ event.image ? event.image.url : fields.image_placeholder }}" alt="{{ event.event_name|e('html_attr') }}">
										<img class="top-image__tablet img-fluid" width="991" height="800" src="{{ event.image_tablet ? event.image_tablet.url : fields.image_tablet_placeholder }}" alt="{{ event.event_name|e('html_attr') }}">
										<img class="top-image__mobile_large img-fluid" width="680" height="880" src="{{ event.image_mobile_large ? event.image_mobile_large.url : fields.image_mobile_large_placeholder }}" alt="{{ event.event_name|e('html_attr') }}">
										<img class="top-image__mobile img-fluid" width="600" height="1200" src="{{ event.image_mobile ? event.image_mobile.url : fields.image_mobile_placeholder }}" alt="{{ event.event_name|e('html_attr') }}">
									{% endif %}
								{% else %}
									{# Video #}
									<div class="img-fluid" style="position: relative;">
										<video autoplay muted loop playsinline style=" width: 100%; object-fit: cover; object-position: center; height: 100%; position: absolute;" {{ loop.index == 1 ? 'preload="metadata"' : 'preload="none"' }}>
											{% if event.video_link_webm %}
												<source {{ loop.index == 1 ? 'src="' ~ event.video_link_webm ~ '"' : 'data-src="' ~ event.video_link_webm ~ '"' }} type="video/webm" />
											{% endif %}
											{% if event.video_link %}
												<source {{ loop.index == 1 ? 'src="' ~ event.video_link ~ '"' : 'data-src="' ~ event.video_link ~ '"' }} type="video/mp4">
											{% endif %}
										</video>
									</div>
								{% endif %}

								{# Content #}
								<div class="top-image__content">
									<div class="container-fluid">

										<div class="top-image__content__inner">

											{# Description #}
											{# <div class="top-image__content__desc text-uppercase">
												<p>{{ event.event_description|striptags }}</p>
											</div> #}

											{# Meta #}
												<div class="top-image__content__meta text-uppercase" style="color: {{event.small_text_color}}"> {% if event.type == 'tickster' %}
													{{ event.event_date|date("j")|e }}
													{{ event.event_date|date("F")|capitalize|e }}
													-
													{{ __( 'SOLVALLA', 'solvalla' ) }}
												{% else %}
													{{ event.small_text|e('wp_kses_post') }}
												{% endif %}
											</div>

											{# Title #}
											<h1 class="display-2 text-uppercase" style="color: {{event.heading_color}}">
												{% if event.type == 'tickster' %}
													{{ event.event_name|e('wp_kses_post') }}
												{% else %}
													{{ event.heading|e('wp_kses_post') }}
												{% endif %}
											</h1>

											{# Button #}
											<div class="flex-wrap gap-3 top-image__content__btns d-flex align-items-center">

												{% if event.type == 'tickster' %}

													{# Buy tickets #}
													{% if event.event_state == 'saleEnded' or event.event_state == 'SaleEnded' or 'fri entré' in event.event_description or 'Fri entré' in event.event_description %}
														{# <div class="link-styled link-styled--ticket link-styled--tall-poppy link-styled--padding-large">
															<span class="calendar__event__link link-white text-uppercase">
																{{ __( 'Slut på försäljning', 'solvalla' ) }}
															</span>
														</div> #}
													{% elseif event.stock_status in ['instock', 'fewleft', 'InStock', 'FewLeft'] %}
														<a class="btn btn--large btn-custom btn--ticket text-uppercase" href="{{ event.tickets_url|e('esc_url') }}" target="_blank">
															{{ __( 'Köp biljetter', 'solvalla' ) }}
														</a>
													{% else %}
														<div class="link-styled link-styled--ticket link-styled--tall-poppy link-styled--padding-large">
															<span class="calendar__event__link link-white text-uppercase">
																{{ __( 'Stängt för försäljning', 'solvalla' ) }}
															</span>
														</div>
													{% endif %}

													{# Read more #}
													<a class="btn btn--large btn-custom btn--dot text-uppercase" href="{{ event.local_url|e('esc_url') }}">
														{{ __( 'Läs mer', 'solvalla' ) }}
													</a>

												{% else %}

													{# Custom button #}
													{% if event.button %}
														<a class="btn btn--large btn-custom btn--dot text-uppercase" href="{{ event.button.url|e('esc_url') }}" target="{{ event.button.target|e('html_attr') }}">
															{{ event.button.title|e('wp_kses_post') }}
														</a>
													{% endif %}

													{# Custom button 2 #}
													{% if event.button2 %}
														<a class="btn btn--large btn-custom btn--dot text-uppercase" href="{{ event.button2.url|e('esc_url') }}" target="{{ event.button2.target|e('html_attr') }}">
															{{ event.button2.title|e('wp_kses_post') }}
														</a>
													{% endif %}

												{% endif %}


											</div>

										</div>

									</div>
								</div>

								{# Slider indicators #}
								{% if events|length > 1 %}
									<div class="carousel-indicators-wrapper">
										<div class="container-fluid">
											<div class="carousel-indicators">
												{% for event in events %}
													<button type="button" data-bs-target="#home-carousel" data-bs-slide-to="{{ loop.index - 1 }}" class="{{ loop.parent.loop.index == loop.index ? 'active' : '' }}" aria-current="{{ loop.parent.loop.index == loop.index ? 'true' : 'false' }}" aria-label="Slide {{ loop.index }}"></button>
												{% endfor %}
											</div>
										</div>
									</div>
								{% endif %}

							</div>
						{% endfor %}
					</div>

					{# Slider prev arrow #}
					{# <button class="carousel-control-prev" type="button" data-bs-target="#home-carousel" data-bs-slide="prev">
						<span class="carousel-control-prev-icon" aria-hidden="true"></span>
						<span class="visually-hidden">Previous</span>
					</button> #}

					{# Slider next arrow #}
					{# <button class="carousel-control-next" type="button" data-bs-target="#home-carousel" data-bs-slide="next">
						<span class="carousel-control-next-icon" aria-hidden="true"></span>
						<span class="visually-hidden">Next</span>
					</button> #}

					{# Slider navigation #}
						<div class="block--slider__nav"> <div
							class="container-fluid">

							{# Links #}
							<div class="gap-3 block--slider__nav__row d-flex">
								{% if fields.link1 %}
									<div class="block--slider__nav__col d-flex apply-first-link">
										<a class="link-white link-slice-hover text-decoration-none d-flex align-items-center" href="{{ fields.link1.url|e('esc_url') }}" data-link-alt="{{ fields.link1.title|upper }}">
											{{ source( directory ~ '/assets/images/icons/icon-calendar-empty.svg' ) }}
											<span>{{ fields.link1.title|upper }}</span>
										</a>
									</div>
								{% endif %}
								{% if fields.link2 %}
									<div class="block--slider__nav__col d-flex apply-first-link">
										<a class="link-white link-slice-hover text-decoration-none d-flex align-items-center" href="{{ fields.link2.url|e('esc_url') }}" data-link-alt="{{ fields.link2.title|upper }}">
											{{ source( directory ~ '/assets/images/icons/icon-fork-knife-alt.svg' ) }}
											<span>{{ fields.link2.title|upper }}</span>
										</a>
									</div>
								{% endif %}
								{% if fields.link3 %}
									<div class="block--slider__nav__col d-flex apply-first-link">
										<a class="link-white link-slice-hover text-decoration-none d-flex align-items-center" href="{{ fields.link3.url|e('esc_url') }}" data-link-alt="{{ fields.link3.title|upper }}">
											{{ source( directory ~ '/assets/images/icons/icon-road.svg' ) }}
											<span>{{ fields.link3.title|upper }}</span>
										</a>
									</div>
								{% endif %}
							</div>

						</div>
					</div>

				</div>

			</section>
		{% endif %}

	{% endif %}

{% endblock %}
