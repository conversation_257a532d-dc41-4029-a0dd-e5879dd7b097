<?php

/**
 * Block Name: Tickster Slider
 */

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace('block_', '', $block['id']);
$context['is_preview'] = $is_preview;

// Add ACF fields to context.
$number = get_field('number'); // how many slides to show
$fields = [
    'number' => ($number && is_numeric($number) && $number > 0) ? $number : 5,
];
$context['fields'] = $fields;

/*
 * Into this array we will save all slide IDs that we want to include in the slider.
 * 
 * Also in this array, we have mixed slide IDs from Tickster and custom slides.
 * Tickster ID exmaple: 9fjckhyuu1alrzb
 * Custom slide ID example: custom_123
 * 
 * We will use this array later to get slides from Tickster and custom slides from database.
 */
$include = [];

/**
 * Get priority rules from Theme Settings.
 */
// error_log("\n-----------------------------------------", 3, WP_CONTENT_DIR . "/slider.log");

$scheduled_rules_rows = get_field('scheduled_priority_rules', 'option');
if ($scheduled_rules_rows) {
    foreach ($scheduled_rules_rows as $row) {
        
        // Skip if we don't have slide ID
        if (!isset($row['scheduled_slide']) || empty($row['scheduled_slide'])) {
            continue;
        }

        // Create scheduled rule array
        $scheduled_rule = [
            'slide' => $row['scheduled_slide'],
            'priority' => isset($row['priority']) ? $row['priority'] : 90,
            'schedule' => isset($row['schedule']) ? '1' : '0',
        ];

        // Create scheduled rule schedule
        if ($row['schedule'] == '1') {
            $scheduled_rule['type'] = isset($row['type']) && !empty($row['type']) ? $row['type'] : 'fixed_date';
            $scheduled_rule['whole_day'] = isset($row['whole_day']) && !empty($row['whole_day']) ? '1' : '0';

            if (isset($row['type']) && $row['type'] == 'fixed_date' && isset($row['date'])) {
                $scheduled_rule['date'] = isset($row['date']) && !empty($row['date']) ? $row['date'] : false;
            }
            if (isset($row['type']) && $row['type'] == 'date_range') {
                $scheduled_rule['date_from'] = isset($row['date_from']) && !empty($row['date_to']) ? $row['date_from'] : '19700101';
                $scheduled_rule['date_to'] = isset($row['date_to']) && !empty($row['date_to']) ? $row['date_to'] : '30001231';
            }
            if (isset($row['type']) && $row['type'] == 'week_days') {
                $scheduled_rule['week_days'] = isset($row['week_days']) && !empty($row['week_days']) ? $row['week_days'] : [];
            }
            if (isset($row['whole_day']) && $row['whole_day'] == '0') {
                $scheduled_rule['time_from'] = isset($row['time_from']) && !empty($row['time_to']) ? $row['time_from'] : '00:00:00';
                $scheduled_rule['time_to'] = isset($row['time_to']) && !empty($row['time_to']) ? $row['time_to'] : '23:59:59';
            }
        }

        // Log scheduled rule
        //error_log("\nRule for slide: " . $scheduled_rule['slide'], 3, WP_CONTENT_DIR . "/slider.log");
        //error_log("\nPriority: " . $scheduled_rule['priority'], 3, WP_CONTENT_DIR . "/slider.log");

        // Decide if scheduled rule is active or not.
        if ($scheduled_rule['schedule'] == '1') {

            // Check if date is within the range
            date_default_timezone_set("Europe/Stockholm");
            if (isset($scheduled_rule['type']) && $scheduled_rule['type'] == 'fixed_date') {
                
                // Debug
                //error_log("\nRule for slide: " . $scheduled_rule['slide'], 3, WP_CONTENT_DIR . "/slider.log");
                //error_log("\nCurrent date: " . date('Ymd'), 3, WP_CONTENT_DIR . "/slider.log");
                // if ($scheduled_rule['date']) {
                //     error_log("\nFixed date: " . date('Ymd', strtotime($scheduled_rule['date'])), 3, WP_CONTENT_DIR . "/slider.log");
                // } else {
                //     error_log("\nFixed date: not set", 3, WP_CONTENT_DIR . "/slider.log");
                // }

                // Skip if date is not set or not today
                if (!$scheduled_rule['date'] || date('Ymd') != date('Ymd', strtotime($scheduled_rule['date']))) {
                    continue;
                }

            } else if (isset($scheduled_rule['type']) && $scheduled_rule['type'] == 'date_range') {

                // Debug
                //error_log("\nRule for slide: " . $scheduled_rule['slide'], 3, WP_CONTENT_DIR . "/slider.log");
                //error_log("\nCurrent date: " . date('Ymd'), 3, WP_CONTENT_DIR . "/slider.log");
                // if ($scheduled_rule['date_from']) {
                //     error_log("\nDate from: " . date('Ymd', strtotime($scheduled_rule['date_from'])), 3, WP_CONTENT_DIR . "/slider.log");
                // } else {
                //     error_log("\nDate from: not set", 3, WP_CONTENT_DIR . "/slider.log");
                // }
                // if ($scheduled_rule['date_to']) {
                //     error_log("\nDate to: " . date('Ymd', strtotime($scheduled_rule['date_to'])), 3, WP_CONTENT_DIR . "/slider.log");
                // } else {
                //     error_log("\nDate to: not set", 3, WP_CONTENT_DIR . "/slider.log");
                // }

                // Skip if date is not within the range
                if (date('Ymd') < date('Ymd', strtotime($scheduled_rule['date_from'])) || date('Ymd') > date('Ymd', strtotime($scheduled_rule['date_to']))) {
                    continue;
                }

            } else if (isset($scheduled_rule['type']) && $scheduled_rule['type'] == 'week_days') {
                
                // Debug
                // error_log("\nRule for slide: " . $scheduled_rule['slide'], 3, WP_CONTENT_DIR . "/slider.log");
                // error_log("\nCurrent day: " . date('D'), 3, WP_CONTENT_DIR . "/slider.log");

                // Skip if week days are not set
                if (!$scheduled_rule['week_days']) {
                    continue;
                }

                // Skip if current day is not in the list of week days
                // foreach ($scheduled_rule['week_days'] as $week_day) {
                //     error_log("\nWeek day: " . $week_day, 3, WP_CONTENT_DIR . "/slider.log");
                // }
                if (!in_array(date('D'), $scheduled_rule['week_days'])) {
                    continue;
                }

            }

            // Check if time is within the range
            if (isset($scheduled_rule['whole_day']) && $scheduled_rule['whole_day'] != '1' && $scheduled_rule['time_from'] && $scheduled_rule['time_to']) {
                // error_log("\nCurrent time: " . date('H:i:s'), 3, WP_CONTENT_DIR . "/slider.log");
                // error_log("\nTime from: " . date('H:i:s', strtotime($scheduled_rule['time_from'])), 3, WP_CONTENT_DIR . "/slider.log");
                // error_log("\nTime to: " . date('H:i:s', strtotime($scheduled_rule['time_to'])), 3, WP_CONTENT_DIR . "/slider.log");
                if (date('H:i:s') < date('H:i:s', strtotime($scheduled_rule['time_from'])) || date('H:i:s') > date('H:i:s', strtotime($scheduled_rule['time_to']))) {
                    continue;
                }
            }
        }
        
        //error_log("\n---------", 3, WP_CONTENT_DIR . "/slider.log");

        // Save ID to include array
        $include[] = [
            'id' => $scheduled_rule['slide'],
            'priority' => $scheduled_rule['priority']
        ];
    }
}

/**
 * Remove custom slides from include array, and push it to its own array.
 * 
 * We do this because we need to get custom slides from database, and also
 * we need to calculate how many non-custom slides we need to get from Tickster.
 */
$custom_slides = [];
foreach ($include as $key => $slide) {
    if (strpos($slide['id'], 'custom_') !== false) {
        unset($include[$key]);
        $custom_slides[] = [
            'id' => $slide['id'],
            'priority' => $slide['priority']
        ];
    }
}
$custom_slides_count = (!empty($custom_slides)) ? count($custom_slides) : 0;

/*
 * Into this array we will save all slides that we want to include in the slider.
 * The objects in this array will have final structure and we will use them to render the slider.
 */
$context['events'] = [];

/*
 * Get custom slides from database.
 */
$custom_slides_posts = [];
if ($custom_slides_count > 0) {
    $custom_slides_ids = [];
    foreach ($custom_slides as $key => $custom_slide) {
        $custom_slides_ids[] = str_replace('custom_', '', $custom_slide['id']);
    }
    $custom_slides_posts = get_posts([
        'numberposts'   => -1,
        'post_type'     => 'slides',
        'fields'        => 'ids',
        'post_status'   => 'publish',
        'post__in'      => $custom_slides_ids,
    ]);
}
foreach ($custom_slides_posts as $key => $custom_slides_post) {

    // Find matching priority by ID
    $slide_id = 'custom_' . $custom_slides_post;
    $priority = 100; // Default priority
    foreach ($custom_slides as $custom_slide) {
        if ($custom_slide['id'] === $slide_id) {
            $priority = intval($custom_slide['priority']);
            break;
        }
    }

    $context['events'][] = (object) [
        'type'                      => 'custom',
        'id'                        => $slide_id,
        'heading'                   => get_field('heading', $custom_slides_post),
        'heading_color'             => get_field('title_color', $custom_slides_post),
        'small_text'                => get_field('small_text', $custom_slides_post),
        'small_text_color'          => get_field('description_color', $custom_slides_post),
        'image'                     => get_field('image', $custom_slides_post),
        'image_tablet'              => get_field('image_tablet', $custom_slides_post),
        'image_mobile_large'        => get_field('image_mobile_large', $custom_slides_post),
        'image_mobile'              => get_field('image_mobile', $custom_slides_post),
        'video_link'                => get_field('video_link', $custom_slides_post),
        'video_link_webm'           => get_field('video_link_webm', $custom_slides_post),
        'slide_autoplay_delay'      => get_field('slide_autoplay_delay', $custom_slides_post),
        'text_position'             => get_field('text_position', $custom_slides_post),
        'button'                    => get_field('button', $custom_slides_post),
        'button2'                   => get_field('button2', $custom_slides_post),
        'button_background'         => get_field('button_background', $custom_slides_post),
        'button_text_color'         => get_field('button_text_color', $custom_slides_post),
        'button_border_hover_color' => get_field('button_border_hover_color', $custom_slides_post),
        'button_text_hover_color'   => get_field('button_text_hover_color', $custom_slides_post),
        'priority'                  => $priority,
        'add_overlay'               => get_field('add_overlay', $custom_slides_post),
        'overlay_opacity'           => (get_field('overlay_opacity', $custom_slides_post)) ? (int) get_field('overlay_opacity', $custom_slides_post) / 100 : 0,
        'small_text_font_size'      => (get_field('small_text_font_size', $custom_slides_post)) ? (int) get_field('small_text_font_size', $custom_slides_post) / 10 : 1,
        'heading_font_size'         => (get_field('heading_font_size', $custom_slides_post)) ? (int) get_field('heading_font_size', $custom_slides_post) / 10 : 1
    ];
}

/*
 * Calculate how many non-custom slides we need to get from Tickster.
 * 
 * Tickster slides are already sorted according to priority settings,
 * so we don't need to sort them again.
 */ 
$custom_slides_count = (!empty($custom_slides_posts)) ? count($custom_slides_posts) : 0;
// $number = ($custom_slides_count > 0) ? $number - $custom_slides_count : $number;
// error_log("\nNumber of Tickster events to get: " . $number, 3, WP_CONTENT_DIR . "/slider.log");

// Get Tickster events from database.
$include_ids = [];
$tickster_events = [];
// if($number > 0) {
    $include_ids = array_column($include, 'id');
    $tickster_events = Tickster_Events_Public::get_events($number, $include_ids);
    
    // Add debug logging
    // error_log("\nTickster events returned from database:", 3, WP_CONTENT_DIR . "/slider.log");
    // foreach ($tickster_events as $event) {
    //     error_log("\nID: {$event->id}, End date: {$event->event_end}", 3, WP_CONTENT_DIR . "/slider.log");
    // }
//}

foreach ($tickster_events as $key => $event) {

    // Add type to Tickster events.
    $tickster_events[$key]->type = 'tickster';

    // Debug expiration check
    // if (isset($event->id)) {
    //     error_log("\nChecking expiration for event {$event->id}:", 3, WP_CONTENT_DIR . "/slider.log");
    // } else {
    //     error_log("\nEvent ID: missing in database", 3, WP_CONTENT_DIR . "/slider.log");
    // }
    // if (isset($event->event_end)) {
    //     error_log("\nEvent end: " . $event->event_end, 3, WP_CONTENT_DIR . "/slider.log");
    // } else {
    //     error_log("\nEvent end: missing in database", 3, WP_CONTENT_DIR . "/slider.log");
    // }
    // error_log("\nCurrent time: " . time(), 3, WP_CONTENT_DIR . "/slider.log");
    
    // Remove expired events from Tickster events.
    date_default_timezone_set("Europe/Stockholm"); // we set this to ensure it expires on local time, not server time
    if (1699393233339 < strtotime($event->event_end) && strtotime($event->event_end) < time()) {
        unset($tickster_events[$key]);
    }

}

/*
 * Also remove expired Tickster events from scheduled rules in admin (ACF repeater in Theme Settings).
 * 
 * This is done because we don't want to show expired/invalid rules to administrators.
 * That would be confusing, and they would have to remove them manually. Also the select
 * field in the repeater would show wrong value.
 */
$expired_events = array_diff($include_ids, array_column($tickster_events, 'id'));
$expired_events = array_filter($expired_events, function($event) {
    return strpos($event, 'custom_') === false;
});
if ($scheduled_rules_rows) {
    $scheduled_rules_rows = array_filter($scheduled_rules_rows, function($rule) use ($expired_events) {
        return !in_array($rule['scheduled_slide'], $expired_events);
    });
    update_field('scheduled_priority_rules', $scheduled_rules_rows, 'option');
}

// Write priority to Tickster events, default is 100 if not set.
$priorities = array_column($include, 'priority', 'id');
foreach ($tickster_events as $key => $event) {
    $tickster_events[$key]->priority = $priorities[$event->id] ?? 100;
}

/*
 * Merge custom slides and Tickster events into one array.
 * 
 * We do this because we need to sort all slides according to priority settings
 * and do some other operations on all slides.
 */
$context['events'] = array_merge($context['events'], $tickster_events);

// Assign correct slug to event and cache images
$image_helper = new Timber\ImageHelper();
foreach ($context['events'] as $key => $event) {

    if ($event->type == 'tickster') {

        // Assign correct category slug to events)
        if (is_array($event->tags) && in_array('Event', $event->tags)) {
            $context['events'][$key]->local_url = '/event/' . $event->slug;
        } elseif (is_array($event->tags) && in_array('Racing', $event->tags)) {
            $context['events'][$key]->local_url = '/travtavlingar/' . $event->slug;
        }

        // Cache external images locally
        $context['events'][$key]->local_image_url = @$image_helper::resize($context['events'][$key]->image_url, 1920, 1080);
    }
}

// Debug logging before sort
// error_log("\n------------------", 3, WP_CONTENT_DIR . "/slider.log");
// error_log("\nBefore sort:", 3, WP_CONTENT_DIR . "/slider.log");
// foreach ($context['events'] as $event) {
//     error_log("\nID: {$event->id}, Priority: {$event->priority}", 3, WP_CONTENT_DIR . "/slider.log");
// }

/*
 * Finally sort all slides according to priority settings.
 */ 
usort($context['events'], function ($a, $b) {
    return $a->priority <=> $b->priority;
});

// Limit number of slides to show
if (!empty($context['events']) && count($context['events']) > $fields['number']) {
    $context['events'] = array_slice($context['events'], 0, $fields['number']);
}

// Debug logging after sort
// error_log("\n------------------", 3, WP_CONTENT_DIR . "/slider.log");
// error_log("\nAfter sort:", 3, WP_CONTENT_DIR . "/slider.log");
// foreach ($context['events'] as $event) {
//     error_log("\nID: {$event->id}, Priority: {$event->priority}", 3, WP_CONTENT_DIR . "/slider.log");
// }

// Add ACF fields to context
$fields = [
    'link1' => get_field('link1'),
    'link2' => get_field('link2'),
    'link3' => get_field('link3'),
];

// Add slider placeholders to context
$fields['image_placeholder'] = 'data:image/png;base64,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';
$fields['image_tablet_placeholder'] = 'data:image/png;base64,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';
$fields['image_mobile_large_placeholder'] = 'data:image/png;base64,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';
$fields['image_mobile_placeholder'] = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAMAAACuX0YVAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDcuMS1jMDAwIDc5LmRhYmFjYmIsIDIwMjEvMDQvMTQtMDA6Mzk6NDQgICAgICAgICI+IDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+IDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCAyMi41IChNYWNpbnRvc2gpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkMzRTFGNzdFREM4NjExRURCMDQwOTU4Rjk2MzEzODMwIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkMzRTFGNzdGREM4NjExRURCMDQwOTU4Rjk2MzEzODMwIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NEI5MkU1N0ZEQzg2MTFFREIwNDA5NThGOTYzMTM4MzAiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NEI5MkU1ODBEQzg2MTFFREIwNDA5NThGOTYzMTM4MzAiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6pXK5/AAAABlBMVEUAAAD///+l2Z/dAAAADklEQVR42mJgYGAACDAAAAQAAZsD6gkAAAAASUVORK5CYII=';

$context['fields'] = $fields;

$context['custom_settings_for_tickster_slides'] = get_field('custom_settings_for_tickster_slides', 'option');

Timber::render('templates/blocks/block-slider/block-slider.twig', $context);
