{#
/**
* Block Name: Libsyn Podcast List
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Libsyn Podcast List Block</strong>' } %}
    {% else %}

        {# Episodes #}
        {% if episodes is not empty %}
            <section class="block--libsyn-podcast-list" id="{{ block.id }}">
                <div class="container-fluid">

                    {# Newest episode #}
                    <section class="block--libsyn-podcast">
                        {% for episode in episodes|slice(0, 1) %}
                            {% include "./partials/podcast-episode.twig" with {'remove_border': true} %}
                        {% endfor %}
                    </section>
                            
                    {# Table #}
                    <table class="table podcast-list">
                        <tbody>

                            <tr>
                                <th>#</th>
                                <th>{{ __( 'Nästa avsnitt', 'solvalla' ) }}</th>
                                <th></th>
                            </tr>
                            
                            {% for episode in episodes|slice(1, fields.number) %}
                                <tr class="podcast-list__episode apply-first-link" data-episode-id="{{ episode.id }}">
                                    <td>
                                        {{ episode.episode_id }}
                                    </td>
                                    <td>
                                        <a class="link-cod-grey text-decoration-none" href="/podcast/{{ episode.slug }}">{{ episode.title }}</a>
                                    </td>
                                    <td class="text-end">
                                        <div class="link-styled link-styled--arrow-top">
                                            <span class="podcast-list__episode__link link-mine-shaft"></span>
                                        </div>
                                    </td>
                                </tr>
                                
                                {# Image #}
                                {% if episode.thumbnail_url is not empty %}
                                    <img class="podcast-list__episode__img img-fluid" width="748" height="421" data-episode-id="{{ episode.id }}" style="aspect-ratio: 16 / 9;object-fit: cover;" data-src="{{ episode.thumbnail_url|e('esc_url') }}" alt="{{ episode.title|e('html_attr') }}">
                                {% endif %}

                            {% endfor %}

                        </tbody>
                    </table>

                </div>
            </section>
        {% endif %}

    {% endif %}

{% endblock %}
