<?php

/**
 * Block Name: News
 */

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace('block_', '', $block['id']);
$context['is_preview'] = $is_preview;

// Add ACF fields to context.
$number = get_field('number');
$fields = [
    'number' => ($number && $number > 0) ? $number : 4,
    'which_event_winners' => get_field('which_event_winners'),
];
$context['fields'] = $fields;

// Get posts.
$posts = Timber::get_posts([
    'post_type' => 'winners',
    'numberposts' => $fields['number'],
    'tax_query' => [
        [
            'taxonomy' => 'which-event-winners',
            'field' => 'slug',
            'terms' => $fields['which_event_winners'] // Where term_id of Term 1 is "1".
        ]
    ]
]);
$context['posts'] = $posts;

// Add ACF fields to context.
foreach ($posts as $key => $post) {
    $context['posts'][$key]->thumbnail = get_the_post_thumbnail_url($post->ID, 'post-thumbnail');
}

Timber::render('templates/blocks/block-winners/block-winners.twig', $context);
