{#
/**
* Block Name: Google Maps
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Google Maps Block</strong>' } %}
    {% else %}
        
        {% if fields.latitude and fields.longitude %}
            <section class="block--google-maps" id="{{ block.id }}">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="block--google-maps__inner" id="google-maps_{{ block_id }}" style="{{ style|e('html_attr') }}" data-lat="{{ fields.latitude|e('html_attr') }}" data-lng="{{ fields.longitude|e('html_attr') }}" data-title="{{ fields.title|e('html_attr') }}" data-desc="{{ fields.description|e('html_attr') }}" data-info-opened="{{ fields.info_opened|e('html_attr') }}" data-zoom="{{ fields.zoom|e('html_attr') }}" data-markers="{{ fields.markers|e('html_attr') }}"></div>
                        </div>
                    </div>
                </div>
            </section>
            <script>
            function initMap() {
                const map_blocks = document.getElementsByClassName('block--google-maps__inner');

                for (const block of map_blocks) {

                    // Get data
                    const lat = parseFloat( block.dataset.lat ); // center of map
                    const lng = parseFloat( block.dataset.lng ); // center of map
                    const title = block.dataset.title;
                    const desc = block.dataset.desc;
                    const zoom_level = parseInt( block.dataset.zoom );
                    const info_opened = block.dataset.infoOpened;
                    const markers = JSON.parse(block.dataset.markers);
                    
                    var img = block.dataset.img;
                    var img_anchor_x = parseInt( block.dataset.img_anchor_x ); 
                    var img_anchor_y = parseInt( block.dataset.img_anchor_y ); 
                    if( ! img ) {
                        img = "map-pointer.png";
                    }
                    if( ! img_anchor_x ) {
                        img_anchor_x = 24;
                    }
                    if( ! img_anchor_y ) {
                        img_anchor_y = 48;
                    }

                    // Map
                    const map = new google.maps.Map( block, {
                        mapTypeId: 'roadmap',
                        center: new google.maps.LatLng( lat, lng ),
                        zoom: zoom_level,
                        zoomControl: true,
                        mapTypeControl: true,
                        scaleControl: false,
                        streetViewControl: false,
                        rotateControl: false,
                        fullscreenControl: true,
                        scrollwheel: false,
                        doubleclickzoom: false,
                        styles: [
                        {
                            "featureType": "poi",
                            "stylers": [
                                {"visibility":"off"}
                            ]
                        },
                        ]
                    });

                    // Info
                    const infowindow = new google.maps.InfoWindow();

                    // Markers
                    const image = {{ theme.path|json_encode() }} + "/assets/images/" + img;
                    let markerElement, markerIteration = 0;
                    for (const marker of markers) {
                        
                        markerElement = new google.maps.Marker({
                            position: new google.maps.LatLng(marker.latitude, marker.longitude),
                            map,
                            title: marker.title,
                            icon: {
                                url: image,
                                anchor: new google.maps.Point(img_anchor_x, img_anchor_y)
                            }
                        });
                        google.maps.event.addListener(markerElement, 'click', (function(markerElement) {
                            return function() {
                                infowindow.setContent('<div style="width: 200px;"><h4 style="margin-bottom:8px;">' + marker.title + '</h4>' + marker.description + '</div>');
                                infowindow.open(map, markerElement);
                            }
                        })(markerElement));
                        
                        if ( markerIteration === 0 && info_opened == true && title.length !== 0 || desc.length !== 0) {
                                infowindow.setContent('<div style="width: 200px;"><h4 style="margin-bottom:8px;">' + marker.title + '</h4>' + marker.description + '</div>');
                                infowindow.open(map, markerElement);
                        }
                        markerIteration++
                    }

                }
            }
            </script>
        {% endif %}

    {% endif %}

{% endblock %}
