<?php
/**
* Block Name: Google Maps
*/

$context = Timber::context();

$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;
$context['google_maps_key'] = $_ENV['GOOGLE_MAPS_KEY'];

// responsive settings
$settings = get_field( 'settings' );
$height = get_field( 'height' );
$width_ratio = get_field( 'width_ratio' );
$height_ratio = get_field( 'height_ratio' );
$info_opened = get_field( 'info_opened' );
$zoom_level = get_field( 'zoom' );

// calculate styles
$style = 'height: 0; position: relative; width: 100%; padding-top: 56.25%';
if( $settings && in_array( $settings, [ 'height', 'ratio' ] ) ) {
    if( $settings == 'height' && $height !== '' ) {
        $style = 'height: ' . $height . 'px';
    } elseif( $settings == 'ratio' && $width_ratio !== '' && $height_ratio !== '' ) {
        $padding_top = $height_ratio / $width_ratio * 100;
        $style = 'height: 0; position: relative; width: 100%; padding-top: ' . $padding_top . '%';
    }
}
$context['style'] = $style;

// add ACF fields to context
$fields = [
    'latitude'              => get_field( 'latitude' ),
    'longitude'             => get_field( 'longitude' ),
    'title'                 => get_field( 'title' ),
    'description'           => get_field( 'description' ),
    'info_opened'           => get_field( 'info_opened' ),
    'zoom'                  => get_field( 'zoom' ),
    'markers'               => json_encode(get_field('markers')),
];
$context['fields'] = $fields;

Timber::render( 'templates/blocks/block-google-maps/block-google-maps.twig', $context );
