{#
/**
* Block Name: Tickster Restaurants
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Tickster Restaurants Block</strong>' } %}
    {% else %}
        
        {# Packages #}
        {% if packages is not empty %}
            <section class="block--tickster-restaurants" id="{{ block.id }}">
                <div class="container-fluid">
                    <div class="article--event__packages">
                        {% for package in packages %}
                            {% include "./partials/restaurant.twig" %}
                        {% endfor %}
                    </div>
                </div>
            </section>
        {% endif %}

    {% endif %}

{% endblock %}
