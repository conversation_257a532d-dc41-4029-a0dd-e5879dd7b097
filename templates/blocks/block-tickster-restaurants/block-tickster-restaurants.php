<?php
/**
* Block Name: Tickster Restaurants
*/

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

// Get Tickster restaurants from database.
$context['packages'] = [];
$context['packages'] = Tickster_Events_Public::get_packages();

$image_helper = new Timber\ImageHelper();

// Get packages data.
if( $context['packages'] ) {
    foreach( $context['packages'] as $key => $package ) {
        $description = htmlspecialchars_decode( $package['description'] );
        
        // Get link elements from package description and save it to separate array.
        $dom = new DOMDocument();
        $dom->loadHTML( $description );
        $links = $dom->getElementsByTagName( 'a' );
        $context['packages'][$key]['menu_links'] = [];
        foreach( $links as $link ) {
            $context['packages'][$key]['menu_links'][] = [
                'href' => $link->getAttribute( 'href' ),
                'text' => $link->nodeValue
            ];

            // Remove link elements from package description.
            $link->parentNode->removeChild( $link );
        }

        // Save modified package description to context.
        $description = $dom->saveHTML();
        $context['packages'][$key]['description'] = $description;

        // Cache external images locally
        $context['packages'][$key]['local_image'] = @$image_helper::resize( $package['image'], 520 );

    }
}

Timber::render( 'templates/blocks/block-tickster-restaurants/block-tickster-restaurants.twig', $context );
