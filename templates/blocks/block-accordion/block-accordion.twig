{#
/**
* Block Name: Accordion
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Accordion Block</strong>' } %}
    {% else %}
        
        {% if fields.block %}
            <section class="block--accordion" id="{{ block.id }}">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="accordion" id="accordion_{{ block_id }}">
                                {% for field in fields.block %}
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading_{{ block_id }}_{{ loop.index }}">
                                            <button class="{{ ( fields.auto_expand == 'first' and loop.index == 1 ) or fields.auto_expand == 'all' ? 'accordion-button' : 'accordion-button collapsed' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse_{{ block_id }}_{{ loop.index }}" aria-expanded="{{ ( fields.auto_expand == 'first' and loop.index == 1 ) or fields.auto_expand == 'all' ? 'true' : 'false' }}" aria-controls="collapse_{{ block_id }}_{{ loop.index }}">
                                                {{ field.title|e('esc_html') }}
                                            </button>
                                        </h2>
                                        <div id="collapse_{{ block_id }}_{{ loop.index }}" class="{{ ( fields.auto_expand == 'first' and loop.index == 1 ) or fields.auto_expand == 'all' ? 'accordion-collapse show collapse' : 'accordion-collapse collapse' }}" aria-labelledby="heading_{{ block_id }}_{{ loop.index }}" {{ fields.one_block_mode == true ? 'data-bs-parent="#accordion_' ~ block_id ~ '"' : '' }}>
                                            <div class="accordion-body content-block">
                                                {{ field.content|e('wp_kses_post') }}
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        {% endif %}

    {% endif %}

{% endblock %}
