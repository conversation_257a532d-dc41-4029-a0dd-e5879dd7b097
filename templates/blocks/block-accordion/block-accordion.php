<?php
/**
* Block Name: Accordion
*/

$context = Timber::context();

$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

$fields = [
    'auto_expand'           => get_field( 'auto_expand' ),
    'one_block_mode'        => get_field( 'one_block_mode' ),
    'block'                 => get_field( 'block' ),
];
$context['fields'] = $fields;

Timber::render( 'templates/blocks/block-accordion/block-accordion.twig', $context );
