<?php
/**
* Block Name: Reports
*/

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

// Add ACF fields to context.
$number = get_field( 'number' );
$fields = [
    'number' => ( $number && $number > 0 ) ? $number : -1,
];
$context['fields'] = $fields;

// Get posts.
$posts = Timber::get_posts([
    'post_type'     => 'reports',
    'numberposts'   => $fields['number'],
]);
$context['posts'] = $posts;

foreach( $context['posts'] as $key => $post ) {
    $pdf = get_field( 'pdf', $post->ID );
    $context['posts'][$key]->pdf = $pdf;
}

Timber::render( 'templates/blocks/block-reports/block-reports.twig', $context );
