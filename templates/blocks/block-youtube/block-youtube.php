<?php

/**
 * Block Name: YouTube
 */

$context = Timber::context();

$context['block'] = $block;
$context['is_preview'] = $is_preview;
$youtube_api = $_ENV['GOOGLE_YOUTUBE_API_KEY'];

// Add ACF fields to context.
$number = (get_field('skip_first_video')) ? get_field('number') + 1 : get_field('number');
$fields = [
    'number' => $number
];
$context['fields'] = $fields;

// Get YouTube videos from db.
$videos = get_youtube_videos($number, get_field('which-event'));
// print_r("<script>console.log(" . json_encode(get_field('which-event')) . ")</script>");

$image_helper = new Timber\ImageHelper();

// Cache images locally
if ($videos && is_array($videos) && !empty($videos)) {
    foreach ($videos as $key => $video) {
        if ($video['thumbnail_url'] && !empty($video['thumbnail_url'])) {
            // $videos[$key]['thumbnail'] = str_replace('hqdefault', 'sddefault', $video['thumbnail']);
            $videos[$key]['thumbnail_local'] = @$image_helper::resize($video['thumbnail_url'], 480, 270, 'center');
        } else {
            $videos[$key]['thumbnail_local'] = @$image_helper::resize(get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg', 480, 270, 'center');
        }
    }
    if (get_field('skip_first_video')) {
        array_shift($videos);
    }
}


$context = [
    'videos' => $videos
];

Timber::render('templates/blocks/block-youtube/block-youtube.twig', $context);
