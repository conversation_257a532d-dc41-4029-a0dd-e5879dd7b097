{#
/**
* Block Name: YouTube
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>YouTube Block</strong>' } %}
    {% else %}

        {% if videos %}
        <section class="block--youtube">
            <div class="container-fluid">
                <div class="row">
                    {% for video in videos %}

                        {# Column #}
                        <div class="col-xl-3 col-lg-4 col-md-6 d-flex align-items-stretch">

                            {# Card #}
                            <div class="card card--event border-0 rounded-0" data-magnific-gallery>

                                <a class="text-decoration-none" href="{{ video.video_url|e('esc_url') }}" data-magnific-gallery-type="iframe">
                                
                                    {# Image #}
                                    {% if video.thumbnail_local is not empty %}
                                        <div class="block--youtube__img card__img">
                                            <img class="img-fluid" width="480" height="270" style="aspect-ratio: 16 / 9;object-fit: cover;" data-src="{{ video.thumbnail_local }}" alt="{{ video.title|e('html_attr') }}">
                                            {% if video.live %}
                                                <small class="rounded-3 bg-white text-secondary text-uppercase">{{ __( 'Live', 'solvalla' ) }}</small>
                                            {% endif %}
                                        </div>
                                    {% endif %}

                                    {# Card body #}
                                    <div class="card-body px-0">

                                        {# Title #}
                                        <h5 class="card-title mb-0 text-cod-grey text-uppercase">
                                            {{ video.title|e('wp_kses_post') }}
                                        </h5>

                                        {# Description #}
                                        {# <div class="card-description text-scarpa-flow">
                                            {% if video.description is not empty %}
                                                <p>{{ video.description|striptags }}</p>
                                            {% endif %}
                                        </div> #}

                                    </div>

                                </a>

                            </div>

                        </div>

                    {% endfor %}
                </div>
            </div>
        </section>
        {% endif %}

    {% endif %}

{% endblock %}
