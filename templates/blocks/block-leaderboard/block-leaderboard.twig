{#
/**
* Block Name: Leaderboard Block
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {# {% if is_preview and fields.title is empty %} #}
    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Leaderboard Block</strong>' } %}
    {% else %}

    <section class="block--leaderboard" id="{{ block.id }}">
        <div class="container-fluid">
            <div class="table-container">
                {% if fields.text %}
                    <h1>{{ fields.title }}</h1>
                {% endif %}
                
                {% if fields.text %}
                    {{ fields.text }}
                {% endif %}

                <div class="filters">
                    {% if fields.image %}
                        <img src="{{ fields.image }}" alt="Solvalaserien">
                    {% endif %}
                    <div class="selects">
                        <select class="year-select" id="year-select">
                            <option value="" disabled>{{ __( 'Filtrera efter år', 'solvalla' ) }}</option>
                            {% set currentYear = "now"|date("Y") %}
                            {% for year in range(currentYear, 2023) %}
                                <option value="{{ year }}" {% if year == currentYear %}selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                        <select class="meeting-select" id="meeting-select">
                            <option value="" disabled selected>{{ __( 'Filtrera efter möte', 'solvalla' ) }}</option>
                        </select>
                    </div>
                    <div class="divisions">
                        <button data-division="2446" class="js-divisions-serien divisions-serien serien-150 active">{{ __( 'Solvallaserien 220', 'solvalla' ) }}</button>
                        <button data-division="2447" class="js-divisions-serien divisions-serien serien-350">{{ __( 'Solvallaserien 420', 'solvalla' ) }}</button>
                        <button data-division="2448" class="js-divisions-serien divisions-serien serien-650">{{ __( 'Solvallaserien 720', 'solvalla' ) }}</button>
                    </div>
                </div>
                <div class="table-wrapper">
                    <table class="meeting">
                        <thead>
                            <tr>
                                <th class="table-position center hideSmall"><span class="label--big">{{ __( 'Plats', 'solvalla' ) }}</span></th>
                                <th class="table-horse"><span class="label--big">{{ __( 'Häst', 'solvalla' ) }}</span></th>
                                <th class="table-points center"><span class="label--big">{{ __( 'Poäng', 'solvalla' ) }}</span></th>
                                <th class="table-starts center"><span class="label--big">{{ __( 'Starter', 'solvalla' ) }}</span></th>
                                <th class="table-trainer hideSmall"><span class="label--big">{{ __( 'Tränare', 'solvalla' ) }}</span></th>
                            </tr>
                        </thead>
                        <tbody id="meet-table"></tbody>
                            <!-- <tr class="expandable odd">
                                <td colspan="7">
                                    <div class="row mb-5">
                                        <div class="col text-center"><i class="icon-loader icon--large spin"></i></div>
                                    </div>
                                </td>
                            </tr> -->
                    </table>
                </div>
                <h2 class="js-error-meetings">{{ __( 'har inte startat ännu', 'solvalla' ) }}</h2>
            </div>
        </div>
    </section>

    {% endif %}

{% endblock %}
