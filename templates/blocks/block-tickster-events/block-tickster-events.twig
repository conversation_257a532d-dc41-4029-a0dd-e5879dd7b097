{#
/**
* Block Name: Tickster Events
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Tickster Events Block</strong>' } %}
    {% else %}
        
        {% if events is not empty and fields.number > 0 %}
            <section class="block--tickster-events" id="{{ block.id }}">
                <div class="container-fluid">
                    <div class="row">
                        {% for event in events|slice( 0, fields.number ) %}
                            {% include "./partials/card-event.twig" %}
                        {% endfor %}
                    </div>
                </div>
            </section>
        {% endif %}

    {% endif %}

{% endblock %}
