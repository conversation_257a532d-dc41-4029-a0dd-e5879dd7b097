<?php
/**
* Block Name: Tickster Events
*/

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

// Add ACF fields to context.
$fields = [
    'number' => get_field( 'number' ),
];
$context['fields'] = $fields;

// Get Tickster events from database.
$context['events'] = [];
$context['events'] = Tickster_Events_Public::get_events();

$image_helper = new Timber\ImageHelper();

foreach( $context['events'] as $key => $event ) {
    
    // Assign correct category slug to events
    if( is_array( $event->tags ) && in_array( 'Event', $event->tags ) ) {
        $context['events'][$key]->local_url = '/event/' . $event->slug;
    } elseif( is_array( $event->tags ) && in_array( 'Racing', $event->tags ) ) {
        $context['events'][$key]->local_url = '/travtavlingar/' . $event->slug;
    }

    // Cache external images locally
    $context['events'][$key]->local_thumbnail_url = @$image_helper::resize( $context['events'][$key]->image_url, 736, 414 );

    // Trim description to 280 characters
    $text_helper = new Timber\TextHelper();
    $context['events'][$key]->event_description = $text_helper::trim_characters( $context['events'][$key]->event_description, 280, '...' );
    
    // Check for link replacements in options
    $context['events'][$key]->link_replacement = false;
    // Get option value from ACF
    $tickster_replacements = get_field('tickster_replace_event_details', 'option');
    
    if( is_array($tickster_replacements) && !empty($tickster_replacements) ) {
        foreach( $tickster_replacements as $replacement ) {
            if( isset( $replacement['which_event'] ) && $replacement['which_event'] == $event->id && !empty( $replacement['las_mer_link'] ) ) {
                $context['events'][$key]->link_replacement = $replacement['las_mer_link'];
                break;
            }
        }
    }
}

Timber::render( 'templates/blocks/block-tickster-events/block-tickster-events.twig', $context );
