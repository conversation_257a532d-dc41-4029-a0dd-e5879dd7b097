<?php
/**
* Block Name: Trainers
*/

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

// Get posts.
$posts = Timber::get_posts([
    'post_type'     => 'trainers',
    'numberposts'   => -1,
    'orderby'       => 'title',
    'order'         => 'ASC',
]);
$context['posts'] = $posts;

// Add ACF fields to context.
foreach( $posts as $key => $post ) {
    $context['posts'][$key]->featured_image = get_field( 'featured_image', $post->ID );
}

Timber::render( 'templates/blocks/block-trainers/block-trainers.twig', $context );
