<?php
/**
* Block Name: Program
*/

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace( 'block_', '', $block['id'] );
$context['is_preview'] = $is_preview;

// Add ACF fields to context.
$number = get_field( 'number' );
$fields = [
    'number' => ( $number && $number > 0 ) ? $number : -1,
];
$context['fields'] = $fields;

// Get posts.
$posts = Timber::get_posts([
    'post_type'     => 'programs',
    'numberposts'   => $fields['number'],
    'meta_key'      => 'date',
    'orderby'       => 'meta_value',
    'order'         => 'DESC',
    // 'meta_query'    => [
    //     [
    //         'key'       => 'date',
    //         'value'     => date( 'Ymd' ),
    //         'compare'   => '>=',
    //         'type'      => 'DATE',
    //     ],
    // ],
]);
$context['posts'] = $posts;

foreach( $context['posts'] as $key => $post ) {
    $pdf = get_field( 'pdf', $post->ID );
    if( $pdf ) {
        $context['posts'][$key]->pdf = $pdf;
        $pdf_thumbnail = str_replace( '.pdf', '-pdf.jpg', $pdf['url'] );
        $context['posts'][$key]->pdf_thumbnail = $pdf_thumbnail;
    }
    $custom_date = get_field( 'date', $post->ID );
    if( $custom_date ) {
        $context['posts'][$key]->custom_date = $custom_date;
    }
}

Timber::render( 'templates/blocks/block-program/block-program.twig', $context );
