<?php

/**
 * Block Name: YouTube
 */

$context = Timber::context();

$context['block'] = $block;
$context['is_preview'] = $is_preview;
$youtube_api = $_ENV['GOOGLE_YOUTUBE_API_KEY'];

$videos = [];

$videos = get_youtube_videos(3);

if ($videos && is_array($videos) && !empty($videos)) {
    foreach ($videos as $key => $video) {
        $video['video_id'] = '0';
        parse_url($video['video_url']);
        if (isset($parsedUrl['query'])) {
            // Parse the query string to get the parameters
            parse_str($parsedUrl['query'], $queryParams);

            // Check if the 'v' parameter exists
            if (isset($queryParams['v'])) {
                $video['video_id'] = $queryParams['v'];
            }
        }
    }

    $context = [
        'video' => $videos[0]
    ];
}

Timber::render('templates/blocks/block-youtube-live/block-youtube-live.twig', $context);
