{#
/**
* Block Name: Block Faq - using block-faq.php
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {# {% if is_preview and fields.title is empty %} #}
    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Example Block</strong>' } %}
    {% else %}

    <section class="accordion hero-about-us faq">
        <div class="container-fluid">
            <div class="row faq__row">
                <div class="col-lg-12">
                    <h3 class="faq__title">{{ fields.faq_title }}</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane" role="tabpanel">
                            <div class="accordion accordion-flush" id="accordion">
                                {% for category in fields.faq %}
                                    <div class="faq__category">
                                        {% set randomNumber = random() %}
                                        <div class="accordion-item">
                                            <div class="accordion-header" id="flush-heading{{ randomNumber }}">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-{{ randomNumber }}" aria-expanded="false" aria-controls="collapse{{ randomNumber }}">
                                                    {{ category['question'] }}
                                                </button>
                                            </div>
                                            <div id="flush-{{ randomNumber }}" class="accordion-collapse collapse" aria-labelledby="flush-heading{{ randomNumber }}" data-bs-parent="#accordion">
                                                <div class="accordion-body">
                                                    {{ category['answer'] }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    {% endif %}

{% endblock %}
