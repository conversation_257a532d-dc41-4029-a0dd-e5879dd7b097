<?php

/**
 * Block Name: News
 */

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace('block_', '', $block['id']);
$context['is_preview'] = $is_preview;

// Add ACF fields to context.
$number = get_field('number');
$fields = [
    'number' => ($number && $number > 0) ? $number : 8,
    'which_event_invited_horses' => get_field('which_event_invited_horses'),
];
$context['fields'] = $fields;

// Get posts.
$posts = Timber::get_posts([
    'post_type' => 'invited-horses', // invited-horses
    'numberposts' => $fields['number'],
    'tax_query' => [
        [
            'taxonomy' => 'which-event-invited-horses',
            'field' => 'slug',
            'terms' => $fields['which_event_invited_horses'] // Where term_id of Term 1 is "1".
        ]
    ]
]);
$context['posts'] = $posts;

// Add ACF fields to context.
foreach ($context['posts'] as $key => $post) {
    $context['posts'][$key]->thumbnail_crop = get_field('thumbnail', $post->ID);
}
foreach ($context['posts'] as $key => $post) {
    $context['posts'][$key]->fields = [];
    foreach (get_field_objects($post->ID) as $field_key => $field_array) {
        foreach ($field_array as $field_param_name => $field_param_value) {
            if ($field_param_name == 'label' || $field_param_name == 'value' || $field_param_name == 'name') {
                $context['posts'][$key]->fields[$field_key][$field_param_name] = $field_param_value;
            }
        }
    }
    // print_r("<script>console.log(" . json_encode($context['posts']) . ")</script>");
}
// print_r("<script>console.log(" . json_encode($context['posts']) . ")</script>");

Timber::render('templates/blocks/block-invited-horses/block-invited-horses.twig', $context);
