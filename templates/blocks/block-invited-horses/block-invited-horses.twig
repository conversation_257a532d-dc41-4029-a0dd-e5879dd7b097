{#
/**
* Block Name: News
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Invited Horses Block</strong>' } %}
    {% else %}
        
        {% if posts is not empty %}
            <section class="block--invited-horses content-block" id="{{ post.id }}">
                <div class="container-fluid">
                    <div class="row">
                        {% for post in posts %}
                            <div class="col-xl-3 col-lg-4 col-md-6">

                                {# Card #}
                                <div class="card card--invited-horses border-0 rounded-0 apply-first-link" style="margin-bottom: 0">

                                    {# Solvalla placeholder #}
                                    {% if site.id == solvalla.id %}
                                        {% set placeholder = Image( site.theme.link ~ '/assets/images/placeholder.jpg' ) | relative %}
                                    {# Elitloppet placeholder #}
                                    {% elseif site.id == elitloppet.id %}
                                        {% set placeholder = Image( site.theme.link ~ '/assets/images/elitloppet-placeholder.jpg' ) | relative %}
                                    {% endif %}

                                    {# Featured image #}
                                    <a class="card__img d-block text-decoration-none disable-auto-translation" href="{{ post.link|e('esc_url') }}">
                                    {# post.thumbnail_crop ? #}
                                        {% if fields.detail_image or post.thumbnail %}
                                            {% if fields.detail_image %}
                                                <img width="{{ fields.detail_image.width }}" height="{{ fields.detail_image.height }}" class="img-fluid" style="aspect-ratio: 16 / 9;object-fit: cover;" data-src="{{ fields.detail_image.url }}" alt="{{ post.title|striptags|e('html_attr') }}">
                                            {% else %}
                                                <img class="img-fluid" width="736" height="429" style="aspect-ratio: 16 / 9;object-fit: cover;" data-src="{{ post.thumbnail.src|tojpg|resize(736, 429, 'center')|e('esc_url') }}" alt="{{ post.title|striptags|e('html_attr') }}">
                                            {% endif %}
                                        {% endif %}
                                    </a>

                                    {# Card body #}
                                    <div class="card-body px-0">

                                        {# Title #}
                                        <h2 class="card-title disable-auto-translation">
                                            <a class="text-decoration-none text-cod-grey text-uppercase" href="{{ post.link|e('esc_url') }}">
                                                {{ post.title|e('wp_kses_post') }}
                                            </a>
                                        </h2>

                                        <div class="invited-horse-info-box" style="margin-block: .25rem">
                                            <ul style="grid-template-columns: 1fr !important">
                                                <li><span style="font-weight: 700" class="disable-auto-translation">{{ post.fields.trainer.label|e }}</span> {{ post.fields.trainer.value|e }}</li>
                                                <li><span style="font-weight: 700">{{ post.fields.nationality.label|e }}</span> {{ post.fields.nationality.value|e }}</li>
                                                {# {% for field in post.fields %}
                                                    <li><span style="font-weight: 700">{{ field.label|e }}</span> {{ field.value|e }}</li>
                                                {% endfor %} #}
                                            </ul>
                                        </div>

                                        
                                        {# Read more #}
                                        <div class="wp-block-button btn btn-secondary btn--dot">
                                            <a class="wp-block-button__link has-white-color has-text-color wp-element-button" href="{{ post.link|e('esc_url') }}" rel="noreferrer noopener">{{ __( 'Läs mer', 'solvalla' ) }}</a>
                                        </div>

                                        {# up/down sliding buttonlink #}
                                        {# <div class="link-styled link-styled--dot link-styled--razzmatazz link-styled--padding text-decoration-none text-uppercase text-razzmatazz d-flex apply-first-link">
                                            <a href="{{ post.link|e('esc_url') }}" data-link-alt=" LÄS MER" class="d-flex link-slice-hover align-items-center">
                                            <span>{{ __( 'Läs mer', 'solvalla' ) }}</span>
                                            </a>
                                        </div> #}
                                        
                                    </div>

                                </div>

                            </div>
                        {% endfor %}
                    </div>                  
                </div>
            </section>
        {% endif %}

    {% endif %}

{% endblock %}
