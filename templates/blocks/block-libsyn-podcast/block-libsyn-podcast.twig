{#
/**
* Block Name: Libsyn Podcast
*/
#}

{% extends 'templates/blocks/base-block.twig' %}

    {% block blockContent %}

    {% if is_preview %}
        {% include 'templates/blocks/admin-message.twig' with { admin_message: 'Click here to edit <strong>Libsyn Podcast Block</strong>' } %}
    {% else %}

        {# Episodes #}
        {% if episodes is not empty %}
            <section class="block--libsyn-podcast" id="{{ block.id }}">
                <div class="container-fluid">
                    {% for episode in episodes %}
                        {% include "./partials/podcast-episode.twig" %}
                    {% endfor %}
                </div>
            </section>
        {% endif %}

        {# Load more #}
        <div class="pt-4 d-flex justify-content-center">
            <button class="btn btn--large btn--load-more btn-secondary" id="load-more" data-page="1" data-block-id="{{ block.id }}" data-block-name="{{ block.name }}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="#ddd"><path d="M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48 48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.491-48-48-48z"/></svg>
                <span>{{ __( 'Ladda mer', 'solvalla' ) }}</span>
            </button>
        </div>

    {% endif %}

{% endblock %}
