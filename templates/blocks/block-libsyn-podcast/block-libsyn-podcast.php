<?php

/**
 * Block Name: Libsyn Podcast
 */

$context = Timber::context();
$context['block'] = $block;
$context['block_id'] = str_replace('block_', '', $block['id']);
$context['is_preview'] = $is_preview;

// Get podcast episodes from database
if (class_exists('Libsyn_Podcast_Public')) {
    $context['episodes'] = Libsyn_Podcast_Public::get_episodes(6, 1);
} else {
    $context['episodes'] = [];
}

// Cache external images locally
foreach ($context['episodes'] as $key => $episode) {

    // Overwrite episode image if there is rule in options
    $image_changes = get_field('images_changes', 'option');
    foreach ($image_changes as $image_change) {
        $libsyn_episode_id = $image_change['libsyn_episode_id'];
        $libsyn_image_url = $image_change['libsyn_image_url'];
        if ($libsyn_episode_id && $libsyn_image_url && $episode && $episode->id == $libsyn_episode_id) {
            $episode->image_url = $libsyn_image_url;
        }
    }

    $image_helper = new Timber\ImageHelper();
    $image = $image_helper::img_to_jpg($episode->image_url);
    $context['episodes'][$key]->thumbnail_url = $image_helper::resize($image, 748);

    // Remove default placeholder
    $image_obj = new Timber\Image($context['episodes'][$key]->thumbnail_url);
    if ($image_obj->aspect() <= 1) {
        try {
            $image = $image_helper::img_to_jpg($episode->image_url, $bghex = '#FFFFFF', $force = true);
            $context['episodes'][$key]->thumbnail_url = $image_helper::resize($image, 748);
        } catch (\Throwable $th) {
            // echo 'Error: ' . $th->getMessage();
        }
        $context['episodes'][$key]->thumbnail_url = '';
    }
}

// Add ACF fields to context.
$fields = [
    'number' => get_field('number'),
];
$context['fields'] = $fields;

// Links for podcast
$context['links'] = [
    'Spotify' => get_field('spotify', 'option'),
    'Podbean' => get_field('podbean', 'option'),
    'Google Podcasts' => get_field('google_podcasts', 'option'),
    'Apple iTunes' => get_field('apple_itunes', 'option'),
];

Timber::render('templates/blocks/block-libsyn-podcast/block-libsyn-podcast.twig', $context);
