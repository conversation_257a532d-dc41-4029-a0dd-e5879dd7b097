{% extends "base.twig" %}

{% block content %}

    {# Top image #}
    {% if event.local_image_url is not empty %}
        <div class="top-image top-image-tickster active">

            <img width="1920" height="1080" class="top-image__desktop img-fluid" src="{{ event.local_image_url }}" alt="{{ event.event_name|e('html_attr') }}">
            <img width="991" height="800" class="top-image__tablet img-fluid" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA98AAAMgCAMAAAAN6SFOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDcuMS1jMDAwIDc5LmRhYmFjYmIsIDIwMjEvMDQvMTQtMDA6Mzk6NDQgICAgICAgICI+IDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+IDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCAyMi41IChNYWNpbnRvc2gpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjRCOTJFNTc5REM4NjExRURCMDQwOTU4Rjk2MzEzODMwIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjRCOTJFNTdBREM4NjExRURCMDQwOTU4Rjk2MzEzODMwIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NEI5MkU1NzdEQzg2MTFFREIwNDA5NThGOTYzMTM4MzAiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NEI5MkU1NzhEQzg2MTFFREIwNDA5NThGOTYzMTM4MzAiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7/h2eyAAAABlBMVEUAAAD///+l2Z/dAAADGUlEQVR42uzBAQEAAACCIP+vbkhAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAK8mwAActAABDv+yqgAAAABJRU5ErkJggg==" alt="{{ event.event_name|e('html_attr') }}">
            <img width="680" height="880" class="top-image__mobile_large img-fluid" src="data:image/png;base64,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" alt="{{ event.event_name|e('html_attr') }}">
            <img width="600" height="1200" class="top-image__mobile img-fluid" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAMAAACuX0YVAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDcuMS1jMDAwIDc5LmRhYmFjYmIsIDIwMjEvMDQvMTQtMDA6Mzk6NDQgICAgICAgICI+IDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+IDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCAyMi41IChNYWNpbnRvc2gpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkMzRTFGNzdFREM4NjExRURCMDQwOTU4Rjk2MzEzODMwIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkMzRTFGNzdGREM4NjExRURCMDQwOTU4Rjk2MzEzODMwIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NEI5MkU1N0ZEQzg2MTFFREIwNDA5NThGOTYzMTM4MzAiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NEI5MkU1ODBEQzg2MTFFREIwNDA5NThGOTYzMTM4MzAiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6pXK5/AAAABlBMVEUAAAD///+l2Z/dAAAADklEQVR42mJgYGAACDAAAAQAAZsD6gkAAAAASUVORK5CYII=" alt="{{ event.event_name|e('html_attr') }}">>

            <div class="top-image__content">
                <div class="container-fluid">

                    <div class="top-image__content__inner">

                        {# Breadcrumbs #}
						<nav aria-label="breadcrumb">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="{{ site.url }}">{{ __( 'Hem', 'solvalla' ) }}</a></li>
								<li class="breadcrumb-item"><a href="{{ calendar_url }}">{{ __( 'Evenemang', 'solvalla' ) }}</a></li>
								<li class="breadcrumb-item active" aria-current="page">{{ event.event_name|e('wp_kses_post') }} - {{ event.event_date|date("j")|e }} {{ event.event_date|date("F")|capitalize|e }} {{ event.event_date|date("Y")|e }}</li>
							</ol>
						</nav>

                        <h1 class="text-uppercase">
                            {{ event.event_name|e('wp_kses_post') }} - {{ event.event_date|date("j")|e }} {{ event.event_date|date("F")|capitalize|e }} {{ event.event_date|date("Y")|e }}
                        </h1>

                        <div class="top-image__content__meta top-image__content__meta--half content-block row">

                            {# Date #}
                            {% if event.event_date %}
                                <div class="col">
                                    <div>{{ __( 'Datum', 'solvalla' ) }}</div>
                                    <div>{{ event.event_date|date("j")|e }} {{ event.event_date|date("F")|capitalize|e }} - {{ event.event_date|date("H:i")|e }}</div>
                                </div>
                            {% endif %}

                            {# Opening time #}
                            {% if event.doors_open %}
                                <div class="col">
                                    <div>{{ __( 'Entreérna öppnar', 'solvalla' ) }}</div>
                                    <div>{{ __( 'Kl.', 'solvalla' ) }} {{ event.doors_open|date("H:i")|e }}</div>
                                </div>
                            {% endif %}
                            
                            {# Starting time #}
                            {% if event.event_date %}
                                <div class="col">
                                    <div>{{ __( 'Första start', 'solvalla' ) }}:</div>
                                    <div>{{ __( 'Kl.', 'solvalla' ) }} {{ event.event_date|date("H:i")|e }}</div>
                                </div>
                            {% endif %}

                            {# Price #}
                            {% if event.price %}
                                <div class="col">
                                    <div>{{ __( 'Priser', 'solvalla' ) }}</div>
                                    <div>{{ event.price }}</div>
                                </div>
                            {% endif %}

                        </div>

                        {# Buttons #}
                        <div class="d-flex gap-4 align-items-center">
                            
                            {# Buy tickets #}
                            {% if event.event_state == 'saleEnded' or event.event_state == 'SaleEnded' or 'fri entré' in event.event_description or 'Fri entré' in event.event_description %}
                                {# <div class="link-styled link-styled--ticket link-styled--tall-poppy">
                                    <span class="calendar__event__link link-white text-uppercase">
                                        {{ __( 'Slut på försäljning', 'solvalla' ) }}
                                    </span>
                                </div> #}
                            {% elseif event.stock_status in ['instock', 'fewleft', 'InStock', 'FewLeft'] %}
                                <a class="btn btn-white btn--large btn--ticket text-uppercase" href="{{ event.tickets_url|e('esc_url') }}" target="_blank">
                                    {{ __( 'Köp biljetter', 'solvalla' ) }}
                                </a>
                            {% else %}
                                <div class="link-styled link-styled--ticket link-styled--tall-poppy">
                                    <span class="calendar__event__link link-white text-uppercase">
                                        {{ __( 'Stängt för försäljning', 'solvalla' ) }}
                                    </span>
                                </div>
                            {% endif %}

                        </div>
                    
                    </div>  

                </div>  
            </div>  

        </div>
    {% endif %}

	<div class="container-fluid">
		<div class="row">
			<div class="col-12">
				<div class="content-wrapper">
					<article class="article article--event" id="event-{{ event_id }}">

                        <div class="article--event__meta row gx-custom">

                            <div class="col-12">

                                {# Title #}
                                <h2 class="article__title article--event__title text-uppercase">
                                    {{ event.event_name|e('wp_kses_post') }}<br>
                                </h2>
                                
                            </div>

                            <div class="col-xl-5 col-md-6 pe-sm-4 pe-xl-0">

                                {# Buttons - mobile #}
                                <div class="d-flex gap-4 align-items-center d-md-none">
                                    
                                    {# Buy tickets #}
                                    {% if event.event_state == 'saleEnded' or event.event_state == 'SaleEnded' or 'fri entré' in event.event_description or 'Fri entré' in event.event_description %}
                                        {# <div class="link-styled link-styled--ticket link-styled--tall-poppy">
                                            <span class="calendar__event__link link-mine-shaft text-uppercase">
                                                {{ __( 'Slut på försäljning', 'solvalla' ) }}
                                            </span>
                                        </div> #}
                                    {% elseif event.stock_status in ['instock', 'fewleft', 'InStock', 'FewLeft'] %}
                                        <a class="btn btn-secondary btn--ticket text-uppercase" href="{{ event.tickets_url|e('esc_url') }}" target="_blank">
                                            {{ __( 'Köp biljetter', 'solvalla' ) }}
                                        </a>
                                    {% else %}
                                        <div class="link-styled link-styled--ticket link-styled--tall-poppy">
                                            <span class="calendar__event__link link-mine-shaft text-uppercase">
                                                {{ __( 'Stängt för försäljning', 'solvalla' ) }}
                                            </span>
                                        </div>
                                    {% endif %}

                                    {# Travprogram #}
                                    {% if event.links is not empty %}
                                        {% for link in event.links %}
                                            {% if link.text == 'Travprogram' %}
                                                <div class="link-styled link-styled--program link-styled--chathams-blue">
                                                    <a class="calendar__event__link link-mine-shaft" href="{{ link.url|e('esc_url') }}" target="_blank">
                                                        {{ link.text }}
                                                    </a>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}

                                </div>

                                {# Table #}
                                <table class="table article--event__table">
                                    <tbody>

                                        {# Date #}
                                        {% if event.event_date %}
                                            <tr>
                                                <th scope="row">{{ __( 'Datum', 'solvalla' ) }}:</th>
                                                <td>{{ event.event_date|date("j")|e }} {{ event.event_date|date("F")|capitalize|e }} {{ event.event_date|date("Y")|e }}</td>
                                            </tr>
                                        {% endif %}
                                        
                                        {# Opening time #}
                                        {% if event.doors_open %}
                                            <tr>
                                                <th scope="row">{{ __( 'Entreérna öppnar', 'solvalla' ) }}:</th>
                                                <td>{{ __( 'Kl.', 'solvalla' ) }} {{ event.doors_open|date("H:i")|e }}</td>
                                            </tr>
                                        {% endif %}

                                        {# Starting time #}
                                        {% if event.event_date %}
                                            <tr>
                                                <th scope="row">{{ __( 'Första start', 'solvalla' ) }}:</th>
                                                <td>{{ __( 'Kl.', 'solvalla' ) }} {{ event.event_date|date("H:i")|e }}</td>
                                            </tr>
                                        {% endif %}

                                        {# Price #}
                                        {% if event.price %}
                                            <tr>
                                                <th scope="row">{{ __( 'Priser', 'solvalla' ) }}:</th>
                                                <td>{{ event.price }}</td>
                                            </tr>
                                        {% endif %}

                                    </tbody>
                                </table>
                            
                            </div>

                            <div class="col-md-6 offset-xl-1 ps-md-4 ps-lg-5 ps-xl-0">

                                {# Excerpt #}
                                {% if event.event_description is not empty %}
                                <div class="article__excerpt article--event__excerpt fw-bold">
                                    <p>{{ event.event_description|striptags }}</p>
                                </div>
                                {% endif %}

                                {# Buttons - desktop #}
                                <div class="d-none gap-4 align-items-center d-md-flex">
                                    
                                    {# Buy tickets #}
                                    {% if event.event_state == 'saleEnded' or event.event_state == 'SaleEnded' or 'fri entré' in event.event_description or 'Fri entré' in event.event_description %}
                                        {# <div class="link-styled link-styled--ticket link-styled--tall-poppy">
                                            <span class="calendar__event__link link-mine-shaft text-uppercase">
                                                {{ __( 'Slut på försäljning', 'solvalla' ) }}
                                            </span>
                                        </div> #}
                                    {% elseif event.stock_status in ['instock', 'fewleft', 'InStock', 'FewLeft'] %}
                                        <a class="btn btn-secondary btn--ticket text-uppercase" href="{{ event.tickets_url|e('esc_url') }}" target="_blank">
                                            {{ __( 'Köp biljetter', 'solvalla' ) }}
                                        </a>
                                    {% else %}
                                        <div class="link-styled link-styled--ticket link-styled--tall-poppy">
                                            <span class="calendar__event__link link-mine-shaft text-uppercase">
                                                {{ __( 'Stängt för försäljning', 'solvalla' ) }}
                                            </span>
                                        </div>
                                    {% endif %}

                                    {# Travprogram #}
                                    {% if event.links is not empty %}
                                        {% for link in event.links %}
                                            {% if link.text == 'Travprogram' %}
                                                <div class="link-styled link-styled--program link-styled--chathams-blue">
                                                    <a class="calendar__event__link link-mine-shaft" href="{{ link.url|e('esc_url') }}" target="_blank">
                                                        {{ link.text }}
                                                    </a>
                                                </div>
                                            {% endif %}
                                            {# {% if 'Meny' in link.text %}
                                                <div class="link-styled link-styled--program link-styled--chathams-blue">
                                                    <a class="calendar__event__link link-mine-shaft" href="{{ link.uri|e('esc_url') }}" target="_blank">
                                                        {{ link.text }}
                                                    </a>
                                                </div>
                                            {% endif %} #}
                                        {% endfor %}
                                    {% endif %}

                                </div>

                            </div>
                        
                        </div>

					</article>
				</div>
			</div>
		</div>
	</div>

    {# Content #}
    {% if blocks is not empty %}
        {% for post_block in blocks %}
            {{ function( 'render_block', post_block ) }}
        {% endfor %}
    {% endif %}

    {# Packages #}
    <div class="container-fluid">
		<div class="row">
			<div class="col-12">
				<div class="content-wrapper">
					<div class="article article--event" id="event-{{ event_id }}">
                        {% if event.packages is not empty %}
                            <div class="article--event__packages d-flex flex-column">
                                
                                {% set packages_exist = false %}
                                {% for package in event.packages %}
                                    {% if package.title in allowed_packages %}
                                        {% set packages_exist = true %}
                                        {% include "./partials/restaurant.twig" %}
                                    {% endif %}
                                {% endfor %}

                                {% if packages_exist %}
                                    <h2 class="article--event__subtitle text-uppercase order-1">{{ __( 'Mat & Dryck', 'solvalla' ) }}</h2>
                                {% endif %}

                            </div>
                        {% endif %}
                    </div>
				</div>
			</div>
		</div>
	</div>



{% endblock %}
