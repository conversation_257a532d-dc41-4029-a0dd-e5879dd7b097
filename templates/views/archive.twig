{% extends "base.twig" %}

{% block content %}


    {# Page title #}
    <div class="page__title--archive">
        <div class="container-fluid">
            <div class="row">
                
                {# Breadcrumbs #}
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ site.url }}">{{ __( 'Hem', 'solvalla' ) }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
                    </ol>
                </nav>

                {# Title #}
                <h1 class="text-uppercase">{{ title }}</h1>

            </div>
        </div>
    </div>

    {# News #}
    {% if posts is not empty %}
        <div class="container-fluid">
            <div class="row">
                {% for post in posts %}
                    {% include "./partials/card-news.twig" %}
                {% endfor %}
            </div>
        </div>
    {% endif %}

	{# Pagination #}
    {% if posts.pagination.pages %}
    <div class="page-pagination">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="Page navigation">
                        <ul class="pagination pagination-lg justify-content-center flex-wrap">
                            {% if posts.pagination.prev %}
                                <li class="page-item fw-medium">
                                    <a href="{{ posts.pagination.prev.link }}" class="page-link page-link--prev {{ posts.pagination.prev.link|length ? '' : 'disabled' }}">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                            {% for page in posts.pagination.pages %}
                                {% if page.link %}
                                <li class="page-item fw-medium">
                                    <a href="{{ page.link }}" class="page-link {{ page.class }}">{{ page.title }}</a>
                                </li>
                                {% else %}
                                <li class="page-item fw-medium active" aria-current="page">
                                    <a href="{{ page.link }}" class="page-link {{ page.class }}">{{ page.title }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            {% if posts.pagination.next %}
                                <li class="page-item fw-medium">
                                    <a href="{{ posts.pagination.next.link }}" class="page-link page-link--next {{ posts.pagination.next.link|length ? '' : 'disabled' }}">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {# Flexible spacer #}
    <div aria-hidden="true" class="wp-block-fsb-flexible-spacer fsb-flexible-spacer">
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--lg" style="height:120px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--md" style="height:80px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--sm" style="height:30px"></div>
    </div>

    {# Banners #}
    {% filter shortcodes %}
        [random_cpt_post taxonomy="groupped" term_id="12"]
    {% endfilter %}
    
{% endblock %}