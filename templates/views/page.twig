{% extends "base.twig" %}

{% block content %}

  {% if has_slider == false %}
  
    {# Page title #}
    <div class="container-fluid page__title">
      <div class="row">
        <div class="col-12">

          {# Breadcrumbs #}
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="{{ site.url }}">{{ __( 'Hem', 'solvalla' ) }}</a></li>
              {% for breadcrumb_page in breadcrumb_pages %}
                <li class="breadcrumb-item"><a href="{{ breadcrumb_page.link }}">{{ breadcrumb_page.title|e('wp_kses_post') }}</a></li>
              {% endfor %}
              <li class="breadcrumb-item active" aria-current="page">{{ post.title|e('wp_kses_post') }}</li>
            </ol>
          </nav>

          {# Title #}
          <h1 class="display-1" style="text-transform:uppercase">{{ post.title|e('wp_kses_post') }}</h1>

        </div>
      </div>
    </div>

    {% if post.thumbnail %}

      {# Featured image #}
      {% if fields.desktop_image or post.thumbnail %}
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            {% if fields.desktop_image and fields.mobile_image %}
              <img width="1760" height="310" class="page__img img-fluid w-100 d-none d-md-block" data-src="{{ fields.desktop_image.url }}" alt="{{ post.title|e('html_attr') }}">
              <img width="736" height="368" class="page__img img-fluid w-100 d-block d-md-none" data-src="{{ fields.mobile_image.url }}" alt="{{ post.title|e('html_attr') }}">
            {% elseif fields.desktop_image %}
              <img width="1760" height="310" class="page__img img-fluid w-100" data-src="{{ fields.desktop_image.url }}" alt="{{ post.title|e('html_attr') }}">
            {% elseif post.thumbnail and fields.mobile_image %}  
              <img width="{{ post.thumbnail.width }}" height="{{ post.thumbnail.height }}" class="page__img img-fluid w-100 d-none d-md-block" data-src="{{ post.thumbnail.src }}" srcset="{{ post.thumbnail.srcset }}" alt="{{ post.title|e('html_attr') }}">
              <img width="736" height="368" class="page__img img-fluid w-100 d-block d-md-none" data-src="{{ fields.mobile_image.url }}" alt="{{ post.title|e('html_attr') }}">
            {% elseif post.thumbnail %}
              <img width="{{ post.thumbnail.width }}" height="{{ post.thumbnail.height }}" class="page__img img-fluid w-100" data-src="{{ post.thumbnail.src }}" srcset="{{ post.thumbnail.srcset }}" alt="{{ post.title|e('html_attr') }}">
            {% endif %}
          </div>
        </div>
      </div>
      {% endif %}

    {% else %}

      {# Separator #}
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <hr class="wp-block-separator has-alpha-channel-opacity">
          </div>
        </div>
      </div>

      {# Flexible spacer #}
      <div aria-hidden="true" class="wp-block-fsb-flexible-spacer fsb-flexible-spacer">
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--lg" style="height:40px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--md" style="height:20px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--sm" style="height:10px"></div>
      </div>

    {% endif %}

  {% endif %}

  {{ post.content }}

  {# Flexible spacer #}
  <div aria-hidden="true" class="wp-block-fsb-flexible-spacer fsb-flexible-spacer">
    <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--lg" style="height:160px"></div>
    <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--md" style="height:100px"></div>
    <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--sm" style="height:30px"></div>
  </div>

  {# Banners #}
  {% filter shortcodes %}
    [random_cpt_post taxonomy="groupped" term_id="12"]
  {% endfilter %}

{% endblock %}
