{% extends "base.twig" %}

{% block content %}

	{# Page title #}
    <div class="page__title"></div>

	{# Article #}
    <article class="article article--trainer" id="post-{{ post.ID }}">

		{# Article header #}
        <div class="container-fluid">
			<div class="row">

                <div class="article--trainer__img col-lg-4">

                    {# Featured image #}
                    {% if fields.featured_image %}
                        <img class="img-fluid" width="{{ fields.featured_image.width }}" height="{{ fields.featured_image.height }}" data-src="{{ fields.featured_image.url }}" alt="{{ post.title|e('html_attr') }}">
                    {% else %}
				        <img class="img-fluid" width="736" height="521" data-src="{{ post.thumbnail.src|tojpg|resize( 736, 521 ) }}" alt="{{ post.title|e('html_attr') }}">
                    {% endif %}

                </div>

                <div class="article--trainer__content col-lg-8 mt-4 mt-lg-0">

                    {# Breadcrumbs #}
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ site.url }}">{{ __( 'Hem', 'solvalla' ) }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ archive_url }}">{{ archive_title }}</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ post.title|e('wp_kses_post') }}</li>
                        </ol>
                    </nav>

                    {# Title #}
                    <h1 class="article__title text-uppercase">{{ post.title }}</h1>

                    {# Separator #}
                    <hr class="wp-block-separator has-text-color has-alpha-channel-opacity has-background is-style-default" style="background-color:#dddddd;color:#dddddd">

                    {# Contact Details #}
                    <div class="article--trainer__meta d-flex flex-wrap">
                        {% if fields.address %}
                            <div class="me-5">
                                <span class="text-mine-shaft">{{ __( 'Adress', 'solvalla' ) }}:</span> {{ fields.address }}
                            </div>
                        {% endif %}
                        {% if fields.mobile %}
                            <div class="me-5">
                                <span class="text-mine-shaft">{{ __( 'Mobil', 'solvalla' ) }}:</span> <a class="text-decoration-none link-cod-grey" href="tel:{{ fields.mobile|replace({' ': ''}) }}">{{ fields.mobile }}</a>
                            </div>
                        {% endif %}
                        {% if fields.phone %}
                            <div class="me-5">
                                <span class="text-mine-shaft">{{ __( 'Telefon', 'solvalla' ) }}:</span> <a class="text-decoration-none link-cod-grey" href="tel:{{ fields.phone|replace({' ': ''}) }}">{{ fields.phone }}</a>
                            </div>
                        {% endif %}
                        {% if fields.email %}
                            <div class="me-5">
                                <span class="text-mine-shaft">{{ __( 'Mail', 'solvalla' ) }}:</span> <a class="text-decoration-none link-cod-grey" href="mailto:{{ fields.email|replace({' ': ''}) }}">{{ fields.email }}</a>
                            </div>
                        {% endif %}
                        {% if fields.website %}
                            <div class="me-5">
                                <span class="text-mine-shaft">{{ __( 'Webb', 'solvalla' ) }}:</span> <a class="text-decoration-none link-cod-grey" href="{{ fields.website }}" target="_blank">{{ fields.website }}</a>
                            </div>
                        {% endif %}
                    </div>

                    {# Content #}
                    <div class="article__body content-block has-medium-font-size">
                        {{ post.post_content|e('wp_kses_post') }}
                    </div>

                    {# Statistics #}
                    {% if fields.statistics %}
                        <a class="btn btn-secondary btn--dot" href="{{ fields.statistics }}" target="_blank">
                            {{ __( 'Statistik (ST)', 'solvalla' ) }}
                        </a>
                    {% endif %}

                    {# Training list #}
                    {% if fields.training_list %}
                        <a class="btn btn-secondary btn--dot" href="{{ fields.training_list }}" target="_blank">
                            {{ __( 'Träningslista (ST)', 'solvalla' ) }}
                        </a>
                    {% endif %}

                </div>

			</div>
		</div>

	</article>

{% endblock %}
