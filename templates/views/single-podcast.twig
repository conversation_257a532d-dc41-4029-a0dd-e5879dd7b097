{% extends "base.twig" %}

{% block content %}

	{# Article #}
    <article class="article article--podcast article--podcast--single" id="post-{{ episode.id }}">

        <div class="container-fluid">
			<div class="row">

				<div class="article--podcast--single__col">

					{# Breadcrumbs #}
					<nav aria-label="breadcrumb">
						<ol class="breadcrumb">
							<li class="breadcrumb-item"><a href="{{ site.url }}">{{ __( 'Hem', 'solvalla' ) }}</a></li>
							<li class="breadcrumb-item"><a href="{{ archive_url }}">{{ archive_title }}</a></li>
							<li class="breadcrumb-item active" aria-current="page">#{{ episode.episode_id }} {{ episode.title }}</li>
						</ol>
					</nav>

					{# Title #}
					<h1 class="h2 text-uppercase">
						#{{ episode.episode_id }} {{ episode.title }}
					</h1>

					{# Description #}
					<div class="article--podcast__description content-block">
						{{ episode.description_text }}
					</div>

					{# Links #}
					<div class="article--podcast__share-links d-flex flex-wrap gap-2 align-items-center">
						{% for key, link in links %}
							{% if link is not empty %}
								<a class="btn btn--large btn--{{ key|replace({' ': '-'})|lower }}" href="{{ link|e('esc_url') }}" target="_blank">
									{{ key }}
								</a>
							{% endif %}
						{% endfor %}
					</div>

				</div>

				<div class="article--podcast--single__col">

					{# Featured image #}
					<div class="article--podcast__img d-flex justify-content-center">
						{% if episode.local_image_url is not empty %}
							<img class="img-fluid" width="748" height="421" data-src="{{ episode.local_image_url }}" alt="{{ episode.title }}">
						{% endif %}
					</div>

				</div>

			</div>

			{# Audio player #}
			<div class="article--podcast__player d-flex rounded">
				<div class="article--podcast__player__img rounded-3 d-flex">
					<img class="img-fluid" width="240" height="240" data-src="{{ podcast_image }}" alt="Solvalla Podcast">
					<div class="article--podcast__player__control">
						<svg class="article--podcast__player__control__play" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><title>play</title><path d="M213.333333 189.226667v645.546666a42.666667 42.666667 0 0 0 66.005334 35.712l493.397333-322.773333a42.666667 42.666667 0 0 0 0-71.424L279.338667 153.514667A42.666667 42.666667 0 0 0 213.333333 189.226667z" fill="#000" /></svg>
						<svg class="article--podcast__player__control__pause" height="512px" style="enable-background:new 0 0 512 512;" version="1.1" viewBox="0 0 512 512" width="512px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>pause</title><g><path d="M224,435.8V76.1c0-6.7-5.4-12.1-12.2-12.1h-71.6c-6.8,0-12.2,5.4-12.2,12.1v359.7c0,6.7,5.4,12.2,12.2,12.2h71.6   C218.6,448,224,442.6,224,435.8z"/><path d="M371.8,64h-71.6c-6.7,0-12.2,5.4-12.2,12.1v359.7c0,6.7,5.4,12.2,12.2,12.2h71.6c6.7,0,12.2-5.4,12.2-12.2V76.1   C384,69.4,378.6,64,371.8,64z"/></g></svg>
					</div>
				</div>
				<div class="article--podcast__player__info w-100 align-self-center">
					<div class="w-100 text-uppercase">
						#{{ episode.episode_id }} {{ episode.title }}
					</div>
					<div class="d-block w-100">
						<audio class="article--podcast__player__audio" controls preload="metadata">
							<source src="{{ episode.audio_file|e('esc_url') }}" type="audio/mpeg">
							Your browser does not support the audio element.
						</audio>
					</div>
				</div>
			</div>

		</div>

	</article>

	{# Related news #}
	{% if other_episodes is not empty %}
	
		<div class="article__related article__related--podcast">
			<div class="container-fluid">
				<div class="row">
					<div class="col-12">
						<h2 class="article__related__title h1 text-uppercase mb-0">
							{{ __( 'Andra avsnitt', 'solvalla' ) }}
						</h2> 
					</div>
				</div>
				<div class="row">
					<div class="col-12">
						<hr class="my-0">
					</div>
				</div>
				<div class="row">
					<div class="col-12">
						{% for episode in other_episodes|slice( 0, 2 ) %}
							{% include "./partials/podcast-episode.twig" %}
						{% endfor %}
					</div>
				</div>
				<div class="row-">
					<div class="col-12">
						<div class="wp-block-buttons is-content-justification-left is-layout-flex wp-container-7">
							<div class="wp-block-button link-styled link-styled--dot" style="text-transform:uppercase">
								<a class="wp-block-button__link has-text-color has-background wp-element-button text-chathams-blue" style="background-color:#ffffff00" href="/podcast/">
									{{ __( 'Alla avsnitt', 'solvalla' ) }}
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

	{% endif %}

{% endblock %}
