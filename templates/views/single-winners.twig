{% extends "base.twig" %}

{% block content %}

	{# Page title #}
    <div class="page__title"></div>

	{# Article #}
    <article class="article post-type-{{ post.post_type }}" id="post-{{ post.ID }}">

		{# Article header #}
        <div class="container-fluid">
			<div class="row">
				<div class="single-article-details">

					{# Breadcrumbs #}
					<nav aria-label="breadcrumb">
						<ol class="breadcrumb">
							<li class="breadcrumb-item"><a href="{{ site.url }}">{{ __( 'Hem', 'solvalla' ) }}</a></li>
							<li class="breadcrumb-item"><a href="{{ archive_url }}">{{ archive_title }}</a></li>
							{% if taxonomy_term and taxonomy_archive_url %}
								<li class="breadcrumb-item"><a href="{{ taxonomy_archive_url }}">{{ taxonomy_term.name }}</a></li>
							{% endif %}
							<li class="breadcrumb-item active" aria-current="page">{{ post.title|e('wp_kses_post') }}</li>
						</ol>
					</nav>

					{# Meta #}
					<div class="article__meta">
						<h1 class="article__title text-uppercase disable-auto-translation">{{ post.title|e('wp_kses_post') }}</h1>
					</div>

					{# Featured image #}
					<div class="article__img disable-auto-translation">
						{% if fields.detail_image or post.thumbnail %}
							{% if fields.detail_image %}
								<img class="img-fluid w-100 d-none d-md-block" width="{{ fields.detail_image.width }}" height="{{ fields.detail_image.height }}" style="aspect-ratio: 16/9;object-fit: cover;" data-src="{{ fields.detail_image.url }}" alt="{{ post.title|striptags|e('html_attr') }}">
							{% else %}
								<img class="img-fluid w-100" width="1400" height="700" style="aspect-ratio: 16/9;object-fit: cover;" data-src="{{ post.thumbnail.src|resize(1400, 700, 'center') }}" srcset="{{ post.thumbnail.srcset }}" alt="{{ post.title|striptags|e('html_attr') }}">
							{% endif %}
							{% if fields.caption %}
								<div class="d-block text-center mt-2">{{ fields.caption }}</div>
							{% endif %}
						{% endif %}
					</div>

				</div>
			</div>
		</div>


        {# Article content #}
		<div class="article__content content-block">
			<div class="container">
				<div class="row">
					<div class="col-12">
						<div class="article__body">
							{{ post.content }}
						</div>
					</div>
				</div>
			</div>
		</div>

	</article>

    {# Related news #}
	{% if post.post_type == 'winners' and related_posts is not empty %}
	
		<div class="container-fluid">
			<hr>
		</div>

		<div class="article__related">
			<div class="container-fluid">
				<div class="row">
					<div class="col-12">
						<h2 class="article__related__title h1 text-uppercase">
							{{ __( 'Andra tidigare vinnare', 'solvalla' ) }}
						</h2> 
					</div>
					{% for post in related_posts %}
						{% include "./partials/card-winners.twig" with { 'fields': fields.related_posts_fields[post.ID] } %}
					{% endfor %}
				</div>
			</div>
		</div>

	{% endif %}

{% endblock %}
