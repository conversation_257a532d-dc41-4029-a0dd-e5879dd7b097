{% extends "base.twig" %}

{% block content %}
    {# Page title #}
    <div class="container-fluid page__title">
        <div class="row">
            <div class="col-12">
                <h1 class="display-1" style="text-transform:uppercase">{{ __('Skyddat innehåll', 'solvalla') }}</h1>
            </div>
        </div>
    </div>

    {# Separator #}
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <hr class="wp-block-separator has-alpha-channel-opacity">
            </div>
        </div>
    </div>

    {# Flexible spacer #}
    <div aria-hidden="true" class="wp-block-fsb-flexible-spacer fsb-flexible-spacer">
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--lg" style="height:40px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--md" style="height:20px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--sm" style="height:10px"></div>
    </div>

    {# Password form #}
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 col-md-6 col-lg-4">
                {% if error %}
                    <p class="password-form__error has-tall-poppy-color">{{ __('Felaktigt lösenord. Vänligen försök igen.', 'solvalla') }}</p>
                {% endif %}
                <form class="password-form" action="/wp-login.php?action=postpass" method="post">
                    <div class="mb-4">
                        <label class="form-label" for="pwbox-{{post.ID}}">{{ __('Lösenord:', 'solvalla') }}</label>
                        <input class="form-control password-box" style="border-radius: 0;" name="post_password" id="pwbox-{{post.ID}}" type="password" placeholder="{{ __('Ange lösenord', 'solvalla') }}" required />
                    </div>
                    <button type="submit" class="btn btn-secondary btn--dot" name="Submit">
                        {{ __('Skicka', 'solvalla') }}
                    </button>
                </form>
            </div>
        </div>
    </div>

    {% if post.post_type != 'page' %}
        {# Flexible spacer #}
        <div aria-hidden="true" class="wp-block-fsb-flexible-spacer fsb-flexible-spacer">
            <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--lg" style="height:200px"></div>
            <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--md" style="height:120px"></div>
            <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--sm" style="height:60px"></div>
        </div>
    {% endif %}

{% endblock %}
