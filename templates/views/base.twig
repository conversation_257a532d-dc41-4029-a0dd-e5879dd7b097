{% block html_head %}
	{% include 'html-header.twig' %}
	{% block head %}{% endblock %}
</head>{% endblock %}<body
class="{{body_class}}" data-template="base.twig">

{# Header #}
<header class="site-header">
	{% block header %}
		<div class="container-fluid">
			<nav class="navbar navbar-expand-lg navbar-light bg-light">

				<div
					class="d-flex align-items-center">

					{# Solvalla #}
					{% if site.id == solvalla.id %}

						{% if options.custom_logo and options.custom_logo_file_exists %}
							<a class="navbar-brand navbar-brand--custom" href="{{ site.url }}" title="Solvalla">
								{% if options.custom_logo_name|split('.')|last == 'svg' %}
									{{ source( directory ~ '/assets/images/' ~ options.custom_logo_name ) }}
								{% elseif options.custom_logo_name|split('.')|last == 'png' %}
									<img src="{{ theme.link ~ '/assets/images/' ~ options.custom_logo_name }}" alt="Solvalla"/>
								{% endif %}
							</a>
							<style>
								.navbar-brand--custom svg,
								.navbar-brand--custom img {
									width: {{options.custom_logo_width}}
									px!important;
									height: auto;
								}
							</style>
						{% else %}
							<a class="navbar-brand" href="{{ site.url }}" title="Solvalla">
								{{ source( directory ~ '/assets/images/logo-header.svg' ) }}
							</a>
						{% endif %}

						<div class="navbar-brand__separator">
							{{ source( directory ~ '/assets/images/icons/icon-exchange-horizontal.svg' ) }}
						</div>

						{% if options.enable_link_to_djurens_helg %}
							<a class="navbar-brand" style="transform: scale(1.5) translateY(-2px);transform-origin: 0;" href="https://www.solvalla.se/djurenshelg/" title="Djurens Helg">
								{{ source( directory ~ '/assets/images/logo_djurens_helg.svg' ) }}
							</a>
						{% elseif other_site_options.custom_logo and other_site_options.custom_logo_file_exists %}

							<a class="navbar-brand navbar-brand--custom-elitloppet" href="{{ elitloppet.url }}" title="Elitloppet">
								{% if other_site_options.custom_logo_name|split('.')|last == 'svg' %}
									{{ source( directory ~ '/assets/images/' ~ other_site_options.custom_logo_name ) }}
								{% elseif other_site_options.custom_logo_name|split('.')|last == 'png' %}
									<img src="{{ theme.link ~ '/assets/images/' ~ other_site_options.custom_logo_name }}" alt="Solvalla"/>
								{% endif %}
							</a>
							<style>
								.navbar-brand--custom-elitloppet svg. .navbar-brand--custom-elitloppet img {
									width: {{other_site_options.custom_logo_width}}
									px!important;
									height: auto;
								}
							</style>

						{% else %}

							<a class="navbar-brand navbar-brand--alt" href="{{ elitloppet.url }}" title="Elitloppet">
								{{ source( directory ~ '/assets/images/logo-elitloppet.svg' ) }}
							</a>
						{% endif %}

					{# Elitloppet #}
					{% elseif site.id == elitloppet.id %}

						{% if options.custom_logo and options.custom_logo_file_exists %}
							<a class="navbar-brand navbar-brand--custom-elitloppet" href="{{ site.url }}" title="Elitloppet">
								{% if options.custom_logo_name|split('.')|last == 'svg' %}
									{{ source( directory ~ '/assets/images/' ~ options.custom_logo_name ) }}
								{% elseif options.custom_logo_name|split('.')|last == 'png' %}
									<img src="{{ theme.link ~ '/assets/images/' ~ options.custom_logo_name }}" alt="Solvalla"/>
								{% endif %}
							</a>
							<style>
								.navbar-brand--custom-elitloppet svg. .navbar-brand--custom-elitloppet img {
									width: {{options.custom_logo_width}}
									px!important;
									height: auto;
								}
							</style>
						{% else %}
							<a class="navbar-brand navbar-brand--alt" href="{{ site.url }}" title="Elitloppet">
								{{ source( directory ~ '/assets/images/logo-elitloppet.svg' ) }}
							</a>
						{% endif %}

						<div class="navbar-brand__separator">
							{{ source( directory ~ '/assets/images/icons/icon-exchange-horizontal.svg' ) }}
						</div>

						{% if other_site_options.custom_logo and other_site_options.custom_logo_file_exists %}
							<a class="navbar-brand navbar-brand--custom" href="{{ solvalla.url }}" title="Solvalla">
								{% if other_site_options.custom_logo_name|split('.')|last == 'svg' %}
									{{ source( directory ~ '/assets/images/' ~ other_site_options.custom_logo_name ) }}
								{% elseif other_site_options.custom_logo_name|split('.')|last == 'png' %}
									<img src="{{ theme.link ~ '/assets/images/' ~ other_site_options.custom_logo_name }}" alt="Solvalla"/>
								{% endif %}
							</a>
							<style>
								.navbar-brand--custom svg,
								.navbar-brand--custom img {
									width: {{other_site_options.custom_logo_width}}
									px!important;
									height: auto;
								}
							</style>
						{% else %}
							<a class="navbar-brand" href="{{ solvalla.url }}" title="Solvalla">
								{{ source( directory ~ '/assets/images/logo-header.svg' ) }}
							</a>
						{% endif %}

					{% endif %}


				</div>

				{# Desktop menu #}
				<div class="collapse navbar-collapse justify-content-center" id="navbarNavDropdown">
					<ul class="ml-auto navbar-nav">
						{% include "./partials/nav.twig" with { menu: main_menu } %}
						{% include "./partials/nav.twig" with { menu: main_menu_right } %}
						{{ function('do_shortcode', '[custom-language-switcher]') }}
					</ul>
				</div>

				{% if options.countdown == true %}

					{# Countdown #}
					<div class="countdown">
						{% include "./partials/countdown.twig" with { countdown_date: options.countdown_date } %}
					</div>

				{% endif %}

				{# Mobile menu #}
				<div class="collapse navbar-collapse justify-content-center" id="navbarNavDropdownMobile">
					<ul class="ml-auto navbar-nav">
						{% include "./partials/nav-mobile.twig" with { menu: main_menu } %}
						{% include "./partials/nav-mobile.twig" with { menu: main_menu_right } %}
						{{ function('do_shortcode', '[custom-language-switcher]') }}
					</ul>
				</div>

				{# Menu button #}
				<button class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdownMobile" aria-controls="navbarNavDropdownMobile" aria-expanded="false" aria-label="Toggle navigation">
					{{ source( directory ~ '/assets/images/icons/icon-elipse.svg' ) }}
					{{ source( directory ~ '/assets/images/icons/icon-hamburger.svg' ) }}
				</button>

			</nav>

		</div>
	{% endblock %}
</header>

{# Content #}
<main role="main" class="site-main">
	{% block content %}
		{{ __( 'Tyvärr, inget innehåll', 'solvalla' ) }}
	{% endblock %}
</main>

{# Footer #}
<footer class="site-footer">
	{% block footer %}
		{# <div class="wrapper"> #}
		{% include 'footer.twig' %}
		{# </div> #}
	{% endblock %}
</footer>

<!-- Google Tag Manager (noscript) -->
{# Solvalla #}
{% if site.id == solvalla.id %}
	<noscript>
		<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MCGMJD6" height="0" width="0" style="display:none;visibility:hidden"></iframe>
	</noscript>
{# Elitloppet #}
{% elseif site.id == elitloppet.id %}
	<noscript>
		<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5HJ9F33" height="0" width="0" style="display:none;visibility:hidden"></iframe>
	</noscript>
{% endif %}

{{ function('wp_footer') }}</body></html>
