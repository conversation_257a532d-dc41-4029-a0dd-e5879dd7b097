<div class="col-xl-3 col-lg-4 col-sm-6">

    {# Card #}
    <div class="card card--news border-0 rounded-0 apply-first-link">

        {# Solvalla placeholder #}
        {% if site.id == solvalla.id %}
            {% set placeholder = Image( site.theme.link ~ '/assets/images/placeholder.jpg' ) | relative %}
        {# Elitloppet placeholder #}
        {% elseif site.id == elitloppet.id %}
            {% set placeholder = Image( site.theme.link ~ '/assets/images/elitloppet-placeholder.jpg' ) | relative %}
        {% endif %}

        {# Image #}
        <a class="card__img d-block text-decoration-none" href="{{ post.pdf.url|e('esc_url') }}" target="_blank">
            <img class="img-fluid" data-src="{{ post.pdf_thumbnail|default(placeholder)|resize(600, 851)|e('esc_url') }}" alt="{{ post.title|e('html_attr') }}">
        </a>

        {# Card body #}
        <div class="card-body px-0">

            <div>

                {# Title #}
                <h5 class="card-title">
                    <a class="text-decoration-none text-cod-grey text-uppercase" href="{{ post.pdf.url|e('esc_url') }}" target="_blank">
                        {{ post.title|e('wp_kses_post') }}
                    </a>
                </h5>
                
                {# Date #}
                <div class="card-subtitle text-mine-shaft">
                    {{ post.custom_date|date("d F Y")|e }}
                </div>

            </div>

            <div>

                {# Button #}
                <a class="btn btn-secondary btn--download" href="{{ post.pdf.url|e('esc_url') }}" target="_blank">
                    {{ __( 'Ladda ner', 'solvalla' ) }}
                </a>

            </div>

        </div>

    </div>

</div>