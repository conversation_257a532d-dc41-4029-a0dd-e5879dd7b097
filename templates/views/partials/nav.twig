{% for item in menu.items %}
  {% if item.children %}
    <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="nav-item {% if item.current_item_parent %} active{% endif %} mega-menu__parent {{ item.class }}">
      <a id="menu-item-mega-menu-{{ item.post_name }}" class="nav-link mega-menu__link" href="{{ item.link }}" target="{{ item.target|length > 0 ? item.target : '_self' }}" role="button" aria-expanded="false" {{ item.color is not empty ? 'style="color:' ~ item.color ~ '!important;"'  }}>
        {{ item.title }}
      </a>
      <ul class="mega-menu" aria-labelledby="menu-item-mega-menu-{{ item.post_name }}" role="menu">
        {% for child in item.children|slice( 0, 4 ) %}
          <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="mega-menu__col mega-menu__col--{{ child.custom.width }} nav-item{% if child.current %} active{% endif %} {{ child.class }}">
            <a class="mega-menu__item" href="{{ child.link }}" target="{{ child.target|length > 0 ? child.target : '_self' }}" style="color: {{ child.custom.color }};">
              <span>{{ child.title }}</span>
            </a>
            {% if child.custom.description %}
              <span class="mega-menu__desc">{{ child.custom.description }}</span>
            {% endif %}
            {% if child.children %}
              <ul class="mega-menu__child-menu" aria-labelledby="menu-item-mega-menu-child-menu{{ item.post_name }}" role="menu">
                {% for grand_child in child.children %}
                  <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="nav-item{% if grand_child.current %} active{% endif %} {{ grand_child.class }}">
                    <a class="mega-menu__item" href="{{ grand_child.link }}" target="{{ grand_child.target|length > 0 ? grand_child.target : '_self' }}" style="color: {{ grand_child.custom.color }};">
                      <span>{{ grand_child.title }}</span>
                    </a>
                  </li>
                {% endfor %}
              </ul>
            {% endif %}
            {{ child.index }}
            {% if loop.index == 4 %}
              {% for child_extra in item.children|slice( 4 ) %}
                <a class="mega-menu__item" href="{{ child_extra.link }}" target="{{ child_extra.target|length > 0 ? child_extra.target : '_self' }}" style="color: {{ child_extra.custom.color }};">
                  <span>{{ child_extra.title }}</span>
                </a>
              {% endfor %}
            {% endif %}
          </li>
        {% endfor %}
      </ul>
    </li>
  {% else %}
    <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="nav-item{% if item.current %} active{% endif %} {{ item.classes|join(' ') }}">
      <a class="nav-link" href="{{ item.link }}" target="{{ item.target|length > 0 ? item.target : '_self' }}">
        {{ item.title }}
      </a>
    </li>
  {% endif %}
{% endfor %}
