<div class="article--podcast row content-block {% if remove_border == false %}border-custom border-custom--bottom{% else %}mb-3 mb-md-4 pb-0{% endif %}">

    <div class="article--podcast__col pe-lg-4 pe-md-3 mb-md-0 mb-4">

        {# Image #}
        {% if episode.thumbnail_url is not empty %}
            <a class="card__img d-block" href="/podcast/{{ episode.slug }}">
                <img class="img-fluid" width="748" height="421" style="aspect-ratio: 16 / 9;object-fit: cover;" data-src="{{ episode.thumbnail_url }}" alt="{{ episode.title|e('html_attr') }}">
            </a>
        {% endif %}

    </div>

    <div class="article--podcast__col ps-md-3">

        <div class="article--podcast__col__inner">

            {# Title #}
            <h3 class="text-uppercase h2">
                <a class="link-cod-grey text-decoration-none" href="/podcast/{{ episode.slug }}">
                    #{{ episode.episode_id }} {{ episode.title }}
                </a>
            </h3>

            {# Description #}
            <div class="article--podcast__description d-none d-md-block">
                {% if episode.excerpt %}
                    {{ episode.excerpt }}
                {% endif %}
            </div>

        </div>

        {# Links #}
        <div class="d-block d-md-none fw-bold text-uppercase mt-4 mb-2">{{ __( 'Lyssna på oss på', 'solvalla' ) }}:</div>
        <div class="article--podcast__share-links d-flex flex-wrap gap-2 align-items-center">
            {% for key, link in links %}
                {% if link is not empty %}
                    <a class="btn btn--large btn--{{ key|replace({' ': '-'})|lower }}" href="{{ link|e('esc_url') }}" target="_blank">
                        {{ key }}
                    </a>
                {% endif %}
            {% endfor %}
        </div>

        {# Read more #}
        <div class="link-styled link-styled--arrow-top">
            <a class="calendar__event__link link-mine-shaft" href="/podcast/{{ episode.slug }}" title="{{ episode.title|e('html_attr') }}"></a>
        </div>

    </div>

</div>