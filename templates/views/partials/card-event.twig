<div
	class="col-xl-3 col-lg-4 col-md-6 d-flex align-items-stretch">

	{# Card #}
	<div
		class="border-0 card card--event d-block d-md-flex w-100 rounded-0 apply-first-link">

		{# Image #}
		{% if event.local_thumbnail_url is not empty %}
			<a class="card__img d-block text-decoration-none" href="{{ event.link_replacement ? event.link_replacement.url|e('esc_url') : event.local_url|e('esc_url') }}">
				<img class="d-block w-100 img-fluid" style="aspect-ratio: 16 / 9;" data-src="{{ event.local_thumbnail_url|e('esc_url') }}" alt="{{ event.event_name|e('html_attr') }}">
			</a>
		{% endif %}

		{# Card body #}
		<div
			class="px-0 card-body w-100">

			{# Title #}
			<h5 class="d-block card-title">
				<a class="d-block text-decoration-none text-cod-grey text-uppercase" href="{{ event.link_replacement ? event.link_replacement.url|e('esc_url') : event.local_url|e('esc_url') }}">
					{{ event.event_name|e('wp_kses_post') }}
				</a>
			</h5>

			{# Meta #}
			<span class="d-block card-meta">
				{{ event.event_date|date("j")|e }}
				{{ event.event_date|date("F")|capitalize|e }}
				{{ event.event_date|date("Y")|e }}
			</span>

			{# Description #}
			{% if event.event_description is not empty %}
				<div class="card-description text-scarpa-flow">
					<p>{{ event.event_description|striptags }}</p>
				</div>
			{% endif %}

			{# Buttons #}
			<div
				class="flex-wrap gap-3 mt-auto d-flex align-items-center">

				{# Buy tickets #}
				{% if event.event_state == 'saleEnded' or event.event_state == 'SaleEnded' or 'fri entré' in event.event_description or 'Fri entré' in event.event_description %}
					{# <div class="link-styled link-styled--ticket link-styled--tall-poppy link-styled--padding">
					                        <span class="calendar__event__link link-mine-shaft text-uppercase">
					                            {{ __( 'Slut på försäljning', 'solvalla' ) }}
					                        </span>
					                    </div> #}
				{% elseif event.stock_status in ['instock', 'fewleft', 'InStock', 'FewLeft'] %}
					<div>
						<a class="btn btn-secondary btn--ticket" href="{{ event.tickets_url|e('esc_url') }}" target="_blank">
							{{ __( 'Köp biljetter', 'solvalla' ) }}
						</a>
					</div>
				{% else %}
					<div class="link-styled link-styled--ticket link-styled--tall-poppy link-styled--padding">
						<span class="calendar__event__link link-mine-shaft text-uppercase">
							{{ __( 'Stängt för försäljning', 'solvalla' ) }}
						</span>
					</div>
				{% endif %}

				{# Read more #}
				<div class="link-styled link-styled--dot link-styled--chathams-blue link-styled--padding text-decoration-none text-uppercase text-chathams-blue">
                    <a href="{{ event.link_replacement ? event.link_replacement.url|e('esc_url') : event.local_url|e('esc_url') }}">
                        {{ event.link_replacement and event.link_replacement.title ? event.link_replacement.title : __( 'Läs mer', 'solvalla' ) }}
                    </a>
				</div>


			</div>

		</div>

	</div>

</div>
