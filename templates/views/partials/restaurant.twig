<div class="article--event__packages__item row order-2">
    <div class="article--event__packages__item__inner border-custom border-custom--top col-12">
        <div class="row">
            <div class="col-md-6">
                <img class="img-fluid" width="520" height="292" data-src="{{ package.local_image|e('esc_url') }}" alt="{{ package.title }}">
            </div>
            <div class="col-md-6">
                <h3 class="text-uppercase h2 d-flex align-items-center">
                    {{ package.title }}
                    {% if package.status and package.status != 'Tillgänglig' %}
                        <span class="badge ms-3 fw-semibold {% if package.status == 'Slutsålt' %}bg-tall-poppy{% else %}bg-primary{% endif %}" style="font-size: 14px; border-radius: 5px; letter-spacing: 0.2px;">{{ package.status }}</span>
                    {% endif %}
                </h3>
                <div class="article--event__packages__item__description">
                    {% if package.description %}
                        {{ package.description|e('wp_kses_post') }}
                    {% endif %}
                </div>
                <div class="d-flex flex-wrap gap-4 align-items-center">
                    <a class="btn btn-secondary btn--dot" href="{{ package.link|e('esc_url') }}" target="_blank">
                        {{ __( 'Boka här', 'solvalla' ) }}
                    </a>
                    {% for menu_link in package.menu_links %}
                    <span class="link-styled link-styled--dot text-uppercase">
                        <a class="link-secondary" href="{{ menu_link.href|e('esc_url') }}" target="_blank">
                            {{ menu_link.text }}
                        </a>
                    </span>
                    {% endfor %}

                    {% if event.links is not empty %}
                        {% for link in event.links %}
                            {% if package.title in link.text %}
                                <div class="link-styled link-styled--program link-styled--chathams-blue">
                                    <a class="calendar__event__link link-mine-shaft" href="{{ link.url|e('esc_url') }}" target="_blank">
                                        {{ link.text }}
                                    </a>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>