{% for item in menu.items %}
  {% if item.children %}
    <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="nav-item {% if item.current_item_parent %} active{% endif %} dropdown {{ item.class }}">
      <a class="dropdown-link" href="{{ item.link }}" target="{{ item.target|length > 0 ? item.target : '_self' }}" {{ item.color is not empty ? 'style="color:' ~ item.color ~ '!important;"'  }}>{{ item.title }}</a>
      <span id="menu-item-dropdown-{{ item.post_name }}" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <span>
          {{ source( directory ~ '/assets/images/icons/icon-angle-down.svg' ) }}
        </span>
      </span>
      <ul class="dropdown-menu" aria-labelledby="menu-item-dropdown-{{ item.post_name }}" role="menu">
        {% for child in item.children %}
          <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="nav-item{% if child.current %} active{% endif %} {{ child.class }}">
            <a class="dropdown-item" href="{{ child.link }}" target="{{ child.target|length > 0 ? child.target : '_self' }}" style="color: {{ child.custom.color }};">
                {{ child.title }}
            </a>
            {% if child.custom.description %}
              <span class="mobile-menu__desc">{{ child.custom.description }}</span>
            {% endif %}
            {% if child.children %}
              <ul class="mobile-menu__child-menu" aria-labelledby="menu-item-mobile-menu-child-menu{{ item.post_name }}" role="menu">
                {% for grand_child in child.children %}
                  <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="nav-item{% if grand_child.current %} active{% endif %} {{ grand_child.class }}">
                    <a class="mobile-menu__item" href="{{ grand_child.link }}" target="{{ grand_child.target|length > 0 ? grand_child.target : '_self' }}" style="color: {{ grand_child.custom.color }};">
                      <span>{{ grand_child.title }}</span>
                    </a>
                  </li>
                {% endfor %}
              </ul>
            {% endif %}
          </li>
        {% endfor %}
      </ul>
    </li>
  {% else %}
    <li itemscope="itemscope" itemtype="https://www.schema.org/SiteNavigationElement" id="menu-item-{{ item.post_name }}" class="nav-item{% if item.current %} active{% endif %} {{ item.classes|join(' ') }}">
      <a class="nav-link" href="{{ item.link }}" target="{{ item.target|length > 0 ? item.target : '_self' }}" style="color: {{ item.custom.color }};">
        {{ item.title }}
      </a>
    </li>
  {% endif %}
{% endfor %}