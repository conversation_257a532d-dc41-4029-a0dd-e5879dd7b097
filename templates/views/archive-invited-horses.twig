{% extends "base.twig" %}

{% block content %}

   {# Page title #}
    <div class="page__title--archive">
        <div class="container-fluid">
            <div class="row">

                {# Breadcrumbs #}
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ site.url }}">{{ __( 'Hem', 'solvalla' ) }}</a></li>
                        {% if which_event_archive %}
                            <li class="breadcrumb-item"><a href="{{ archive_url }}">{{ archive_title }}</a></li>
                        {% endif %}
                        <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
                    </ol>
                </nav>

                {# Title #}
                <h1 class="text-uppercase">{{ title }}</h1>
                
            </div>
        </div>
    </div>

    {# News #}
    {% if posts is not empty %}
        <div class="container-fluid">
            <div class="row">
                {% for post in posts %}
                    <div class="col-xl-3 col-lg-4 col-md-6">

                        {# Card #}
                        <div class="card card--invited-horses border-0 rounded-0 apply-first-link">

                            {# Solvalla placeholder #}
                            {% if site.id == solvalla.id %}
                                {% set placeholder = Image( site.theme.link ~ '/assets/images/placeholder.jpg' ) | relative %}
                            {# Elitloppet placeholder #}
                            {% elseif site.id == elitloppet.id %}
                                {% set placeholder = Image( site.theme.link ~ '/assets/images/elitloppet-placeholder.jpg' ) | relative %}
                            {% endif %}

                            {# Featured image #}
                            <a class="card__img d-block text-decoration-none disable-auto-translation" href="{{ post.link|e('esc_url') }}">
                            {# post.thumbnail_crop ? #}
                                {% if fields.detail_image or post.thumbnail %}
                                    {% if fields.detail_image %}
                                        <img class="img-fluid" width="736" height="429" style="aspect-ratio: 16 / 9;object-fit: cover;" data-src="{{ fields.detail_image.url }}" alt="{{ post.title|striptags|e('html_attr') }}">
                                    {% else %}
                                        <img class="img-fluid" width="736" height="429" style="aspect-ratio: 16 / 9;object-fit: cover;" data-src="{{ post.thumbnail.src|tojpg|resize(736, 429, 'center')|e('esc_url') }}" alt="{{ post.title|striptags|e('html_attr') }}">
                                    {% endif %}
                                {% endif %}
                            </a>

                            {# Card body #}
                            <div class="card-body px-0">

                                {# Title #}
                                <h2 class="card-title disable-auto-translation">
                                    <a class="text-decoration-none text-cod-grey text-uppercase" href="{{ post.link|e('esc_url') }}">
                                        {{ post.title|e('wp_kses_post') }}
                                    </a>
                                </h2>

                                <div class="invited-horse-info-box" style="margin-block: .25rem">
                                    <ul>
                                        {# <li><span style="font-weight: 700">{{ post.fields.age.label|e }}</span> {{ post.fields.age.value|e }}</li>
                                        <li><span style="font-weight: 700">{{ post.fields.gender.label|e }}</span> {{ post.fields.gender.value|e }}</li>
                                        <li><span style="font-weight: 700">{{ post.fields.record.label|e }}</span> {{ post.fields.record.value|e }}</li>
                                        <li><span style="font-weight: 700">{{ post.fields.nationality.label|e }}</span> {{ post.fields.nationality.value|e }}</li> #}
                                        {% for field in post.fields %}
                                            <li><span style="font-weight: 700" {% if field.name == 'father' or field.name == 'mother' or field.name == 'owner' or field.name == 'trainer' or field.name == 'groom' or field.name == 'breeder' %}class="disable-auto-translation"{% endif %}>{{ field.label|e }}</span> {{ field.value|e }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                
                                
                                {# Read more #}
                                <div class="link-styled link-styled--dot link-styled--razzmatazz link-styled--padding text-decoration-none text-uppercase text-razzmatazz d-flex apply-first-link">
                                    <a href="{{ post.link|e('esc_url') }}" data-link-alt="{{ __( 'Läs mer', 'solvalla' ) }}" class="d-flex link-slice-hover align-items-center">
                                        <span>{{ __( 'Läs mer', 'solvalla' ) }}</span>
                                    </a>
                                </div>
                                
                            </div>

                        </div>

                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

	{# Pagination #}
    {% if posts.pagination.pages %}
    <div class="page-pagination">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="Page navigation">
                        <ul class="pagination pagination-lg justify-content-center flex-wrap">
                            {% if posts.pagination.prev %}
                                <li class="page-item fw-medium">
                                    <a href="{{ posts.pagination.prev.link }}" class="page-link page-link--prev {{ posts.pagination.prev.link|length ? '' : 'disabled' }}">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                            {% for page in posts.pagination.pages %}
                                {% if page.link %}
                                <li class="page-item fw-medium">
                                    <a href="{{ page.link }}" class="page-link {{ page.class }}">{{ page.title }}</a>
                                </li>
                                {% else %}
                                <li class="page-item fw-medium active" aria-current="page">
                                    <a href="{{ page.link }}" class="page-link {{ page.class }}">{{ page.title }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            {% if posts.pagination.next %}
                                <li class="page-item fw-medium">
                                    <a href="{{ posts.pagination.next.link }}" class="page-link page-link--next {{ posts.pagination.next.link|length ? '' : 'disabled' }}">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {# Flexible spacer #}
    <div aria-hidden="true" class="wp-block-fsb-flexible-spacer fsb-flexible-spacer">
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--lg" style="height:120px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--md" style="height:80px"></div>
        <div class="fsb-flexible-spacer__device fsb-flexible-spacer__device--sm" style="height:30px"></div>
    </div>

    {# Banners #}
    {% filter shortcodes %}
        [random_cpt_post taxonomy="groupped" term_id="12"]
    {% endfilter %}

{% endblock %}