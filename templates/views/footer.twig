<div class="container-fluid">

    {# Footer menus and address #}
    <div class="row site-footer__row border-custom border-custom--top">

        {# Text #}
        <div class="col-sm site-footer__col site-footer__text content-block">
            
            {# Solvalla #}
			{% if site.id == solvalla.id %}
                {{ source( directory ~ '/assets/images/logo-footer.svg' ) }}
            {# Elitloppet #}
            {% elseif site.id == elitloppet.id %}
                {{ source( directory ~ '/assets/images/logo-elitloppet-footer.svg' ) }}
            {% endif %}

            <div class="text-cod-grey">
                {{ options.about_solvalla|e('wp_kses_post') }}
            </div>
        </div>

        {# Menu #}
        <div class="col-sm site-footer__col site-footer__menu">
            {% if footer_menu %}
                <h6 class="lh-base text-uppercase text-cod-grey fw-bold">{{ footer_menu.name }}</h6>
                <ul class="list-unstyled">
                    {% for item in footer_menu.items %}
                        <li class="link-styled">
                            <a class="text-decoration-none text-uppercase link-cod-grey fw-medium" href="{{ item.link|e('esc_url') }}" target="{{ item.target|e('html_attr') }}">
                                {{ item.title }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>

        {# Menu #}
        <div class="col-sm site-footer__col site-footer__menu">
            {% if footer_menu_2 %}
                <h6 class="lh-base text-uppercase text-cod-grey fw-bold">{{ footer_menu_2.name }}</h6>
                <ul class="list-unstyled">
                    {% for item in footer_menu_2.items %}
                        <li class="link-styled">
                            <a class="text-decoration-none text-uppercase link-cod-grey fw-medium" href="{{ item.link|e('esc_url') }}" target="{{ item.target|e('html_attr') }}">
                                {{ item.title }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>

        {# Text #}
        <div class="col-sm site-footer__col site-footer__text content-block">
            <h6 class="mb-0 lh-base text-uppercase text-cod-grey fw-bold">{{ __( 'Om spel', 'solvalla' ) }}</h6>
            <div>
                {{ options.about_games|e('wp_kses_post') }}
            </div>
        </div>
        
        {# Text #}
        <div class="col-sm site-footer__col site-footer__text content-block">
            <h6 class="mb-0 lh-base text-uppercase text-cod-grey fw-bold">{{ __( 'Kontakta oss', 'solvalla' ) }}</h6>
            <div>
                {{ options.contact_us|e('wp_kses_post') }}
            </div>
        </div>

    </div>

    {# Copyright #}
    <div class="row site-footer__row border-custom border-custom--top">
        <div class="col-8" style="white-space:nowrap">
            {{ __( 'Designad och utvecklad av', 'solvalla' ) }} <a class="text-decoration-none link-cod-grey fw-medium" href="https://www.visionmate.se/" target="_blank">{{ 'Visionmate' }}</a>.
        </div>
        <div class="col-4 text-end">
            <div class="copyright fw-medium">{{ now|default("now")|date('Y') }}</div>
        </div>
    </div>

</div>