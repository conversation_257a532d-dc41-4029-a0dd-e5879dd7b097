{"key": "group_63d01d3d09f98", "title": "Theme Settings", "fields": [{"key": "field_645386bd4831f", "label": "<PERSON><PERSON><PERSON>", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_67ba272047ecf", "label": "Custom Settings for Tickster Slides", "name": "custom_settings_for_tickster_slides", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_67b8a6234ebc3", "label": "Slide Mobile Image Replacement", "name": "slide_mobile_image_replacement", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium", "parent_repeater": "field_67ba272047ecf"}, {"key": "field_67ba275347ed0", "label": "Which Slide", "name": "which_slide", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"vgz63fpuf92jg8d": "9 May 2025 - FredagsTrav", "yb6kfr2cndng5j8": "11 May 2025 - PonnyTrav", "n56x6utu380gyuu": "14 May 2025 - OnsdagsNöjet", "y69l9wv557xkygy": "23 May 2025 - Elitloppsfredag", "2zx85065xhkkygn": "23 May 2025 - Elitauktion 2025", "d2ezh37clu861zr": "24 May 2025 - Elitloppslördag", "x6eg6tvxj7r2n0l": "25 May 2025 - Elitloppssöndag", "cvgygd52pf9vzaf": "2 June 2025 - SommarLunch", "51lhhp8eakjkdf3": "10 June 2025 - SommarTrav", "lkdwwwf3ncb474l": "15 June 2025 - PonnyTrav", "z96gcg44w7zczh1": "18 June 2025 - SommarTrav STL", "u8g0ne0n7xhf6bl": "24 June 2025 - SommarTrav", "py6u2famrppxlbp": "29 June 2025 - FörmiddagsTrav", "3t6djk7d1jxvnub": "1 July 2025 - SommarTrav", "57ffrn2wngzy2gc": "31 July 2025 - SommarTrav", "uffv3h28luaf0wl": "10 August 2025 - FörmiddagsTrav", "33e8lx5ddph7mrn": "12 August 2025 - SommarTrav", "t7ap3hvlafyg4v8": "20 August 2025 - Jubileumsdagen", "k10yhjc7v8yn402": "24 August 2025 - PonnyTrav", "za1xm8g81gevw2g": "27 August 2025 - OnsdagsNöjet", "6wlbleh4kf6ch1g": "3 September 2025 - OnsdagsNöjet", "lhua5wa7c1ar3xc": "10 September 2025 - OnsdagsNöjet", "5enarckmet5cvze": "12 September 2025 - FredagsTrav", "g1rrkr760hugnv3": "17 September 2025 - OnsdagsNöjet", "5cnmmbdrm55d29g": "21 September 2025 - FörmiddagsTrav", "zb9vlpxgu4azprb": "24 September 2025 - OnsdagsNöjet", "fkyx8c2e2n4baf7": "28 September 2025 - Kriterieuttagningar med V75®", "maru2gyjwgrpn66": "1 October 2025 - OnsdagsNöjet", "eb8jrxenyadj2h1": "8 October 2025 - OnsdagsNöjet", "kl4cuylprzv369d": "10 October 2025 - LunchTrav", "92bheg137amwj7j": "11 October 2025 - SuperLördag", "152cxpj430hyypp": "22 October 2025 - OnsdagsNöjet", "r71nkz02u33mp2z": "26 October 2025 - PonnyTrav", "wu33rc1xubt8trx": "29 October 2025 - OnsdagsNöjet", "689gvu36nxkkhfb": "2 November 2025 - Breeders' Crown semifinaler med V75®", "chc92gyz5hrhz1u": "5 November 2025 - OnsdagsNöjet", "5ybtazpnc8w4uw1": "12 November 2025 - OnsdagsNöjet", "ryat47b94vmz5h4": "19 November 2025 - OnsdagsNöjet", "jjnd8z9kpyhz2hc": "26 November 2025 - OnsdagsNöjet", "gh36641nbd086m4": "29 November 2025 - STL med V75®", "5zxxrb9zzangrxz": "3 December 2025 - OnsdagsNöjet", "wwynylav9depk5f": "10 December 2025 - OnsdagsNöjet", "ptnh91p27k2zm7l": "17 December 2025 - OnsdagsNöjet", "rkp0nyh77utjv52": "26 December 2025 - STL-finaler med V75®"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0, "placeholder": "", "parent_repeater": "field_67ba272047ecf", "create_options": 0, "save_options": 0}]}, {"key": "field_67d2bc549d12d", "label": "Priority Rules", "name": "scheduled_priority_rules", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Rule", "rows_per_page": 20, "sub_fields": [{"key": "field_67d2bc8a9d12e", "label": "Slide", "name": "scheduled_slide", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"custom_9685": "Custom - Restaurangsläpp 26 feb inför", "custom_9331": "Custom - <PERSON><PERSON> <PERSON> &#8211; 3 februari", "custom_9264": "Custom - Rösta årets häst 2024", "custom_8933": "Custom - Årskort 2024", "custom_7684": "Custom - NYFIKEN PÅ TRAV? AW-PAKET  &#8211; 14 FEB", "custom_6772": "Custom - Julbord", "custom_6586": "Custom - Early Bird 2024", "custom_6304": "Custom - Pangpriser Jubileumsdagen och SuperLördag med Svenskt Travkriterium", "custom_6290": "Custom - Hotellpaket &#8211; 23 mars", "custom_5563": "Custom - Test Slide", "vgz63fpuf92jg8d": "9 May 2025 - FredagsTrav", "yb6kfr2cndng5j8": "11 May 2025 - PonnyTrav", "n56x6utu380gyuu": "14 May 2025 - OnsdagsNöjet", "y69l9wv557xkygy": "23 May 2025 - Elitloppsfredag", "2zx85065xhkkygn": "23 May 2025 - Elitauktion 2025", "d2ezh37clu861zr": "24 May 2025 - Elitloppslördag", "x6eg6tvxj7r2n0l": "25 May 2025 - Elitloppssöndag", "cvgygd52pf9vzaf": "2 June 2025 - SommarLunch", "51lhhp8eakjkdf3": "10 June 2025 - SommarTrav", "lkdwwwf3ncb474l": "15 June 2025 - PonnyTrav", "z96gcg44w7zczh1": "18 June 2025 - SommarTrav STL", "u8g0ne0n7xhf6bl": "24 June 2025 - SommarTrav", "py6u2famrppxlbp": "29 June 2025 - FörmiddagsTrav", "3t6djk7d1jxvnub": "1 July 2025 - SommarTrav", "57ffrn2wngzy2gc": "31 July 2025 - SommarTrav", "uffv3h28luaf0wl": "10 August 2025 - FörmiddagsTrav", "33e8lx5ddph7mrn": "12 August 2025 - SommarTrav", "t7ap3hvlafyg4v8": "20 August 2025 - Jubileumsdagen", "k10yhjc7v8yn402": "24 August 2025 - PonnyTrav", "za1xm8g81gevw2g": "27 August 2025 - OnsdagsNöjet", "6wlbleh4kf6ch1g": "3 September 2025 - OnsdagsNöjet", "lhua5wa7c1ar3xc": "10 September 2025 - OnsdagsNöjet", "5enarckmet5cvze": "12 September 2025 - FredagsTrav", "g1rrkr760hugnv3": "17 September 2025 - OnsdagsNöjet", "5cnmmbdrm55d29g": "21 September 2025 - FörmiddagsTrav", "zb9vlpxgu4azprb": "24 September 2025 - OnsdagsNöjet", "fkyx8c2e2n4baf7": "28 September 2025 - Kriterieuttagningar med V75®", "maru2gyjwgrpn66": "1 October 2025 - OnsdagsNöjet", "eb8jrxenyadj2h1": "8 October 2025 - OnsdagsNöjet", "kl4cuylprzv369d": "10 October 2025 - LunchTrav", "92bheg137amwj7j": "11 October 2025 - SuperLördag", "152cxpj430hyypp": "22 October 2025 - OnsdagsNöjet", "r71nkz02u33mp2z": "26 October 2025 - PonnyTrav", "wu33rc1xubt8trx": "29 October 2025 - OnsdagsNöjet", "689gvu36nxkkhfb": "2 November 2025 - Breeders' Crown semifinaler med V75®", "chc92gyz5hrhz1u": "5 November 2025 - OnsdagsNöjet", "5ybtazpnc8w4uw1": "12 November 2025 - OnsdagsNöjet", "ryat47b94vmz5h4": "19 November 2025 - OnsdagsNöjet", "jjnd8z9kpyhz2hc": "26 November 2025 - OnsdagsNöjet", "gh36641nbd086m4": "29 November 2025 - STL med V75®", "5zxxrb9zzangrxz": "3 December 2025 - OnsdagsNöjet", "wwynylav9depk5f": "10 December 2025 - OnsdagsNöjet", "ptnh91p27k2zm7l": "17 December 2025 - OnsdagsNöjet", "rkp0nyh77utjv52": "26 December 2025 - STL-finaler med V75®"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0, "placeholder": "", "create_options": 0, "save_options": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2d981f07d5", "label": "Priority", "name": "priority", "aria-label": "", "type": "range", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": 1, "min": 1, "max": 20, "allow_in_bindings": 0, "step": 1, "prepend": "", "append": "", "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d81b6978a95", "label": "Schedule", "name": "schedule", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "message": "", "default_value": 0, "allow_in_bindings": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2bcb99d12f", "label": "Type", "name": "type", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "choices": {"fixed_date": "Fixed date", "date_range": "Date range", "week_days": "Week days"}, "default_value": "fixed_date", "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 1, "ui": 0, "ajax": 0, "placeholder": "", "create_options": 0, "save_options": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2d8d721c33", "label": "Date", "name": "date", "aria-label": "", "type": "date_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bcb99d12f", "operator": "==", "value": "fixed_date"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "display_format": "d/m/Y", "return_format": "Ymd", "first_day": 1, "allow_in_bindings": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2d8fef45be", "label": "", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bcb99d12f", "operator": "==", "value": "fixed_date"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "new_lines": "wpautop", "esc_html": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2d79d3d1ab", "label": "Date - From", "name": "date_from", "aria-label": "", "type": "date_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bcb99d12f", "operator": "==", "value": "date_range"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "display_format": "d/m/Y", "return_format": "Ymd", "first_day": 1, "allow_in_bindings": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2d8063d1ac", "label": "Date - To", "name": "date_to", "aria-label": "", "type": "date_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bcb99d12f", "operator": "==", "value": "date_range"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "display_format": "d/m/Y", "return_format": "Ymd", "first_day": 1, "allow_in_bindings": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2da2b35ecb", "label": "", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bcb99d12f", "operator": "==", "value": "date_range"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "message": "", "new_lines": "wpautop", "esc_html": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2db3ff1ec0", "label": "Week days", "name": "week_days", "aria-label": "", "type": "checkbox", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bcb99d12f", "operator": "==", "value": "week_days"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "75", "class": "", "id": ""}, "choices": {"Mon": "Monday", "Tue": "Tuesday", "Wed": "Wednesday", "Thu": "Thursday", "Fri": "Friday", "Sat": "Saturday", "Sun": "Sunday"}, "default_value": [], "return_format": "value", "allow_custom": 0, "allow_in_bindings": 0, "layout": "horizontal", "toggle": 1, "save_custom": 0, "custom_choice_button_text": "Add new choice", "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2bd389d130", "label": "Whole day", "name": "whole_day", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "message": "", "default_value": 1, "allow_in_bindings": 1, "ui_on_text": "", "ui_off_text": "", "ui": 1, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2d84ba776f", "label": "", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bd389d130", "operator": "==", "value": "1"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "75", "class": "", "id": ""}, "message": "", "new_lines": "wpautop", "esc_html": 0, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2bd7d9d131", "label": "Time - From", "name": "time_from", "aria-label": "", "type": "time_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bd389d130", "operator": "!=", "value": "1"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "display_format": "H:i:s", "return_format": "H:i:s", "allow_in_bindings": 1, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d2bd9b9d132", "label": "Time - To", "name": "time_to", "aria-label": "", "type": "time_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bd389d130", "operator": "!=", "value": "1"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "display_format": "H:i:s", "return_format": "H:i:s", "allow_in_bindings": 1, "parent_repeater": "field_67d2bc549d12d"}, {"key": "field_67d8542392d26", "label": "", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67d2bd389d130", "operator": "!=", "value": "1"}, {"field": "field_67d81b6978a95", "operator": "==", "value": "1"}]], "wrapper": {"width": "25", "class": "", "id": ""}, "message": "", "new_lines": "wpautop", "esc_html": 0, "parent_repeater": "field_67d2bc549d12d"}]}, {"key": "field_645bb3ffafa1f", "label": "<PERSON><PERSON><PERSON>", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_67e583e311049", "label": "Tickster Replace Event Details", "name": "tickster_replace_event_details", "aria-label": "", "type": "repeater", "instructions": "Links with those names will show on event detail page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_67e583e31104a", "label": "Las mer link", "name": "las_mer_link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "allow_in_bindings": 0, "parent_repeater": "field_67e583e311049"}, {"key": "field_67e584101104b", "label": "Which Event", "name": "which_event", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"ax9n5k5clpdnrrt": "29 March 2025 - STL-finaler med V75®", "t8euadcvv8lgbfz": "2 April 2025 - OnsdagsNöjet", "3076wa1hugh6w4f": "9 April 2025 - OnsdagsNöjet", "ryu8xkhfmm2b5az": "13 April 2025 - FörmiddagsTrav", "u6zrcur66nnll5u": "16 April 2025 - OnsdagsNöjet", "7nglucn3vxw1cbx": "23 April 2025 - OnsdagsNöjet", "4jt680teb7k14vk": "30 April 2025 - OnsdagsNöjet", "wz5bn4e5muw974v": "7 May 2025 - OnsdagsNöjet", "vgz63fpuf92jg8d": "9 May 2025 - FredagsTrav", "yb6kfr2cndng5j8": "11 May 2025 - PonnyTrav", "n56x6utu380gyuu": "14 May 2025 - OnsdagsNöjet", "y69l9wv557xkygy": "23 May 2025 - Elitloppsfredag", "2zx85065xhkkygn": "23 May 2025 - Elitauktion 2025", "d2ezh37clu861zr": "24 May 2025 - Elitloppslördag", "x6eg6tvxj7r2n0l": "25 May 2025 - Elitloppssöndag", "cvgygd52pf9vzaf": "2 June 2025 - SommarLunch", "51lhhp8eakjkdf3": "10 June 2025 - SommarTrav", "lkdwwwf3ncb474l": "15 June 2025 - PonnyTrav", "z96gcg44w7zczh1": "18 June 2025 - SommarTrav STL", "u8g0ne0n7xhf6bl": "24 June 2025 - SommarTrav", "py6u2famrppxlbp": "29 June 2025 - FörmiddagsTrav", "3t6djk7d1jxvnub": "1 July 2025 - SommarTrav", "57ffrn2wngzy2gc": "31 July 2025 - SommarTrav", "uffv3h28luaf0wl": "10 August 2025 - FörmiddagsTrav", "33e8lx5ddph7mrn": "12 August 2025 - SommarTrav", "t7ap3hvlafyg4v8": "20 August 2025 - Jubileumsdagen", "k10yhjc7v8yn402": "24 August 2025 - PonnyTrav", "za1xm8g81gevw2g": "27 August 2025 - OnsdagsNöjet", "6wlbleh4kf6ch1g": "3 September 2025 - OnsdagsNöjet", "lhua5wa7c1ar3xc": "10 September 2025 - OnsdagsNöjet", "5enarckmet5cvze": "12 September 2025 - FredagsTrav", "g1rrkr760hugnv3": "17 September 2025 - OnsdagsNöjet", "5cnmmbdrm55d29g": "21 September 2025 - FörmiddagsTrav", "zb9vlpxgu4azprb": "24 September 2025 - OnsdagsNöjet", "fkyx8c2e2n4baf7": "28 September 2025 - Kriterieuttagningar med V75®", "maru2gyjwgrpn66": "1 October 2025 - OnsdagsNöjet", "eb8jrxenyadj2h1": "8 October 2025 - OnsdagsNöjet", "kl4cuylprzv369d": "10 October 2025 - LunchTrav", "92bheg137amwj7j": "11 October 2025 - SuperLördag", "152cxpj430hyypp": "22 October 2025 - OnsdagsNöjet", "r71nkz02u33mp2z": "26 October 2025 - PonnyTrav", "wu33rc1xubt8trx": "29 October 2025 - OnsdagsNöjet", "689gvu36nxkkhfb": "2 November 2025 - Breeders' Crown semifinaler med V75®", "chc92gyz5hrhz1u": "5 November 2025 - OnsdagsNöjet", "5ybtazpnc8w4uw1": "12 November 2025 - OnsdagsNöjet", "ryat47b94vmz5h4": "19 November 2025 - OnsdagsNöjet", "jjnd8z9kpyhz2hc": "26 November 2025 - OnsdagsNöjet", "gh36641nbd086m4": "29 November 2025 - STL med V75®", "5zxxrb9zzangrxz": "3 December 2025 - OnsdagsNöjet", "wwynylav9depk5f": "10 December 2025 - OnsdagsNöjet", "ptnh91p27k2zm7l": "17 December 2025 - OnsdagsNöjet", "rkp0nyh77utjv52": "26 December 2025 - STL-finaler med V75®"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0, "placeholder": "", "parent_repeater": "field_67e583e311049"}]}, {"key": "field_645be08e445f1", "label": "Tickster Package Names", "name": "tickster_package_names", "aria-label": "", "type": "repeater", "instructions": "Links with those names will show on event detail page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_645be0aa445f2", "label": "Name", "name": "name", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_645be08e445f1"}]}, {"key": "field_64b7c5c7af4e7", "label": "Header", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_67b453d81994a", "label": "Link to <PERSON>jure<PERSON> He<PERSON>g", "name": "enable_link_to_djurens_helg", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "allow_in_bindings": 1, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_64b7c5cfaf4e8", "label": "Display Custom Logo", "name": "custom_logo", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_64b7c5f1af4e9", "label": "Custom Logo Name", "name": "custom_logo_name", "aria-label": "", "type": "text", "instructions": "Name of the SVG file located in /assets/images/ path in theme folder.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_64b7c79563f89", "label": "Custom Logo Width", "name": "custom_logo_width", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": 100, "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": "px"}, {"key": "field_65265fb35b71a", "label": "Display Countdown", "name": "countdown", "aria-label": "", "type": "true_false", "instructions": "Display countdown instead of <PERSON> <PERSON><PERSON> in top right corner", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_65265ff15b71b", "label": "Countdown Date", "name": "countdown_date", "aria-label": "", "type": "date_time_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_65265fb35b71a", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "display_format": "F j, Y H:i", "return_format": "Y-m-d H:i:s", "first_day": 1}, {"key": "field_6526a1a8b2473", "label": "Countdown Link", "name": "countdown_link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_65265fb35b71a", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_63d01d3dc1201", "label": "Footer", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_643679b2be0bf", "label": "About Solvalla Text", "name": "about_solvalla", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 0}, {"key": "field_6436798dbe0be", "label": "About Games Text", "name": "about_games", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 0}, {"key": "field_643679c7be0c0", "label": "Contact Us Text", "name": "contact_us", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "34", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 0}, {"key": "field_641ac683e2fea", "label": "Podcast", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_681e1a3c083df", "label": "Image Changes", "name": "images_changes", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_681e1c346c17a", "label": "Episode ID", "name": "libsyn_episode_id", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_681e1a3c083df"}, {"key": "field_681e1b37083e2", "label": "Libsyn Image URL", "name": "libsyn_image_url", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "67", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_681e1a3c083df"}]}, {"key": "field_642ff18432159", "label": "Podcast Image", "name": "podcast_image", "aria-label": "", "type": "image", "instructions": "This image is displayed in the podcast player", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}, {"key": "field_641ac696e2feb", "label": "Spotify Link", "name": "spotify", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_641ac6d6e2fee", "label": "Podbean Link", "name": "podbean", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_641ac6e8e2fef", "label": "Google Podcasts Link", "name": "google_podcasts", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_641ac702e2ff0", "label": "Apple iTunes Link", "name": "apple_itunes", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}], "location": [[{"param": "options_page", "operator": "==", "value": "theme-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1746803908}