{"key": "group_648c216f81d9b", "title": "Custom Slide", "fields": [{"key": "field_648c2256b7788", "label": "Desktop Image", "name": "image", "aria-label": "", "type": "image_aspect_ratio_crop", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "crop_type": "aspect_ratio", "aspect_ratio_width": 1920, "aspect_ratio_height": 1080, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_64994835b80bd", "label": "Tablet Image", "name": "image_tablet", "aria-label": "", "type": "image_aspect_ratio_crop", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "crop_type": "aspect_ratio", "aspect_ratio_width": 991, "aspect_ratio_height": 800, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_64994843b80be", "label": "Large Mobile Image", "name": "image_mobile_large", "aria-label": "", "type": "image_aspect_ratio_crop", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "crop_type": "aspect_ratio", "aspect_ratio_width": 680, "aspect_ratio_height": 880, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_649948ffb80bf", "label": "Mobile Image", "name": "image_mobile", "aria-label": "", "type": "image_aspect_ratio_crop", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "crop_type": "aspect_ratio", "aspect_ratio_width": 600, "aspect_ratio_height": 1200, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_656f438fb1bbd", "label": "Video link (MP4)", "name": "video_link", "aria-label": "", "type": "url", "instructions": "If you place a video link here, it will override the image settings and display a video. <br>Try to keep the filesize as small as possible (below 10MB). We recommend using <a href=\"https://www.freeconvert.com/video-converter\" target=\"_blank\">this tool</a> - choose to convert the file to MP4 (even though the source is also MP4 file). It will compress the video.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "allow_in_bindings": 1, "placeholder": ""}, {"key": "field_6808d757779a0", "label": "Video link (WebM)", "name": "video_link_webm", "aria-label": "", "type": "url", "instructions": "If you place a video link here, it will override the image settings and display a video. <br>This is a modern video format that will be prioritized before MP4 - it usually has half the size with the same quality compared to MP4. We recommend using <a href=\"https://www.freeconvert.com/video-converter\" target=\"_blank\">this tool</a> - choose to convert the file to WEBM here.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "allow_in_bindings": 1, "placeholder": ""}, {"key": "field_681dd56ab79bd", "label": "Add Black Overlay", "name": "add_overlay", "aria-label": "", "type": "true_false", "instructions": "Applies a left-to-right (or right-to-left, based on text alignment) black-to-transparent gradient overlay to enhance contrast between light backgrounds and slider text", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "allow_in_bindings": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_681dd515b79bc", "label": "Overlay Opacity", "name": "overlay_opacity", "aria-label": "", "type": "range", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_681dd56ab79bd", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": 60, "min": 0, "max": 100, "allow_in_bindings": 0, "step": 1, "prepend": "", "append": "%"}, {"key": "field_64994c1436a40", "label": "Text Position", "name": "text_position", "aria-label": "", "type": "button_group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"left": "Left", "right": "Right"}, "default_value": "left", "return_format": "value", "allow_null": 0, "layout": "horizontal"}, {"key": "field_648c218509dff", "label": "Small Text", "name": "small_text", "aria-label": "", "type": "text", "instructions": "Small text above the heading", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_681de8538844f", "label": "Small Text Font Size", "name": "small_text_font_size", "aria-label": "", "type": "number", "instructions": "10 is default size", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": 10, "min": "", "max": "", "allow_in_bindings": 0, "placeholder": "", "step": 1, "prepend": "", "append": ""}, {"key": "field_648c216f09dfe", "label": "Heading", "name": "heading", "aria-label": "", "type": "text", "instructions": "Slide heading", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_681de7a88844e", "label": "Heading Font Size", "name": "heading_font_size", "aria-label": "", "type": "number", "instructions": "10 is default size", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": 10, "min": "", "max": "", "allow_in_bindings": 0, "placeholder": "", "step": 1, "prepend": "", "append": ""}, {"key": "field_653ba8a5bb5ab", "label": "Small Text Color", "name": "description_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "", "enable_opacity": 0, "return_format": "string", "allow_in_bindings": 1}, {"key": "field_653ba88cbb5aa", "label": "Heading Color", "name": "title_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "", "enable_opacity": 0, "return_format": "string", "allow_in_bindings": 1}, {"key": "field_681dedced9afc", "label": "", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "new_lines": "wpautop", "esc_html": 0}, {"key": "field_648c21ac09e00", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "allow_in_bindings": 1}, {"key": "field_64b7bcf229ae6", "label": "Button #2", "name": "button2", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "allow_in_bindings": 1}, {"key": "field_653ba8edbb5ae", "label": "Button Text Color", "name": "button_text_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "#000000", "enable_opacity": 0, "return_format": "string", "allow_in_bindings": 1}, {"key": "field_653ba8bcbb5ad", "label": "<PERSON>ton Background", "name": "button_background", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "#ffffff", "enable_opacity": 0, "return_format": "string", "allow_in_bindings": 1}, {"key": "field_653ba8fcbb5af", "label": "Button Border Hover Color", "name": "button_border_hover_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "#ffffff", "enable_opacity": 0, "return_format": "string", "allow_in_bindings": 1}, {"key": "field_653ba92bbb5b0", "label": "Button Text Hover Color", "name": "button_text_hover_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "default_value": "#ffffff", "enable_opacity": 0, "return_format": "string", "allow_in_bindings": 1}, {"key": "field_659fd45026cb6", "label": "Slide Autoplay Delay (in ms)", "name": "slide_autoplay_delay", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": 7000, "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "slides"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1746796638}