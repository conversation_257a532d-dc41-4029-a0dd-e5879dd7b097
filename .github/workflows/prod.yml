on: 
  push:
    branches:
      - main
name: 🚀 Deploy plugin to PROD on push
jobs:
  web-deploy:
    name: 🎉 Deploy to PROD
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v3
    
    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.4
      with:
        server: 13.51.31.108
        username: solvalla_ftp
        password: ${{ secrets.ftp_password }}
        server-dir: /web/wp-content/plugins/tickster-events/