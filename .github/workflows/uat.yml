on: 
  push:
    branches:
      - uat
name: 🚀 Deploy plugin to UAT on push
jobs:
  web-deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v3
    
    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.4
      with:
        server: 13.50.1.105
        username: vision<PERSON>_solvalla
        password: ${{ secrets.uat_ftp_password }}
        server-dir: /web/wp-content/plugins/tickster-events/