on: 
  push:
    branches:
      - uat
name: 🚀 Deploy website to UAT on push
jobs:
  web-deploy:
    name: 🎉 Deploy to UAT
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.0'

    - name: Install Composer dependencies
      run: composer install --no-dev --optimize-autoloader

    - name: Use Node.js 16
      uses: actions/setup-node@v2
      with:
        node-version: '16'
      
    - name: 🔨 Build Project
      run: |
        npm install
        npm run build

    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.4
      with:
        server: 13.50.1.105
        username: visionmate_solvalla
        password: ${{ secrets.uat_ftp_password }}
        server-dir: /web/wp-content/themes/solvalla/