#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Sol<PERSON>la\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-12 13:15+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.8.0; wp-6.8.1; php-8.1.32\n"
"X-Domain: solvalla"

#: templates/views/404.twig:9
msgid "404 Sida inte hittad"
msgstr ""

#: lib/taxonomies.php:20
msgid "Add New Group"
msgstr ""

#: lib/taxonomies.php:45 lib/taxonomies.php:74
msgid "Add New WhichEvent"
msgstr ""

#: templates/views/single-trainers.twig:47
msgid "Adress"
msgstr ""

#: lib/taxonomies.php:15
msgid "All Groups"
msgstr ""

#: lib/taxonomies.php:40 lib/taxonomies.php:69
msgid "All WhichEvent"
msgstr ""

#: templates/views/single-podcast.twig:104
msgid "Alla avsnitt"
msgstr ""

#: templates/views/single-podcast.twig:83
msgid "Andra avsnitt"
msgstr ""

#: templates/views/single-invited-horses.twig:75 templates/views/single.twig:67
msgid "Andra nyheter"
msgstr ""

#: templates/views/single-winners.twig:65
msgid "Andra tidigare vinnare"
msgstr ""

#: templates/views/partials/restaurant.twig:21
msgid "Boka här"
msgstr ""

#: lib/meta.php:25
msgid "Click Count"
msgstr ""

#. Description of the theme
msgid "Custom theme for Solvalla"
msgstr ""

#: templates/views/partials/countdown.twig:3
msgid "Dagar"
msgstr ""

#: templates/views/single-event.twig:28 templates/views/single-event.twig:154
#: templates/blocks/block-tickster-calendar/block-tickster-calendar.twig:69
msgid "Datum"
msgstr ""

#: templates/views/footer.twig:75
msgid "Designad och utvecklad av"
msgstr ""

#: lib/taxonomies.php:18
msgid "Edit Group"
msgstr ""

#: lib/taxonomies.php:43 lib/taxonomies.php:72
msgid "Edit WhichEvent"
msgstr ""

#: templates/views/partials/countdown.twig:10
msgid "Elitloppet är live!"
msgstr ""

#: templates/views/single-event.twig:36 templates/views/single-event.twig:162
#: templates/blocks/block-tickster-calendar/block-tickster-calendar.twig:76
msgid "Entreérna öppnar"
msgstr ""

#: lib/meta.php:72
msgid "Event Date"
msgstr ""

#: templates/views/single-password.twig:34
msgid "Felaktigt lösenord. Vänligen försök igen."
msgstr ""

#: functions.php:962
msgid "Fetch Elitloppet 2025"
msgstr ""

#: functions.php:926
msgid "Fetch Libsyn Podcasts"
msgstr ""

#: functions.php:950
msgid "Fetch Solvalla 2024"
msgstr ""

#: functions.php:974
msgid "Fetch Solvalla 2025"
msgstr ""

#: functions.php:938
msgid "Fetch Tickster Events"
msgstr ""

#: functions.php:914
msgid "Fetch YouTube Videos"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:40
msgid "Filtrera efter möte"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:33
msgid "Filtrera efter år"
msgstr ""

#: templates/views/single-event.twig:44 templates/views/single-event.twig:170
msgid "Första start"
msgstr ""

#: lib/taxonomies.php:22
msgid "Groups"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:70
msgid "har inte startat ännu"
msgstr ""

#: templates/views/single-trainers.twig:31
#: templates/views/single-result-photos.twig:20
msgid "Hem"
msgstr ""

#. Author URI of the theme
msgid "https://www.visionmate.se/"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:54
msgid "Häst"
msgstr ""

#: archive-invited-horses.php:9 taxonomy-which-event-invited-horses.php:19
msgid "Inbjudna Hästar"
msgstr ""

#: templates/views/single-event.twig:37 templates/views/single-event.twig:45
#: templates/views/single-event.twig:163 templates/views/single-event.twig:171
msgid "Kl."
msgstr ""

#: templates/views/footer.twig:64
msgid "Kontakta oss"
msgstr ""

#: templates/views/single-event.twig:71 templates/views/single-event.twig:122
#: templates/views/single-event.twig:209
#: templates/blocks/block-tickster-calendar/block-tickster-calendar.twig:111
#: templates/blocks/block-slider/block-slider.twig:111
#: templates/blocks/block-slider-custom/block-slider-custom.twig:110
#: templates/views/partials/card-event.twig:54
msgid "Köp biljetter"
msgstr ""

#: templates/blocks/block-libsyn-podcast/block-libsyn-podcast.twig:30
msgid "Ladda mer"
msgstr ""

#: templates/views/partials/card-program.twig:42
msgid "Ladda ner"
msgstr ""

#: templates/blocks/block-youtube/block-youtube.twig:34
#: templates/blocks/block-youtube-live/block-youtube-live.twig:23
msgid "Live"
msgstr ""

#: templates/views/partials/podcast-episode.twig:35
msgid "Lyssna på oss på"
msgstr ""

#: templates/views/archive-invited-horses.twig:70
#: templates/blocks/block-tickster-calendar/block-tickster-calendar.twig:125
#: templates/blocks/block-invited-horses/block-invited-horses.twig:68
#: templates/blocks/block-slider/block-slider.twig:123
#: templates/blocks/block-slider-custom/block-slider-custom.twig:122
#: templates/views/partials/card-event.twig:68
msgid "Läs mer"
msgstr ""

#: templates/views/single-password.twig:38
msgid "Lösenord:"
msgstr ""

#: templates/views/single-trainers.twig:62
msgid "Mail"
msgstr ""

#: templates/views/single-event.twig:276
msgid "Mat & Dryck"
msgstr ""

#: templates/views/partials/countdown.twig:5
msgid "Minuter"
msgstr ""

#: templates/views/single-trainers.twig:52
msgid "Mobil"
msgstr ""

#: archive.php:58 templates/views/single-result-photos.twig:21
msgid "Målfoton"
msgstr ""

#: archive.php:56
msgid "Målfoton - Sida %d"
msgstr ""

#: lib/taxonomies.php:21
msgid "New Group Name"
msgstr ""

#: lib/taxonomies.php:46 lib/taxonomies.php:75
msgid "New WhichEvent Name"
msgstr ""

#: archive-news.php:15
msgid "Nyheter"
msgstr ""

#: archive-news.php:13
msgid "Nyheter - Sida %d"
msgstr ""

#. Name of the template
#: nyheter-rss-feed.php:2
msgid "Nyheter RSS Feed"
msgstr ""

#: templates/blocks/block-libsyn-podcast-list/block-libsyn-podcast-list.twig:33
msgid "Nästa avsnitt"
msgstr ""

#: templates/views/footer.twig:56
msgid "Om spel"
msgstr ""

#. Name of the template
#: page-sidebar.php:2
msgid "Page with sidebar"
msgstr ""

#: lib/taxonomies.php:16
msgid "Parent Group"
msgstr ""

#: lib/taxonomies.php:17
msgid "Parent Group:"
msgstr ""

#: lib/taxonomies.php:41 lib/taxonomies.php:70
msgid "Parent WhichEvent"
msgstr ""

#: lib/taxonomies.php:42 lib/taxonomies.php:71
msgid "Parent WhichEvent:"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:53
msgid "Plats"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:55
msgid "Poäng"
msgstr ""

#: templates/views/single-event.twig:52 templates/views/single-event.twig:178
msgid "Priser"
msgstr ""

#: templates/views/archive-trainers.twig:9
#: templates/views/single-trainers.twig:32
msgid "Proffsstränare"
msgstr ""

#: lib/taxonomies.php:14
msgid "Search Groups"
msgstr ""

#: lib/taxonomies.php:39 lib/taxonomies.php:68
msgid "Search WhichEvent"
msgstr ""

#: templates/views/partials/countdown.twig:6
msgid "Sekunder"
msgstr ""

#: taxonomy-which-event-invited-horses.php:22
#: taxonomy-which-event-winners.php:22
msgid "Sida"
msgstr ""

#: templates/views/single-password.twig:42
msgid "Skicka"
msgstr ""

#: templates/views/single-password.twig:8
msgid "Skyddat innehåll"
msgstr ""

#: templates/blocks/block-slider/block-slider.twig:82
#: templates/blocks/block-slider-custom/block-slider-custom.twig:81
msgid "SOLVALLA"
msgstr ""

#. Name of the theme
msgid "Solvalla"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:44
msgid "Solvallaserien 220"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:45
msgid "Solvallaserien 420"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:46
msgid "Solvallaserien 720"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:56
msgid "Starter"
msgstr ""

#: templates/views/single-trainers.twig:80
msgid "Statistik (ST)"
msgstr ""

#: templates/views/single-event.twig:76 templates/views/single-event.twig:127
#: templates/views/single-event.twig:214
#: templates/blocks/block-tickster-calendar/block-tickster-calendar.twig:117
#: templates/blocks/block-slider/block-slider.twig:116
#: templates/blocks/block-slider-custom/block-slider-custom.twig:115
#: templates/views/partials/card-event.twig:60
msgid "Stängt för försäljning"
msgstr ""

#: lib/taxonomies.php:12
msgctxt "taxonomy general name"
msgid "Groups"
msgstr ""

#: lib/taxonomies.php:37 lib/taxonomies.php:66
msgctxt "taxonomy general name"
msgid "WhichEvent"
msgstr ""

#: lib/taxonomies.php:13
msgctxt "taxonomy singular name"
msgid "Group"
msgstr ""

#: lib/taxonomies.php:38 lib/taxonomies.php:67
msgctxt "taxonomy singular name"
msgid "WhichEvent"
msgstr ""

#: templates/views/single-trainers.twig:57
msgid "Telefon"
msgstr ""

#: templates/views/partials/countdown.twig:4
msgid "Timmar"
msgstr ""

#: templates/blocks/block-leaderboard/block-leaderboard.twig:57
msgid "Tränare"
msgstr ""

#: templates/views/single-trainers.twig:87
msgid "Träningslista (ST)"
msgstr ""

#: templates/views/404.twig:34
msgid "Tyvärr hittade vi inte det du letar efter."
msgstr ""

#: templates/views/base.twig:176
msgid "Tyvärr, inget innehåll"
msgstr ""

#: lib/taxonomies.php:19
msgid "Update Group"
msgstr ""

#: lib/taxonomies.php:44 lib/taxonomies.php:73
msgid "Update WhichEvent"
msgstr ""

#: lib/meta.php:26
msgid "View Count"
msgstr ""

#: archive-winners.php:10 taxonomy-which-event-winners.php:19
msgid "Vinnare"
msgstr ""

#. Author of the theme
msgid "Visionmate"
msgstr ""

#: templates/views/single-trainers.twig:67
msgid "Webb"
msgstr ""

#: lib/taxonomies.php:47 lib/taxonomies.php:76
msgid "WhichEvent"
msgstr ""
