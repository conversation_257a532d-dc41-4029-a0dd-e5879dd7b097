<?php
/**
 * The template for displaying all pages.
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site will use a
 * different template.
 *
 * To generate specific templates for your pages you can use:
 * /mytheme/views/page-mypage.twig
 * (which will still route through this PHP file)
 * OR
 * /mytheme/page-mypage.php
 * (in which case you'll want to duplicate this file and save to the above path)
 *
 * Methods for TimberHelper can be found in the /lib sub-directory
 *
 * @package  WordPress
 * @subpackage  Timber
 * @since    Timber 0.1
 */

$context = Timber::get_context();
$post = new TimberPost();
$context['post'] = $post;

// Add page ancestors for breadcrumbs
$ancestors = get_post_ancestors( $post->ID );
$breadcrumb_pages = [];
if ( $ancestors ) {
    // Reverse the array to get correct order (top parent first)
    $ancestors = array_reverse( $ancestors );
    foreach ( $ancestors as $ancestor_id ) {
        $ancestor_post = new TimberPost( $ancestor_id );
        $breadcrumb_pages[] = $ancestor_post;
    }
}
$context['breadcrumb_pages'] = $breadcrumb_pages;

// Add ACF fields to context.
$context['fields'] = [
    'desktop_image'     => get_field( 'desktop_image' ),
    'mobile_image'      => get_field( 'mobile_image' ),
];

$blocks = parse_blocks($post->post_content);
$context['has_slider'] = has_custom_slider_block($blocks);

if ( post_password_required( $post->ID ) ) {
    Timber::render( 'single-password.twig', $context );
} else {
    Timber::render( array( 'page-' . $post->post_name . '.twig', 'page.twig' ), $context );
}
