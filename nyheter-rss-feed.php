<?php
/**
 * Template Name: Nyheter RSS Feed
 */

header('Content-Type: application/rss+xml; charset=UTF-8');

echo '<?xml version="1.0" encoding="UTF-8" ?>';
?>
<rss version="2.0">
    <channel>
        <title><?php bloginfo('name'); ?> - Nyheter</title>
        <link><?php bloginfo('url'); ?></link>
        <description><?php bloginfo('description'); ?></description>
        <language>sv-SE</language>
        <pubDate><?php echo date('r'); ?></pubDate>
        <lastBuildDate><?php echo date('r'); ?></lastBuildDate>

        <?php
        $args = [
            'post_type' => 'post',
            'posts_per_page' => 100,
        ];
        $query = new WP_Query($args);

        if ($query->have_posts()) :
            while ($query->have_posts()) : $query->the_post();
        ?>
                <item>
                    <title><?php the_title_rss(); ?></title>
                    <link><?php the_permalink_rss(); ?></link>
                    <description><![CDATA[
                        <?php if (has_excerpt()) : ?>
                            <p><?php the_excerpt_rss(); ?></p>
                        <?php endif; ?>
                        <p><?php the_content_feed('rss2'); ?></p>
                        <?php if (has_post_thumbnail()) : ?>
                            <p><?php the_post_thumbnail('medium'); ?></p>
                        <?php endif; ?>
                    ]]></description>
                    <pubDate><?php echo get_the_date(DATE_RSS); ?></pubDate>
                    <guid><?php the_guid(); ?></guid>
                </item>
        <?php
            endwhile;
        endif;
        wp_reset_postdata();
        ?>
    </channel>
</rss>