<?php

/**
 * The template for displaying single podcast episode from Libsyn.
 */

// If Tickster events plugin is not activated, send user to 404 template.
if (!class_exists('Libsyn_Podcast_Public')) {
    Timber::render('404.twig', $context);
    exit();
}

$context = Timber::get_context();

// Put episode object to context.
$context['episode'] = Libsyn_Podcast_Public::get_episode_details_by_slug($params['podcast_slug']);

// Overwrite episode image if there is rule in options
$image_changes = get_field('images_changes', 'option');
foreach ($image_changes as $image_change) {
    $libsyn_episode_id = $image_change['libsyn_episode_id'];
    $libsyn_image_url = $image_change['libsyn_image_url'];
    if ($libsyn_episode_id && $libsyn_image_url && $context['episode'] && $context['episode']->id == $libsyn_episode_id) {
        $context['episode']->image_url = $libsyn_image_url;
    }
}

// Cache external images locally
$image_helper = new Timber\ImageHelper();
$image = $image_helper::img_to_jpg($context['episode']->image_url);
$context['episode']->local_image_url = $image_helper::resize($image, 748);

// Remove default placeholder
$image_obj = new Timber\Image($context['episode']->local_image_url);
if ($image_obj->aspect() <= 1) {
    try {
        $image = $image_helper::img_to_jpg($context['episode']->image_url, $bghex = '#FFFFFF', $force = true);
        $context['episode']->local_image_url = $image_helper::resize($image, 748);
    } catch (\Throwable $th) {
        // echo 'Error: ' . $th->getMessage();
    }
    $context['episode']->local_image_url = '';
}

// Podcast image
$podcast_image = get_field('podcast_image', 'option');
if ($podcast_image) {
    $context['podcast_image'] = $image_helper::resize($podcast_image['url'], 240, 240);
}

// Links for podcast
$context['links'] = [
    'Spotify' => get_field('spotify', 'option'),
    'Podbean' => get_field('podbean', 'option'),
    'Google Podcasts' => get_field('google_podcasts', 'option'),
    'Apple iTunes' => get_field('apple_itunes', 'option'),
];


// Get other podcast episodes from database
if (class_exists('Libsyn_Podcast_Public')) {
    $context['other_episodes'] = Libsyn_Podcast_Public::get_episodes(3, 1);
} else {
    $context['other_episodes'] = [];
}

foreach ($context['other_episodes'] as $key => $episode) {

    // Unset the current episode
    if ($episode->id === $context['episode']->id) {
        unset($context['other_episodes'][$key]);
        continue;
    }

    // Overwrite episode image if there is rule in options
    foreach ($image_changes as $image_change) {
        $libsyn_episode_id = $image_change['libsyn_episode_id'];
        $libsyn_image_url = $image_change['libsyn_image_url'];
        if ($libsyn_episode_id && $libsyn_image_url && $episode && $episode->id == $libsyn_episode_id) {
            $episode->image_url = $libsyn_image_url;
        }
    }

    // Cache external images locally
    $image_helper = new Timber\ImageHelper();
    $image = $image_helper::img_to_jpg($episode->image_url);
    $context['other_episodes'][$key]->thumbnail_url = $image_helper::resize($image, 748);

    // Remove default placeholder
    $image_obj = new Timber\Image($context['other_episodes'][$key]->thumbnail_url);
    if ($image_obj->aspect() <= 1) {
        try {
            $image = $image_helper::img_to_jpg($episode->image_url, $bghex = '#FFFFFF', $force = true);
            $context['other_episodes'][$key]->thumbnail_url = $image_helper::resize($image, 748);
        } catch (\Throwable $th) {
            // echo 'Error: ' . $th->getMessage();
        }
        $context['other_episodes'][$key]->thumbnail_url = '';
    }
}

// Archive url for breadcrumbs
$context['archive_title'] = __('Solvalla Podcast', 'solvalla');
$context['archive_url'] = get_site_url() . '/podcast/';

// add metadata to wp_head (wp_title and meta description) :
add_action('wp_head', function () use ($context) {
    echo '<meta name="description" content="' . strip_tags($context['episode']->description_text) . '" />';
}, 1);
$context['wp_title'] = $context['episode']->title;

Timber::render('single-podcast.twig', $context);
