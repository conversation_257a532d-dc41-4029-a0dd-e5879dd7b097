const ExtraWatchWebpackPlugin = require('extra-watch-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');
const webpack = require('webpack');
const CopyPlugin = require("copy-webpack-plugin");
const MODE = 'production';

module.exports = {
  mode: MODE,

  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
      },
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env'],
          },
        },
      },
    ],
  },
  resolve: {
    extensions: ['.ts', '.js'],
  },
  plugins: [
    new webpack.ProvidePlugin({
      $: 'jquery',
      jQuery: 'jquery',
    }),
    new ESLintPlugin({
      extensions: ['.js', '.ts'],
    }),
    new ExtraWatchWebpackPlugin({
      dirs: [ 
        'templates' 
      ]
    }),
    new CopyPlugin({
      patterns: [
        { from: 'node_modules/plyr/dist/plyr.min.js', to: 'plyr.js' },
        { from: 'node_modules/smartcrop/smartcrop.js', to: 'smartcrop.js' },
        { from: 'node_modules/plyr/dist/plyr.css', to: '../css/plyr.css' },
        { from: 'node_modules/magnific-popup/dist/magnific-popup.css', to: '../css/magnific-popup.css' },
        { from: 'node_modules/lightbox2/dist/css/lightbox.min.css', to: '../css/lightbox.css' },
        { from: 'node_modules/lightbox2/dist/images/', to: '../images/' },
      ],
    }),
  ],
};
