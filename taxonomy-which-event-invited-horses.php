<?php

/**
 * The template for displaying events from Tickster.
 */

$context = Timber::get_context();

global $wp;

$paged = (get_query_var('paged')) ? get_query_var('paged') : '';

$queried_object = get_queried_object();

if (!$queried_object->name) {
    Timber::render('404.twig', $context);
}

$context['title'] = __('Inbjudna Hästar', 'solvalla') . ' – ' . $queried_object->name;

if ($paged) {
    $context['title'] = $context['title'] . ' – ' . __('Sida', 'solvalla') . ' ' . $paged;
}

$context['posts'] = new Timber\PostQuery();


// $context['posts'] = new Timber\PostQuery();
// $context['posts'] = Timber::get_posts([
//     'post_type'   => 'invited-horses',
//     'numberposts' => 12,
//     'tax_query' => [
//         [
//             'taxonomy' => 'which-event-invited-horses',
//             'field' => 'slug',
//             'terms' => $current_which_event // Where term_id of Term 1 is "1".
//         ]
//     ]
// ]);

foreach ($context['posts'] as $key => $value) {
    $context['posts'][$key]->fields = [];
    foreach (get_field_objects($value->ID) as $field_key => $field_array) {
        foreach ($field_array as $field_param_name => $field_param_value) {
            if ($field_param_name == 'label' || $field_param_name == 'value' || $field_param_name == 'name') {
                $context['posts'][$key]->fields[$field_key][$field_param_name] = $field_param_value;
            }
        }
    }
}

// Archive url for breadcrumbs
$context['which_event_archive'] = true; 
$context['archive_title'] = __( 'Inbjudna Hästar', 'solvalla' );
$context['archive_url'] = get_site_url() . '/inbjudna-hastar/';

Timber::render('archive-invited-horses.twig', $context);
