<?php

/**
 * The template for displaying single event from <PERSON>icks<PERSON>.
 * Most information is taken from <PERSON><PERSON><PERSON>, but event description, price range are defined in a WP custom post.
 */

// If Tickster events plugin is not activated, send user to 404 template.
if (!class_exists('Tickster_Events_Public')) {
    Timber::render('404.twig', $context);
    exit();
}

$context = Timber::get_context();

// Determine if this is racing/non-racing event.
if ($params['event_type'] == 'event') {
    $tag_to_lookup = 'Event';
} else {
    $tag_to_lookup = 'Racing';
}
$events = Tickster_Events_Public::get_events_by_slug($params['event_slug'], $tag_to_lookup);

// Get Tickster event details from database.
if (empty($events)) {
    Timber::render('404.twig', $context);
    exit();
}

// Put event object to context.
$context['event'] = $events[0];

// Cache external images locally
$image_helper = new Timber\ImageHelper();
$context['event']->local_image_url = @$image_helper::resize($context['event']->image_url, 1920, 1080);

// Find WP post object that connects with this event.
$connected_post = get_posts([
    'numberposts'   => 1,
    'post_type'     => 'eventtexts',
    'meta_key'      => 'tickster_event',
    'meta_value'    => $events[0]->id,
    'meta_compare'  => 'LIKE'
]);
$context['post'] = [];
if ($connected_post) {
    $context['post'] = $connected_post[0];
    $context['blocks'] = parse_blocks($connected_post[0]->post_content);
}

// Get restaurants data.
$prices = [];
if ($context['event']->packages) {
    $allowed_packages = get_field('tickster_package_names', 'option');
    $allowed_packages = ($allowed_packages && is_array($allowed_packages) && !empty($allowed_packages) && array_key_exists('name', $allowed_packages[0])) ? array_column($allowed_packages, 'name') : [];
    $context['allowed_packages'] = $allowed_packages;
    foreach ($context['event']->packages as $key => $package) {

        if (in_array($package['title'], $allowed_packages)) {
            $prices[] = $package['price_min'];
            $prices[] = $package['price_max'];
        }

        $description = htmlspecialchars_decode($package['description']);

        // Get link elements from restaurant description and save it to separate array.
        $dom = new DOMDocument();
        $dom->loadHTML($description);
        $links = $dom->getElementsByTagName('a');
        $context['event']->packages[$key]['menu_links'] = [];
        foreach ($links as $link) {
            $context['event']->packages[$key]['menu_links'][] = [
                'href' => $link->getAttribute('href'),
                'text' => $link->nodeValue
            ];

            // Remove link elements from restaurant description.
            $link->parentNode->removeChild($link);
        }

        // Save modified restaurant description to context.
        $description = $dom->saveHTML();
        $context['event']->packages[$key]['description'] = $description;

        // Cache external images locally
        $context['event']->packages[$key]['local_image'] = @$image_helper::resize($package['image'], 520);
    }
}
$price_min = ($prices) ? min($prices) : '';
$price_max = ($prices) ? max($prices) : '';
if ($price_min !== '') {
    $context['event']->price = ($price_min === $price_max) ? $price_min . ' kr' : $price_min . ' - ' . $price_max . ' kr';
} else {
    $context['event']->price = '';
}

// Post type slug for breadcrumbs
$context['calendar_url'] = get_permalink(186);

// add metadata to wp_head (wp_title and meta description) :
add_action('wp_head', function () use ($context) {
    echo '<meta name="description" content="' . strip_tags($context['event']->event_description) . '" />';
}, 1);
$event_date = (isset($context['event']->event_date)) ? date('j F Y', strtotime($context['event']->event_date)) : '';
$context['wp_title'] = $context['event']->event_name;
if ($event_date) {
    $context['wp_title'] .= ' - ' . $event_date;
}

Timber::render('single-event.twig', $context);
