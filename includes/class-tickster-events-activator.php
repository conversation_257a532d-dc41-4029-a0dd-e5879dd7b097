<?php

/**
 * Fired during plugin activation.
 */
class Tickster_Events_Activator {

	/**
	 * Run on plugin activation.
	 */
	public static function activate() {

		// Create database table
		global $wpdb;

		$table_name = $wpdb->prefix . 'tickster_events';
		$charset_collate = $wpdb->get_charset_collate();

		$sql = "CREATE TABLE IF NOT EXISTS $table_name (
			id varchar(25) NOT NULL,
			slug text NOT NULL,
			event_name text NOT NULL,
			event_date datetime NOT NULL,
			event_end datetime NOT NULL,
			event_state varchar(255) NOT NULL,
			stock_status varchar(255) NOT NULL,
			doors_open datetime NOT NULL,
			event_description text NOT NULL,
			image_url varchar(255) NOT NULL,
			thumbnail_url varchar(255) NOT NULL,
			tags text NOT NULL,
			links text NOT NULL,
			packages text NOT NULL,
			tickets_url varchar(255) NOT NULL,
			PRIMARY KEY (id)
		) $charset_collate;";

		require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
		dbDelta($sql);

		//Add column event_end, used for unpublishing events, this was missing in previous plugin versions
		$column_name = 'event_end';
		$create_ddl = `ALTER TABLE {$table_name} ADD COLUMN {$column_name} datetime NOT NULL AFTER event_date;`;
		maybe_add_column($table_name, $column_name, $create_ddl);
	}
}
