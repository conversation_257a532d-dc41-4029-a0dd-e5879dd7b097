<?php

/**
 * The file that defines the core plugin class
 */
class Tickster_Events {

	/**
	 * The loader that's responsible for maintaining and registering all hooks.
	 */
	protected $loader;

	/**
	 * Define the core functionality of the plugin.
	 */
	public function __construct() {
		$this->load_dependencies();
		$this->define_hooks();
	}

	/**
	 * Load the required dependencies for this plugin.
	 */
	private function load_dependencies() {
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-tickster-events-loader.php';
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-tickster-events-fetch.php';
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-tickster-events-debug.php';
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-tickster-events-api.php';
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-tickster-events-public.php';
		$this->loader = new Tickster_Events_Loader();
	}

	/**
	 * Register all of the hooks.
	 */
	private function define_hooks() {
		$plugin_public = new Tickster_Events_Api();
		$this->loader->add_action( 'rest_api_init', $plugin_public, 'register_update_tickster_events_endpoint' );
	}

	/**
	 * Run the loader to execute all of the hooks with WordPress.
	 */
	public function run() {
		$this->loader->run();
	}

	/**
	 * The reference to the class that orchestrates the hooks with the plugin.
	 */
	public function get_loader() {
		return $this->loader;
	}

}
