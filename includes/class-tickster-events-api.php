<?php

use GuzzleHttp\Client;
use GuzzleHttp\Cookie\SessionCookieJar;

/**
 * REST API endpoints.
 */
class Tickster_Events_Api {

	/**
	 * Endpoint for updating Tickster events in database.
	 */
	public function register_update_tickster_events_endpoint() {
		register_rest_route(
			'tickster/v1',
			'/update/(?P<token>[a-zA-Z0-9-]+)',
			array(
				'methods'       		=> 'GET',
				'callback'      		=> array($this, 'update_tickster_events'),
				'permission_callback' 	=> '__return_true',
				'show_in_index' 		=> false
			)
		);
	}

	/**
	 * Update Tickster events in database.
	 */
	public function update_tickster_events($request) {
			// var_dump($request->get_params()['query']);
		// each time we add a new production to tickster we need to add a new case here
		switch ($request->get_params()['query']) {
			case 'solvalla_2024':
				$TICKSTER_EVENTS_QUERY = $_ENV['TICKSTER_EVENTS_QUERY_SOLVALLA_2024'];
				break;

			case 'elitloppet_2025':
				$TICKSTER_EVENTS_QUERY = $_ENV['TICKSTER_EVENTS_QUERY_ELITLOPPET_2025'];
				break;

			case 'solvalla_2025':
				$TICKSTER_EVENTS_QUERY = $_ENV['TICKSTER_EVENTS_QUERY_SOLVALLA_2025'];
				break;

			default:
				$TICKSTER_EVENTS_QUERY = $_ENV['TICKSTER_EVENTS_QUERY_SOLVALLA_2024'];
				break;
		}

		if ($request['token'] === $_ENV['UPDATE_EVENTS_TOKEN']) {
			$fetch = new Tickster_Events_Fetch($TICKSTER_EVENTS_QUERY);
			$debug = new Tickster_Events_Debug(false);
			$json = $fetch->getAllEvents();
			$events = json_decode($json, true);

			$debug->log($events);

			switch ( wp_get_environment_type() ) {
				case 'local':
				case 'development':
				// var_dump($events);
				break;
			}

			if ($events) {
				foreach ($events['childEvents'] as $event) {

					// Get event details
					$eventDetailsFromAPI = json_decode($fetch->getEventDetails($event['id']), true);

					// Scrape Tickster page to get additional data
					if ($eventDetailsFromAPI['shopUrl']) {

						$jar = new SessionCookieJar('CookieJar', true);
						$client = new Client([
							'cookies'  => $jar,
							'verify' => false
						]);
						$response = $client->get($eventDetailsFromAPI['shopUrl']);
						$body = $response->getBody();

						libxml_use_internal_errors(true);

						$dom = new DomDocument;
						$dom->loadHTML($body);
						$dom->preserveWhiteSpace = false;

						$dom_xpath = new DomXPath($dom);
						$elements = $dom_xpath->query("//*[contains(concat(' ', normalize-space(@class), ' '), ' product-type ')]");

						// Get link details
						$links = [];
						foreach ($elements as $key => $element) {

							// Price
							$price_el = $dom_xpath->query("descendant::*[contains(concat(' ', normalize-space(@class), ' '), ' c-card__foot ')]//span[not(@class)]", $element)[0];
							if ($price_el) {
								$price = $price_el->textContent;
								$price_values = explode('–', $price);
								$prices = [];
								foreach ($price_values as $value) {
									$value_clean = str_replace('kr', '', $value);
									$value_clean = str_replace(',', '.', $value_clean);
									$value_clean = str_replace(' ', '', $value_clean);
									$prices[]    = floatval($value_clean);
								}
								$links[$key]['price_min'] = $prices[0];
								$links[$key]['price_max'] = ($prices[1]) ? $prices[1] : $prices[0];
							}

							// Ticket status
							$status_el = $dom_xpath->query("descendant::*[contains(concat(' ', normalize-space(@class), ' '), ' c-card__foot ')]//span[contains(concat(' ', normalize-space(@class), ' '), ' availability ')]", $element)[0];
							if ($status_el) {
								$links[$key]['status'] = trim($status_el->textContent);
							} else {
								$links[$key]['status'] = 'Tillgänglig';
							}

							// Image
							$image_el = $dom_xpath->query("descendant::*[contains(concat(' ', normalize-space(@class), ' '), ' c-card__image ')]//img", $element)[0];
							$links[$key]['image'] = ($image_el) ? $image_el->getAttribute('data-src') : '';

							// Title
							$title_el = $dom_xpath->query("descendant::*[contains(concat(' ', normalize-space(@class), ' '), ' c-card__title ')]", $element)[0];
							$links[$key]['title'] = ($title_el) ? trim($title_el->textContent) : '';

							// Remove the title from card body - we use the rest for description
							$card_title_el = $dom_xpath->query("descendant::*[contains(concat(' ', normalize-space(@class), ' '), ' c-card__body ')]/div", $element)[0];
							$card_title_el->parentNode->removeChild($card_title_el);

							// Description
							$desc_el = $dom_xpath->query("descendant::*[contains(concat(' ', normalize-space(@class), ' '), ' c-card__body ')]", $element)[0];
							$desc_html = '';
							foreach ($desc_el->childNodes as $desc_el_child) {
								$desc_html .= $dom->saveHTML($desc_el_child);
							}
							$links[$key]['description'] = ($desc_html) ? trim(htmlentities($desc_html)) : '';

							// Link
							$link_el = $dom_xpath->query("descendant::*[contains(concat(' ', normalize-space(@class), ' '), ' c-card__foot ')]//a", $element)[0];
							$links[$key]['link'] = ($link_el) ? 'https://secure.tickster.com' . $link_el->getAttribute('href') : '';
						}
					}

					// Setup the data
					$eventDataForDB = [];
					$eventDataForDB['id'] = $eventDetailsFromAPI['id'];

					$eventDataForDB['event_name'] = $eventDetailsFromAPI['name'];
					$eventDataForDB['event_description'] = (!empty($eventDetailsFromAPI['description']['html'])) ? stripslashes_deep($eventDetailsFromAPI['description']['html']) : '';


					// TIME ........... NEW API HAS TIME IN UTC!!!!!
					$startTime = new DateTime($eventDetailsFromAPI['startUtc'], new DateTimeZone('UTC'));
					$endTime = new DateTime($eventDetailsFromAPI['endUtc'], new DateTimeZone('UTC'));
					$doorsOpenTime = new DateTime($eventDetailsFromAPI['doorsOpenUtc'], new DateTimeZone('UTC'));

					$startTime->setTimezone(new DateTimeZone('Europe/Stockholm'));
					$endTime->setTimezone(new DateTimeZone('Europe/Stockholm'));
					$doorsOpenTime->setTimezone(new DateTimeZone('Europe/Stockholm'));

					$eventDataForDB['slug'] = $startTime->format('Ymd');

					$eventDataForDB['event_date'] = stripslashes_deep($startTime->format('Y-m-d\TH:i:s'));
					$eventDataForDB['event_end'] = stripslashes_deep($endTime->format('Y-m-d\TH:i:s'));
					$eventDataForDB['doors_open'] = stripslashes_deep($doorsOpenTime->format('Y-m-d\TH:i:s'));
					// TIME END

					$eventDataForDB['event_state'] = stripslashes_deep($eventDetailsFromAPI['state']);

					$eventDataForDB['stock_status'] = (!empty($eventDetailsFromAPI['stockLevel']) && $eventDetailsFromAPI['stockLevel'] !== 'undefined') ? stripslashes_deep($eventDetailsFromAPI['stockLevel']) : 'instock';

					// Tags need to contain Racing or Event - we base URL slug on this in the theme
					$tags = [];
					if (!empty($eventDetailsFromAPI['tags'])) {
						$tags = $eventDetailsFromAPI['tags'];
					} else {
						$tags = ['Racing'];
					}
					if (!in_array('Event', $tags) && !in_array('Racing', $tags)) {
						$tags[] = 'Racing';
					}
					$eventDataForDB['tags'] = serialize($tags);

					$eventDataForDB['links'] = (!empty($eventDetailsFromAPI['webLinks'])) ? serialize($eventDetailsFromAPI['webLinks']) : '';
					
					// maybe also store links attached to the production?
					$eventDataForDB['packages'] = (!empty($links)) ? serialize($links) : '';
					$eventDataForDB['tickets_url'] = stripslashes_deep($eventDetailsFromAPI['shopUrl']);

					// IMAGES
					$eventDataForDB['image_url'] = stripslashes_deep($eventDetailsFromAPI['imageUrl']);

					// Use a regular expression to capture the group and id from the original URL
					$pattern = '/https:\/\/static\.tickster\.com\/(\d+)\/([a-f0-9]+)(?:\/|$)/';
					// Check if the pattern matches
					if (preg_match($pattern, $eventDetailsFromAPI['imageUrl'], $matches)) {
						$group = $matches[1];  // The group is the first capture group
						$id = $matches[2];     // The id is the second capture group

						// Reconstruct the new URL
						$eventDataForDB['thumbnail_url'] = "https://static.tickster.com/cdn-cgi/image/format=auto,width=871,height=350,fit=scale-down/$group/$id";
					} else {
						$eventDataForDB['thumbnail_url'] = $eventDetailsFromAPI['imageUrl'];
					}
					// IMAGES END

					// Insert or update database
					global $wpdb;
					$table_name = $wpdb->prefix . 'tickster_events';
					$update = $wpdb->update($table_name, $eventDataForDB, array('id' => $eventDataForDB['id']));
					if ($update === false || $update < 1) {
						$id = $wpdb->insert($table_name, $eventDataForDB);

						if (!$id) {
							$debug->log($wpdb->last_error);
							$debug->log('<br />Could not enter data: <br />');
							$debug->log($eventDataForDB);
						} else {
							$debug->log('Data entered: <br />');
						}
					} else {
						$debug->log('Event with ID: ' . $eventDataForDB['id'] . ' updated. <br />');
					}
				}
			}

			// Purge cache for all pages
			if (class_exists('Purge_Varnish')) {
				$purge_varnish = new Purge_Varnish();
				$purge_varnish->purge_varnish_all_cache_manually();
			}

			// Success response
			$response = new WP_REST_Response([
				'status'        => 'events_updated',
				'response'      => 'Tickster events were successfully updated.'
			]);
			$response->set_status(200);
		} else {

			// Not authenticated response
			$response = new WP_Error(
				'rest_api_invalid',
				'Could not authenticate the request.',
				array('status' => 401)
			);
		}

		return $response;
	}
}
