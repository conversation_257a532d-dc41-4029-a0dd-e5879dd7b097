<?php

/**
 * Fetch events from Tickster.
 */
class Tickster_Events_Fetch {

	/**
	 * Tickster settings.
	 */
	private $tickster_api_events_endpoint;
	private $number_of_hits = "50";
	private $query;
	private $queryHeaders;
	private $production_event = null;

	function __construct($TICKSTER_EVENTS_QUERY) {
		$this->tickster_api_events_endpoint = "https://event.api.tickster.com/api/v1.0/sv/events/";
		$this->production_event = $TICKSTER_EVENTS_QUERY;
		$this->queryHeaders = array(
			'http' => array(
				'method' => "GET",
				'header' => "accept: application/json\r\n" . "x-api-key: " . $_ENV['TICKSTER_API_KEY'] . "\r\n"
			)
		);
	}

	/**
	 * Get events URL.
	 */
	private function eventsUrl() {
		if ($this->production_event !== null) {
			return $this->tickster_api_events_endpoint . $this->production_event . "?take=" . $this->number_of_hits . "&skip=0";
		} elseif ($this->query !== null) {
			return $this->tickster_api_events_endpoint . "?query=" . $this->query . "&take=" . $this->number_of_hits . "&skip=0";
		}
	}

	/**
	 * Get event details URL.
	 */
	private function eventDetailsUrl($eventId) {
		return $this->tickster_api_events_endpoint . $eventId;
	}

	/**
	 * Fix JSON output from Tickster. ??????????????? do we need this
	 */
	private function fixTicksterJson($json) {
		$json = str_replace("?(", "", $json);
		$json = str_replace("});", "}", $json);
		return $json;
	}

	/**
	 * Get event details in JSON format.
	 */
	public function getEventDetails($eventId) {
		$context = stream_context_create($this->queryHeaders);
		$json = file_get_contents($this->eventDetailsUrl($eventId), false,  $context);
		return $this->fixTicksterJson($json);
	}

	/**
	 * Get all events in JSON format.
	 */
	public function getAllEvents() {
		$context = stream_context_create($this->queryHeaders);
		$json = file_get_contents($this->eventsUrl(), false, $context);
		return $this->fixTicksterJson($json);
	}

	/**
	 * Test functions.
	 */
	public function test_getEventDetails($eventId) {
		print("[TEST] Fetching from: " . $this->eventDetailsUrl($eventId) . "<br />");
		print("[TEST] Data: <pre>" . $this->getEventDetails($eventId) . "</pre><br />");
	}

	public function test_getAllEvents() {
		print("[TEST] Fetching from: " . $this->fixUrl($this->tickster_api_url) . "<br />");
		print("[TEST] Data: <pre>" . $this->getAllEvents() . "</pre><br />");
	}
}
