<?php

/**
 * Get events from database to display them publicly.
 */
class Tickster_Events_Public {

	/**
     * Retrieves events from database.
     */    
    public static function get_events( $number = -1, $include = [] ) {

		global $wpdb;
		$table_name = $wpdb->prefix . 'tickster_events';

        $limit = '';
        if( $number > 0 ) {
            $limit = 'LIMIT ' . $number;
        }

        $results = [];
        $include_results = [];
        $include_string = '';

        // if there are some included events, take them first
        if( $include ) {
            $i = 0;

            // create a string of event IDs (implode didn't work for some reason)
            foreach( $include as $event_id ) {
                $i++;
                if( $i == count( $include ) ) {
                    $include_string .= "'" .  $event_id . "'";
                } else {
                    $include_string .= "'" .  $event_id . "',";
                }
            }

            $include_results = $wpdb->get_results( "SELECT * FROM $table_name WHERE DATE(event_date) >= CURDATE() AND id IN (" . $include_string . ") ORDER BY FIELD(id, " . $include_string . ") $limit" );

            $include_results_count = 0;
            if( $include_results ) {
                $include_results_count = count( $include_results );
            }
        }

        // get other upcoming events
        if( $include_results ) {

            // calculate how many extra events to get
            $no_run = false;
            if( $number > 0 ) {
                if( $number > $include_results_count ) {
                    $number = $number - $include_results_count;
                    $limit = 'LIMIT ' . $number;
                } elseif( $number == $include_results_count ) {
                    $no_run = true;
                }
            }
            
            if( $no_run == false ) {
                $results = $wpdb->get_results( "SELECT * FROM $table_name WHERE DATE(event_date) >= CURDATE() AND id NOT IN (" . $include_string . ") ORDER BY event_date ASC $limit" );
            }
        } 
        
        // in case there are no included events
        else {
            $results = $wpdb->get_results( "SELECT * FROM $table_name WHERE DATE(event_date) >= CURDATE() ORDER BY event_date ASC $limit" );
        }

        $results = array_merge( $results, $include_results );

        if( $results ) {
            foreach( $results as $key => $result ) {
                $results[$key]->tags = unserialize( $result->tags );
                $results[$key]->links = unserialize( $result->links );
                $results[$key]->packages = unserialize( $result->packages );
            }
        }

        return $results;

    }

    /**
     * Retrieves events from database by event slug. Optionally filter results by tag.
     */    
    public static function get_events_by_slug( $event_slug, $event_tag = false ) {

		global $wpdb;

		$table_name = $wpdb->prefix . 'tickster_events';
        $results = $wpdb->get_results( "SELECT * FROM $table_name WHERE slug = $event_slug" );

        if( $results ) {
            foreach( $results as $key => $result ) {

                $results[$key]->tags = unserialize( $result->tags );
                $results[$key]->links = unserialize( $result->links );
                $results[$key]->packages = unserialize( $result->packages );

                // Filter by tag
                if( $event_tag !== false && ! in_array( $event_tag, (array) $result->tags ) ) {
                    unset( $results[$key] );
                }

            }
        }

        return $results;

    }
    
	/**
     * Retrieves event details from database by event ID.
     */    
    public static function get_event_details_by_id( $event_id ) {

		global $wpdb;

		$table_name = $wpdb->prefix . 'tickster_events';
		$result = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM $table_name WHERE id = %s", $event_id ) );

        if( $result ) {
            $result->tags = unserialize( $result->tags );
            $result->links = unserialize( $result->links );
            $result->packages = unserialize( $result->packages );
        }

        return $result;

    }

    /**
     * Retrieves packages from database.
     */    
    public static function get_packages() {

		global $wpdb;

		$table_name = $wpdb->prefix . 'tickster_events';
		$results = $wpdb->get_results( "SELECT packages FROM $table_name WHERE DATE(event_date) >= CURDATE() ORDER BY event_date ASC" );

        $array = [];
        if( $results ) {
            foreach( $results as $result ) {
                if( $result->packages ) {
                    foreach( unserialize( $result->packages ) as $package ) {
                        $array[$package['title']] = $package;
                    }
                }
            }
        }

        return $array;

    }

}