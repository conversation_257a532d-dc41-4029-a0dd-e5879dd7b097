<?php

/**
 * Plugin Name:       Tickster Events
 * Description:       Fetches upcoming events from Tickster.
 * Version:           1.0.0
 * Author:            Visionmate
 * Author URI:        https://www.visionmate.se/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       tickster-events
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Autoload composer.
 */
$composer_autoload = __DIR__ . '/vendor/autoload.php';
if ( file_exists( $composer_autoload ) ) {
	require_once $composer_autoload;

	/** 
	 * Load variables from .env file (if file exists in project root folder). Variables are then available 
	 * as $_ENV['VARIABLE_NAME'] or $_SERVER['VARIABLE_NAME'].
	 */
	$dotenv = \Dotenv\Dotenv::createImmutable(__DIR__);
	$dotenv->safeLoad();
}

/**
 * The code that runs during plugin activation.
 */
function activate_tickster_events() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-tickster-events-activator.php';
	Tickster_Events_Activator::activate();
}
register_activation_hook( __FILE__, 'activate_tickster_events' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-tickster-events.php';

/**
 * Begins execution of the plugin.
 */
function run_tickster_events() {
	$plugin = new Tickster_Events();
	$plugin->run();
}
run_tickster_events();