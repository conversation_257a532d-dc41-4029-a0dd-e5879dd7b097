<?php

/**
 * The template for displaying single trainer.
 */

$context = Timber::get_context();
$post = Timber::query_post();
$context['post'] = $post;


$related_posts = Timber::get_posts([
    'post_type' => $post->post_type,
    'posts_per_page' => '4',
    'post__not_in' => [$post->ID],
    'orderby' => 'date',
    'order' => 'DESC'
]);
$context['related_posts'] = $related_posts;

// Archive url for breadcrumbs
$context['archive_title'] = __('Inbjudna Hästar', 'solvalla');
$archive_url = get_post_type_archive_link('invited-horses');
$archive_url = str_replace('%we-ih%/', '', $archive_url);
$context['archive_url'] = $archive_url;

// Get taxonomy terms for breadcrumbs
$taxonomy_terms = get_the_terms($post->ID, 'which-event-invited-horses');
if ($taxonomy_terms && !is_wp_error($taxonomy_terms)) {
    $primary_term = $taxonomy_terms[0]; // Get the first term
    $context['taxonomy_term'] = $primary_term;
    $context['taxonomy_archive_url'] = get_term_link($primary_term);
}

// Add ACF fields to context.
$context['fields'] = [];
foreach (get_field_objects($post->ID) as $field_key => $field_array) {
    foreach ($field_array as $field_param_name => $field_param_value) {
        if ($field_param_name == 'label' || $field_param_name == 'value' || $field_param_name == 'name') {
            $context['fields'][$field_key][$field_param_name] = $field_param_value;
        }
    }
}

if ( post_password_required( $post->ID ) ) {
    Timber::render( 'single-password.twig', $context );
} else {
    Timber::render( 'single-invited-horses.twig', $context );
}