<?php
/**
 * The template for displaying events from Tickster.
 */

// If Tickster events plugin is not activated, send user to 404 template.
if( ! class_exists( 'Tickster_Events_Public' ) ) {
    Timber::render( '404.twig', $context );
}

$context = Timber::context();

$context['title'] = 'TRAVTÄVLINGAR';

// Get Tickster events from database.
$context['events'] = [];
$context['events'] = Tickster_Events_Public::get_events();

$image_helper = new Timber\ImageHelper();

foreach( $context['events'] as $key => $event ) {
    
    // Assign correct category slug to events
    if( is_array( $event->tags ) && in_array( 'Event', $event->tags ) ) {
        $context['events'][$key]->local_url = '/event/' . $event->slug;
    } elseif( is_array( $event->tags ) && in_array( 'Racing', $event->tags ) ) {
        $context['events'][$key]->local_url = '/travtavlingar/' . $event->slug;
    }

    // Cache external images locally
    $context['events'][$key]->local_thumbnail_url = @$image_helper::resize( $context['events'][$key]->image_url, 781 );

}

Timber::render( 'archive-event.twig', $context );
 