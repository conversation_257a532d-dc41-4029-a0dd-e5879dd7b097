<?php

defined('ABSPATH') || exit;

/**
 * If you are installing <PERSON>ber as a Composer dependency in your theme, you'll need this block
 * to load your dependencies and initialize <PERSON><PERSON>. If you are using <PERSON><PERSON> via the WordPress.org
 * plug-in, you can safely delete this block.
 */
$composer_autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($composer_autoload)) {
	require_once $composer_autoload;
	$timber = new Timber\Timber();
}
/** 
 * Load variables from .env file (if file exists in project root folder). Variables are then available 
 * as $_ENV['VARIABLE_NAME'] or $_SERVER['VARIABLE_NAME'].
 */
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->safeLoad();

// If the Timber plugin isn't activated, print a notice in the admin.
if (!class_exists('Timber')) {
	add_action('admin_notices', function () {
		echo '<div class="error"><p>Timber not activated. Make sure you activate the plugin in <a href="' . esc_url(admin_url('plugins.php#timber')) . '">' . esc_url(admin_url('plugins.php')) . '</a></p></div>';
	});
	return;
}

// Create our version of the TimberSite object
class StarterSite extends TimberSite {

	// This function applies some fundamental WordPress setup, as well as our functions to include custom post types and taxonomies.
	function __construct() {
		// add_theme_support( 'post-formats' );
		add_theme_support('post-thumbnails');
		add_theme_support('menus');
		add_theme_support('editor-styles');
		add_theme_support('custom-spacing');
		add_theme_support('responsive-embeds');
		add_theme_support('editor-color-palette', array(
			array(
				'name'  => 'Galliano',
				'slug'  => 'galliano',
				'color' => '#e7b00d',
			),
			array(
				'name'  => 'Cod Grey',
				'slug'  => 'cod-grey',
				'color' => '#121212',
			),
			array(
				'name'  => 'Mine Shaft',
				'slug'  => 'mine-shaft',
				'color' => '#343434',
			),
			array(
				'name'  => 'Alto',
				'slug'  => 'alto',
				'color' => '#ddd',
			),
			array(
				'name'  => 'Chathams Blue',
				'slug'  => 'chathams-blue',
				'color' => '#133a63',
			),
			array(
				'name' 	=> 'Razzmatazz',
				'slug' 	=> 'razzmatazz',
				'color' => '#D0006F'
			)
		));

		add_filter('timber_context', [$this, 'add_to_context']);
		add_filter('render_block', [$this, 'wrap_core_blocks'], 10, 2);
		// add_filter( 'get_twig', [ $this, 'add_to_twig' ) );

		add_editor_style('assets/css/wp-editor.css');

		add_action('wp_enqueue_scripts', [$this, 'enqueue_custom_theme_assets']);
		// add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_custom_admin_assets' ] );
		add_action('init', [$this, 'register_rewrites']);
		add_action('init', [$this, 'register_post_types']);
		add_action('init', [$this, 'register_taxonomies']);
		add_action('init', [$this, 'register_menus']);
		add_action('init', [$this, 'register_widgets']);
		add_action('init', [$this, 'register_yoast']);
		add_action('init', [$this, 'register_blocks']);
		add_action('init', [$this, 'register_meta']);
		add_action('init', [$this, 'register_shortcodes']);
		add_action('init', [$this, 'register_youtube']);
		add_action('init', [$this, 'register_custom_rest_routes']);
		add_action('init', [$this, 'register_posts_sync']);
		add_action('init', [$this, 'register_sitemap']);
		add_action('init', [$this, 'add_custom_table_for_youtube']);
		add_action('acf/init', [$this, 'register_options']);
		add_filter('body_class', [$this, 'add_body_classes']);
		add_filter('acf/load_field/name=tickster_event', [$this, 'acf_add_tickster_events_to_checkbox']);
		add_filter('acf/update_value/name=tickster_event', [$this, 'check_tickster_events_before_post_save'], 10, 4);
		add_filter('acf/load_field/name=slider_priority_1', [$this, 'acf_add_tickster_events_to_slider_priority_checkbox']);
		add_filter('acf/load_field/name=slider_priority_2', [$this, 'acf_add_tickster_events_to_slider_priority_checkbox']);
		add_filter('acf/load_field/name=slider_priority_3', [$this, 'acf_add_tickster_events_to_slider_priority_checkbox']);
		add_filter('acf/load_field/name=slider_priority_4', [$this, 'acf_add_tickster_events_to_slider_priority_checkbox']);
		add_filter('acf/load_field/name=slider_priority_5', [$this, 'acf_add_tickster_events_to_slider_priority_checkbox']);
		add_filter('acf/load_field/key=field_67d2bc8a9d12e', [$this, 'acf_add_tickster_events_to_slider_priority_checkbox']); // Tickster Slider tab in Theme Settings - Scheduled Priority Rules - Slide field
		add_filter('acf/load_field/name=which_slide', [$this, 'acf_add_tickster_events_to_slider_custom_settings_repeater']);
		add_filter('acf/load_field/name=which_event', [$this, 'acf_add_tickster_events_to_slider_custom_settings_repeater']);
		add_filter('script_loader_tag', [$this, 'defer_scripts'], 10);
		add_filter('upload_mimes', [$this, 'add_mime_types']);
		add_filter('wp_check_filetype_and_ext', [$this, 'allow_svg_upload'], 10, 4);
		add_action('admin_head', [$this, 'fix_svg']);
		add_filter('wpseo_metabox_prio', [$this, 'yoast_to_bottom']);
		add_action('map_meta_cap', [$this, 'custom_manage_privacy_options'], 1, 4);
		add_action('admin_head', [$this, 'hide_menu_for_editors']);
		add_filter('wp_get_nav_menu_items', [$this, 'fix_nav_menu_items'], 10, 3);
		add_action('admin_bar_menu', [$this, 'admin_bar_fetch_tickster_events'], 1000);
		add_filter('redirection_role', [$this, 'allow_seo_manager_to_edit_redirections']);
		add_action('rest_api_init', [$this, 'update_youtube_endpoint']);
		add_filter('wp_kses_allowed_html', [$this, 'allow_iframes_for_editor'], 1);
		add_action('acf/input/admin_head', [$this, 'acf_admin_styles']);
		add_filter('acf/load_value/name=scheduled_priority_rules', [$this, 'sort_slider_rules_by_priority'], 10, 3);
		add_action('init', function() {
			if (!wp_next_scheduled('tickster_cleanup_expired_events')) {
				wp_schedule_event(time(), 'five_minutes', 'tickster_cleanup_expired_events');
			}
		});
		add_filter('cron_schedules', function($schedules) {
			$schedules['five_minutes'] = array(
				'interval' => 300,
				'display'  => __('Every 5 Minutes')
			);
			return $schedules;
		});
		add_action('tickster_cleanup_expired_events', [$this, 'remove_tickster_replacements_for_expired_events']);
		add_action('load-toplevel_page_theme-settings', [$this, 'remove_tickster_replacements_for_expired_events']);
		add_shortcode('custom-language-switcher', [$this, 'trpc_custom_language_switcher'], 10);

		parent::__construct();
	}

	/**
	 * Theme styles and scripts.
	 */
	function enqueue_custom_theme_assets() {

		global $params;

		/**
		 * Styles.
		 */

		$css_deps = [];

		// Dequeue Dashicons for non-logged in users
		if (!is_user_logged_in()) {
			wp_deregister_style('dashicons');
		}

		// Podcast single page - Plyr
		if ($params && array_key_exists('podcast_slug', $params)) {
			$css_deps[] = 'plyr-css';
			wp_enqueue_style('plyr-css', get_theme_file_uri('assets/css/plyr.css'));
		}

		// Main CSS styles
		wp_enqueue_style('vm-main', get_theme_file_uri('assets/css/main.css'), $css_deps, filemtime(get_theme_file_path('assets/css/main.css')));

		/**
		 * Scripts.
		 */

		$js_deps = [];

		// Google Maps API - API key needs to be added to .env file
		if ((has_block('acf/block-google-maps') && $_ENV['GOOGLE_MAPS_KEY'])) {
			$js_deps[] = 'google-maps-api';
			wp_enqueue_script('google-maps-api', 'https://maps.googleapis.com/maps/api/js?key=' . $_ENV['GOOGLE_MAPS_KEY'] . '&callback=initMap', array(), false, true);
		} elseif (($params && array_key_exists('event_slug', $params)) && $_ENV['GOOGLE_MAPS_KEY']) {
			$event_text_has_block = $this->event_text_has_block('acf/block-google-maps');
			if ($event_text_has_block == true) {
				$js_deps[] = 'google-maps-api';
				wp_enqueue_script('google-maps-api', 'https://maps.googleapis.com/maps/api/js?key=' . $_ENV['GOOGLE_MAPS_KEY'] . '&callback=initMap', array(), false, true);
			}
		}

		// Podcast single page - Plyr
		if ($params && array_key_exists('podcast_slug', $params)) {
			$js_deps[] = 'plyr-js';
			wp_enqueue_script('plyr-js', get_theme_file_uri('assets/js/plyr.js'), array('jquery'), filemtime(get_theme_file_path('assets/js/plyr.js')), true);
		}

		// Slider block or Event single page - Smartcrop
		if (has_block('acf/block-slider') || ($params && array_key_exists('event_slug', $params))) {
			$js_deps[] = 'smartcrop-js';
			wp_enqueue_script('smartcrop-js', get_theme_file_uri('assets/js/smartcrop.js'), array('jquery'), filemtime(get_theme_file_path('assets/js/smartcrop.js')), true);
		}

		// Main JS bundle
		wp_enqueue_script('vm-bundle', get_theme_file_uri('assets/js/main.js'), $js_deps, filemtime(get_theme_file_path('assets/js/main.js')), true);
		// wp_enqueue_script( 'vm-index', get_theme_file_uri( 'assets/js/index.js' ), $js_deps, filemtime( get_theme_file_path( 'assets/js/index.js' ) ), true );
		wp_localize_script('vm-bundle', 'global_object', [
			'ajax_url' => admin_url('admin-ajax.php')
		]);
	}

	/**
	 * Admin styles and scripts.
	 */
	// function enqueue_custom_admin_assets() {
	//     wp_enqueue_style( 'custom-admin-style', get_stylesheet_directory_uri() . '/css/custom-admin-style.css' );
	// }

	/**
	 * ACF repeater styles.
	 */
	function acf_admin_styles() {
		?>
		<style type="text/css">
			.acf-flexible-content .layout .acf-fc-layout-handle {
				/*background-color: #00B8E4;*/
				background-color: #202428;
				color: #eee;
			}
			.acf-repeater.-row > table > tbody > tr > td,
			.acf-repeater.-block > table > tbody > tr > td {
				border-top: 1px solid #202428;
			}
			.acf-repeater .acf-row-handle {
				vertical-align: top !important;
				padding-top: 11px;
			}
			.acf-repeater .acf-row-handle span {
				font-size: 20px;
				font-weight: bold;
				color: #202428;
			}
			.acf-repeater .acf-row-handle .acf-icon.-minus {
				top: 30px;
			}

		</style>
		<?php
	}

	/**
	 * Defer parsing of scripts.
	 */
	function defer_scripts($url) {
		if (is_user_logged_in()) return $url; // don't break WP Admin

		if (false !== strpos($url, '//maps.googleapis.com/maps/api/')) {
			// Google Maps API
			return str_replace(' src', ' defer async src', $url);
		} elseif (false !== strpos($url, 'assets/js/cookieconsent.js')) {
			// Cookie consent
			return str_replace(' src', ' defer src', $url);
		} else {
			return $url;
		}
	}

	/** 
	 * Abstracting long chunks of code.
	 * The following included files only need to contain the arguments and register_whatever functions. They are applied to WordPress 
	 * in these functions that are hooked to init above. The point of having separate files is solely to save space in this file. 
	 * Think of them as a standard PHP include or require.
	 */
	function register_post_types() {
		define('VM_CPT', [
			[
				'name' 					=> 'Event Texts',
				'label_singular' 		=> 'Event Text',
				'label_plural' 			=> 'Event Texts',
				'slug' 					=> 'travtavlingar',
				'icon' 					=> 'calendar',
				'public' 				=> false,
				'has_archive' 			=> false,
				'exclude_from_search' 	=> true,
				'publicly_queryable'	=> false
			],
			[
				'name' 					=> 'Trainers',
				'label_singular' 		=> 'Trainer',
				'label_plural' 			=> 'Trainers',
				'slug' 					=> 'sport/proffstranare',
				'icon' 					=> 'groups',
				'has_archive' 			=> false,
			],
			[
				'name' 					=> 'Hall of Fame',
				'label_singular' 		=> 'Person',
				'label_plural' 			=> 'Persons',
				'slug' 					=> 'sport/hall-of-fame',
				'icon' 					=> 'star-filled',
				'has_archive' 			=> false,
				'menu_name'				=> 'Hall of Fame',
				'post_type_name'		=> 'hall-of-fame',
			],
			[
				'name' 					=> 'Program',
				'label_singular' 		=> 'Program',
				'label_plural' 			=> 'Programs',
				'slug' 					=> 'program',
				'icon' 					=> 'pdf',
				'public'				=> false,
				'has_archive' 			=> false,
				'exclude_from_search'	=> true,
				'supports'            	=> ['title'],
				'show_in_rest'        	=> false,
				'publicly_queryable'	=> false
			],
			[
				'name' 					=> 'Result Photos',
				'label_singular' 		=> 'Photo',
				'label_plural' 			=> 'Photos',
				'slug' 					=> 'sport/resultat-malfoton',
				'icon' 					=> 'camera',
				'public'				=> true,
				'has_archive' 			=> true,
				'menu_name'				=> 'Result Photos',
				'post_type_name'		=> 'result-photos',
			],
			[
				'name' 					=> 'Banner',
				'label_singular' 		=> 'Banner',
				'label_plural' 			=> 'Banners',
				'slug' 					=> 'banner',
				'icon' 					=> 'images-alt2',
				'public'				=> false,
				'has_archive' 			=> false,
				'exclude_from_search'	=> true,
				'supports'            	=> ['title'],
				'show_in_rest'        	=> false,
			],
			[
				'name' 					=> 'Annual Reports',
				'label_singular' 		=> 'Report',
				'label_plural' 			=> 'Annual Reports',
				'slug' 					=> 'arsredovisning',
				'icon' 					=> 'pdf',
				'public'				=> false,
				'has_archive' 			=> false,
				'exclude_from_search'	=> true,
				'supports'            	=> ['title'],
				'show_in_rest'        	=> false,
				'post_type_name'		=> 'reports',
			],
			[
				'name' 					=> 'Custom Slides',
				'label_singular' 		=> 'Slide',
				'label_plural' 			=> 'Custom Slides',
				'slug' 					=> 'slide',
				'icon' 					=> 'images-alt2',
				'public'				=> false,
				'has_archive' 			=> false,
				'exclude_from_search'	=> true,
				'supports'            	=> ['title'],
				'show_in_rest'        	=> false,
				'post_type_name'		=> 'slides',
			],
			[
				'name' 					=> 'Winners',
				'label_singular' 		=> 'Winner',
				'label_plural' 			=> 'Winners',
				'slug' 					=> 'winners',
				'icon' 					=> 'awards',
				'public'				=> true,
				'has_archive' 			=> true,
				'exclude_from_search'	=> true,
				'supports'            	=> ['title', 'thumbnail', 'editor'],
				'show_in_rest'          => true, // Needed for tax to appear in Gutenberg editor.
				'taxonomies'            => ['which-event-winners'],
				'post_type_name'		=> 'winners',
				'rewrite'               => [
					'slug' => 'vinnare/%we-w%', // which-event-winners (shortened because wp max slug length for rewrite is 20 chr)
					'with_front' => false
				]
			],
			[
				'name' 					=> 'Invited Horses',
				'label_singular' 		=> 'Invited Horse',
				'label_plural' 			=> 'Invited Horses',
				'slug' 					=> 'invited-horses', // invited-horses
				'icon' 					=> 'buddicons-activity',
				'public'				=> true,
				'has_archive' 			=> true,
				'exclude_from_search'	=> true,
				'supports'            	=> ['title', 'thumbnail', 'editor'],
				'show_in_rest'          => true, // Needed for tax to appear in Gutenberg editor.
				'taxonomies'            => ['which-event-invited-horses'],
				'post_type_name'		=> 'invited-horses', // invited-horses
				'rewrite'               => [
					'slug' => 'inbjudna-hastar/%we-ih%', // which-event-invited-horsees (shortened because wp max slug length for rewrite is 20 chr)
					'with_front' => false
				]
			],
		]);

		require('lib/custom-types.php');
	}

	function register_taxonomies() {
		require('lib/taxonomies.php');
	}

	function register_rewrites() {
		require('lib/rewrites.php');
	}

	function register_menus() {
		require('lib/menus.php');
	}

	function register_widgets() {
		require('lib/widgets.php');
	}

	function register_options() {
		require('lib/options.php');
	}

	function register_meta() {
		require('lib/meta.php');
	}

	function register_shortcodes() {
		require('lib/shortcodes.php');
	}

	function register_yoast() {
		require('lib/yoast.php');
	}

	function register_youtube() {
		require('lib/youtube.php');
	}

	function register_custom_rest_routes() {
		require('lib/rest-api.php');
	}

	function register_posts_sync() {
		require('lib/posts-sync.php');
	}

	function register_sitemap() {
		require('lib/sitemap.php');
	}

	// Register every block in the blocks folder.
	function register_blocks() {
		$block_folders = glob(__DIR__ . '/templates/blocks/*', GLOB_ONLYDIR);

		foreach ($block_folders as $folder) {
			register_block_type($folder);
		}
	}

	function allow_iframes_for_editor($allowed_tags) {

		if (current_user_can('editor')) {
			$allowed_tags['iframe'] = array(
				'align' => true,
				'allow' => true,
				'allowfullscreen' => true,
				'class' => true,
				'frameborder' => true,
				'height' => true,
				'id' => true,
				'marginheight' => true,
				'marginwidth' => true,
				'name' => true,
				'scrolling' => true,
				'src' => true,
				'style' => true,
				'width' => true,
				'allowFullScreen' => true,
				'class' => true,
				'frameborder' => true,
				'height' => true,
				'mozallowfullscreen' => true,
				'src' => true,
				'title' => true,
				'webkitAllowFullScreen' => true,
				'width' => true
			);
		}

		return $allowed_tags;
	}


	/**
	 * Add custom classes to body tag.
	 */
	function add_body_classes($classes) {

		global $params;
		global $post;

		// Add Elitloppet class
		if ($this->id == 2) {
			$classes[] = 'elitloppet';
		}

		// Single event
		if ($params && array_key_exists('event_type', $params)) {
			$classes[] = 'single-event';
		}
		
		// Add class if page has custom slider block
		if (isset($post) && is_singular()) {
			$blocks = parse_blocks($post->post_content);
			if (has_custom_slider_block($blocks)) {
				$classes[] = 'has-custom-slider';
			}
		}
		
		return $classes;
	}

	/** 
	 * Dynamically populate select on event admin screen by Tickster events (from custom database table).
	 */
	function acf_add_tickster_events_to_checkbox($field) {
		$field['choices'] = [];

		// Get Tickster events from database.
		$events = Tickster_Events_Public::get_events();

		foreach ($events as $event) {

			// Find WP post object that connects with the event - if there is none, add Tickster event to list of inputs.
			$connected_post = get_posts([
				'numberposts'   => 1,
				'post_type'     => 'eventtexts',
				'meta_key'      => 'tickster_event',
				'meta_value'    => $event->id,
				'meta_compare'  => 'LIKE',
				'fields'		=> 'ids'
			]);
			if (!$connected_post || (array_key_exists('post', $_GET) && $connected_post[0] == $_GET['post'])) {
				$date = mysql2date('j F Y', $event->event_date);
				$field['choices'][$event->id] = $date . ' - ' . $event->event_name;
			}
		}
		return $field;
	}

	/** 
	 * Check if we are not assigning Tickster events which already have been assigned to another post.
	 */
	function check_tickster_events_before_post_save($value, $post_id, $field, $original) {
		foreach ($value as $key => $event_id) {
			$connected_posts = get_posts([
				'numberposts'   => -1,
				'post_type'     => 'eventtexts',
				'meta_key'      => 'tickster_event',
				'meta_value'    => $event_id,
				'meta_compare'  => 'LIKE',
				'fields'		=> 'ids'
			]);
			foreach ($connected_posts as $connected_post) {
				if ($connected_post !== $post_id) {
					unset($value[$key]);
				}
			}
		}
		return $value;
	}

	/** 
	 * Dynamically populate Slider Priority select in Theme Settings by Tickster events (from custom database table).
	 */
	function acf_add_tickster_events_to_slider_priority_checkbox($field) {
		$field['choices'] = [];

		// Add custom blocks on the beginning of the list.
		$custom_slides = get_posts([
			'numberposts'   => -1,
			'post_type'     => 'slides',
			'fields'		=> 'ids'
		]);
		foreach ($custom_slides as $slide) {
			$field['choices']['custom_' . $slide] = 'Custom - ' . get_the_title($slide);
		}

		// Get Tickster events from database.
		$events = Tickster_Events_Public::get_events();

		foreach ($events as $event) {
			$date = mysql2date('j F Y', $event->event_date);
			$field['choices'][$event->id] = $date . ' - ' . $event->event_name;
		}
		return $field;
	}


	/** 
	 * Dynamically populate Custom Settings for Tickster Slides select in Theme Settings by Tickster events (from custom database table).
	 */
	function acf_add_tickster_events_to_slider_custom_settings_repeater($field)
	{
		$field['choices'] = [];

		// Get Tickster events from database.
		$events = Tickster_Events_Public::get_events();
		error_log(json_encode( $events), 3, __DIR__ . "/error.log");
		foreach ($events as $event) {
			$date = mysql2date('j F Y', $event->event_date);
			$field['choices'][$event->id] = $date . ' - ' . $event->event_name;
		}
		return $field;
	}

	/** 
	 * Access data site-wide.
	 * This function adds data to the global context of your site. In less-jargon-y terms, any values in this function are available 
	 * on any view of your website. Anything that occurs on every page should be added here.
	 */
	function add_to_context($context) {

		// Our menu occurs on every page, so we add it to the global context.
		$context['main_menu'] = new TimberMenu('main-menu');
		$context['main_menu_right'] = new TimberMenu('main-menu-right');
		$context['footer_menu'] = new TimberMenu('footer-menu');
		$context['footer_menu_2'] = new TimberMenu('footer-menu-2');

		// This 'site' context below allows you to access main site information like the site title or description.
		$context['site'] = $this;

		// Info about sites from multisite - can be used to compare with current site.
		$context['solvalla'] = new Timber\Site(1);
		$context['elitloppet'] = new Timber\Site(2);

		// Add data from ACF options to global context.
		$context['options'] = get_fields('option');
		$context['options']['custom_logo_file_exists'] = file_exists(get_theme_file_path('assets/images/' . $context['options']['custom_logo_name']));

		// Add data from other site to global context.
		if ($context['options']['enable_link_to_djurens_helg']) {
			$context['djurens_helg'] = file_get_contents(get_theme_file_path('assets/images/djurens_helg.png'));
		} elseif ($context['site'] && property_exists($context['site'], 'id') && $context['site']->id == 1) {
			switch_to_blog(2);
			$context['other_site_options'] = get_fields('option');
			$context['other_site_options']['custom_logo_file_exists'] = file_exists(get_theme_file_path('assets/images/' . $context['options']['custom_logo_name']));
			switch_to_blog(1);
		} else {
			switch_to_blog(1);
			$context['other_site_options'] = get_fields('option');
			$context['other_site_options']['custom_logo_file_exists'] = file_exists(get_theme_file_path('assets/images/' . $context['options']['custom_logo_name']));
			switch_to_blog(2);
		}

		// Add initial countdown data to global context.
		$context['countdown_start'] = [
			'days' => '',
			'hours' => '',
			'minutes' => '',
			'seconds' => ''
		];
		if (
			$context['options']
			&& array_key_exists('countdown_date', $context['options'])
			&& $context['options']['countdown_date']
		) {
			$futureDate = new DateTime($context['options']['countdown_date'], new DateTimeZone('Europe/Stockholm'));
			$currentDate = new DateTime('now', new DateTimeZone('Europe/Stockholm'));
			$currentDate->modify('+1 hours');
			$interval = $currentDate->diff($futureDate);
			$days = $interval->days;
			$hours = $interval->h;
			$minutes = $interval->i;
			$seconds = $interval->s;
			$context['countdown_start'] = [
				'days' => str_pad($days, 2, '0', STR_PAD_LEFT),
				'hours' => str_pad($hours, 2, '0', STR_PAD_LEFT),
				'minutes' => str_pad($minutes, 2, '0', STR_PAD_LEFT),
				'seconds' => str_pad($seconds, 2, '0', STR_PAD_LEFT),
			];
		}

		return $context;
	}

	function wrap_core_blocks($block_content, $block) {
		$core_block = ($block && array_key_exists('blockName', $block) && $block['blockName']) ? strpos($block['blockName'], 'core') : false;
		$gf_block = ($block && array_key_exists('blockName', $block) && $block['blockName']) ? strpos($block['blockName'], 'gravityforms') : false;
		$acf_block = ($block && array_key_exists('blockName', $block) && $block['blockName']) ? strpos($block['blockName'], 'acf') : false;

		// print_r($block);

		$fullWidth = false;
		if (array_key_exists('className', $block['attrs'])) {
			$fullWidth = strpos($block['attrs']['className'], 'is-style-full-width');
		}

		global $template;
		$template = basename($template);

		// print_r($post->post_type);

		$excludedTemplates = [];

		if (!in_array($template, $excludedTemplates) && ($core_block !== false || $gf_block !== false || $acf_block !== false) && ($fullWidth === false)) {
			$block_name = '';
			$additionalClasses = '';

			if ($gf_block !== false) {
				$block_name = 'gravity-forms';
			}

			if ($core_block !== false) {
				$block_name = explode('core/', $block['blockName'])[1];
			}

			// add id same as content to h[1-6] tags
			if ($block_name == 'heading') {
				$isMatch = preg_match('/(<h[1-6])>\s*(.*\b\S*)\s*(<\/h[1-6]>)/', $block_content, $matches);

				if ($isMatch === 1) {
					$block_content = $matches[1] . ' id="' . sanitize_title($matches[2]) . '">' . $matches[2] . $matches[3];
				}
			}

			// Remove additional wrapper markup from inner blocks
			if (array_key_exists('innerBlocks', $block) && count($block['innerBlocks']) > 0) {

				$find = [
					'<section class="overflow-hidden content-block content-block--core content-block--columns">', // Remove inner columns block with 'overflow-hidden' class
					'<div class="container-fluid"><div class="row"><div class="col">',
					'<div class="container-fluid"><div class="row"><div class="col-12 offset-md-1 col-md-10">',
					'<div class="container-fluid"><div class="row"><div class="col-12">',
					'</div></div></div></section>',
				];

				foreach ($block['innerBlocks'] as $innerBlock) {
					if (strpos($innerBlock['blockName'], 'core') !== false) {
						$inner_block_name = explode('core/', $innerBlock['blockName'])[1];
						$find[] = '<section class="content-block content-block--core content-block--' . $inner_block_name . '">';
					}
				}

				$block_content = str_replace($find, '', $block_content);

				if ($block_name == 'group') {
					$pos = strpos($block_content, '<div class="wp-block-group__inner-container">');

					if ($pos !== false) {
						$block_content = substr_replace($block_content, '', $pos, strlen('<div class="wp-block-group__inner-container">'));
						$block_content = substr_replace($block_content, '', -11, strlen('</div>'));
					}
				}
			}

			global $post;

			// if( $post->post_type === 'post' ) {
			//     $wrapper['opening_markup'] = '<section class="content-block content-block--core content-block--' . $block_name . $additionalClasses . '"><div class="container-fluid"><div class="row"><div class="col-12 offset-md-1 col-md-10">';
			// } else {
			//     $wrapper['opening_markup'] = '<section class="content-block content-block--core content-block--' . $block_name . $additionalClasses . '"><div class="container-fluid"><div class="row"><div class="col-12">';
			// }
			$wrapper['opening_markup'] = '<section class="content-block content-block--core content-block--' . $block_name . $additionalClasses . '"><div class="container-fluid"><div class="row"><div class="col-12">';

			$wrapper['closing_markup'] = '</div></div></div></section>';

			// leave block content as is for acf block
			if ($acf_block === false) {
				$block_content = $wrapper['opening_markup'] . $block_content . $wrapper['closing_markup'];
			}
		}

		return $block_content;
	}

	/**
	 * Curl call
	 */
	function curl_get_contents($url) {
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		$data = curl_exec($ch);
		curl_close($ch);
		return $data;
	}

	// Allow SVG
	function allow_svg_upload($data, $file, $filename, $mimes) {
		$filetype = wp_check_filetype($filename, $mimes);
		return [
			'ext'             => $filetype['ext'],
			'type'            => $filetype['type'],
			'proper_filename' => $data['proper_filename']
		];
	}

	function add_mime_types($mimes) {
		$mimes['svg'] = 'image/svg+xml';
		return $mimes;
	}

	function fix_svg() {
		echo '<style type="text/css">
			.attachment-266x266, .thumbnail img {
				width: 100% !important;
				height: auto !important;
			}
			</style>';
	}

	/**
	 * Move Yoast SEO metabox to bottom
	 */
	function yoast_to_bottom() {
		return 'low';
	}

	/**
	 * Fix Customizer error
	 */
	function fix_nav_menu_items($items, $menu, $args) {
		foreach ($items as $key => $item)
			$items[$key]->description = '';

		return $items;
	}

	/**
	 * Allow administrators and editors to manage privacy options
	 */
	function custom_manage_privacy_options($caps, $cap, $user_id, $args) {
		if (!is_user_logged_in()) return $caps;
		$user_meta = get_userdata($user_id);
		if (array_intersect(['editor', 'administrator'], $user_meta->roles)) {
			if ('manage_privacy_options' === $cap) {
				$manage_name = is_multisite() ? 'manage_network' : 'manage_options';
				$caps = array_diff($caps, [$manage_name]);
			}
		}
		return $caps;
	}

	/**
	 * Hide items from Appearance menu for editors
	 */
	function hide_menu_for_editors() {
		if (current_user_can('editor')) {
			remove_submenu_page('themes.php', 'themes.php'); // hide the theme selection submenu
			remove_submenu_page('themes.php', 'widgets.php'); // hide the widgets submenu
			$customizer_url = add_query_arg('return', urlencode(remove_query_arg(wp_removable_query_args(), wp_unslash($_SERVER['REQUEST_URI']))), 'customize.php');
			remove_submenu_page('themes.php', $customizer_url); // hide the customizer submenu
		}
	}

	/**
	 * Allow SEO manager to edit redirections
	 */
	function allow_seo_manager_to_edit_redirections($role) {
		return 'edit_posts';
	}

	/**
	 * Determine if event text has block
	 */
	function event_text_has_block($block_name) {

		global $params;

		if ($params && array_key_exists('event_slug', $params)) {

			// Determine if this is racing/non-racing event.
			if ($params['event_type'] == 'event') {
				$tag_to_lookup = 'Event';
			} else {
				$tag_to_lookup = 'Racing';
			}

			$events = Tickster_Events_Public::get_events_by_slug($params['event_slug'], $tag_to_lookup);

			// Find WP post object that connects with this event.
			$connected_post = get_posts([
				'numberposts'   => 1,
				'post_type'     => 'eventtexts',
				'meta_key'      => 'tickster_event',
				'meta_value'    => $events[0]->id,
				'meta_compare'  => 'LIKE'
			]);

			$has_map_block = false;
			if ($connected_post) {
				if (has_block($block_name, $connected_post[0])) {
					$has_map_block = true;
				}
			}

			return $has_map_block;
		}
	}

	/**
	 * Add admin bar buttons to manually fetch data from APIs.
	 */
	public function admin_bar_fetch_tickster_events($admin_bar) {

		// Allow for administrator and editor roles
		if (!current_user_can('publish_pages')) {
			return;
		}

		// YouTube Videos
		$tickster_args = [
			'id' => 'youtube-admin-bar-button',
			'title' => '<span class="ab-label">' . esc_html__('Fetch YouTube Videos', 'solvalla') . '</span>',
			'parent' => 'top-secondary',
			'href' => get_site_url() . '/wp-json/youtube/v1/update/' . $_ENV['UPDATE_YOUTUBE_TOKEN'],
			'meta'  => [
				'target' => '_blank'
			]
		];
		$admin_bar->add_node($tickster_args);

		// Libsyn Podcasts
		$libsyn_args = [
			'id' => 'libsyn-admin-bar-button',
			'title' => '<span class="ab-label">' . esc_html__('Fetch Libsyn Podcasts', 'solvalla') . '</span>',
			'parent' => 'top-secondary',
			'href' => get_site_url() . '/wp-json/libsyn/v1/update/M6REYA7QfVQYK4iZAB',
			'meta'  => [
				'target' => '_blank'
			]
		];
		$admin_bar->add_node($libsyn_args);

		// Tickster Events
		$tickster_args = [
			'id' => 'tickster-admin-bar-group',
			'title' => '<span class="ab-label">' . esc_html__('Fetch Tickster Events', 'solvalla') . '</span>',
			'parent' => 'top-secondary',
			'href' => get_site_url() . '/wp-json/tickster/v1/update/QNKkQNvuVdE3Y2Epex',
			'meta'  => [
				'target' => '_blank'
			]
		];
		$admin_bar->add_node($tickster_args);

		// Child Link 1
		$link1_args = [
			'id'     => 'tickster-link-1',
			'title'  => esc_html__('Fetch Solvalla 2024', 'solvalla'),
			'parent' => 'tickster-admin-bar-group',
			'href'   => get_site_url() . '/wp-json/tickster/v1/update/QNKkQNvuVdE3Y2Epex?query=solvalla_2024',
			'meta'   => [
				'target' => '_blank',
			],
		];
		$admin_bar->add_node($link1_args);

		// Child Link 2
		$link2_args = [
			'id'     => 'tickster-link-2',
			'title'  => esc_html__('Fetch Elitloppet 2025', 'solvalla'),
			'parent' => 'tickster-admin-bar-group',
			'href'   => get_site_url() . '/wp-json/tickster/v1/update/QNKkQNvuVdE3Y2Epex?query=elitloppet_2025',
			'meta'   => [
				'target' => '_blank',
			],
		];
		$admin_bar->add_node($link2_args);

		// Child Link 3
		$link3_args = [
			'id'     => 'tickster-link-3',
			'title'  => esc_html__('Fetch Solvalla 2025', 'solvalla'),
			'parent' => 'tickster-admin-bar-group',
			'href'   => get_site_url() . '/wp-json/tickster/v1/update/QNKkQNvuVdE3Y2Epex?query=solvalla_2025',
			'meta'   => [
				'target' => '_blank',
			],
		];
		$admin_bar->add_node($link3_args);

	}

	/**
	 * Create custom database table for YouTube videos.
	 */
	function add_custom_table_for_youtube() {
		global $wpdb;

		$table_name = $wpdb->prefix . 'youtube';
		$charset_collate = $wpdb->get_charset_collate();

		$sql = "CREATE TABLE IF NOT EXISTS $table_name (
			id mediumint(10) NOT NULL AUTO_INCREMENT,
			video_id varchar(255) NOT NULL,
			title text NOT NULL,
			video_url text NOT NULL,
			published datetime NOT NULL,
			thumbnail_url text NOT NULL,
			views mediumint(10) NOT NULL,
			live tinyint(1) NOT NULL,
			PRIMARY KEY (id)
		) $charset_collate;";

		require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
		dbDelta($sql);
	}

	/**
	 * Endpoint for fetching YouTube videos for Solvalla TV.
	 */
	function update_youtube_endpoint() {
		register_rest_route(
			'youtube/v1',
			'/update/(?P<token>[a-zA-Z0-9-]+)',
			array(
				'methods'       		=> 'GET',
				'callback'      		=> array($this, 'update_youtube'),
				'permission_callback' 	=> '__return_true',
				'show_in_index' 		=> false
			)
		);
	}

	/**
	 * Update Solvalla TV by fetching YouTube videos and checking for current live streams.
	 */
	function update_youtube($request) {

		$response = '';

		if ($request['token'] === $_ENV['UPDATE_YOUTUBE_TOKEN'] && $_ENV['YOUTUBE_CHANNEL_ID'] !== '' && $_ENV['YOUTUBE_CHANNEL_NAME'] !== '') {

			// Update videos
			get_youtube_videos_from_xml($_ENV['YOUTUBE_CHANNEL_ID']);
			save_live_youtube_video_id($_ENV['YOUTUBE_CHANNEL_NAME']);

			// Purge URLs where videos are displayed (using Purge Varnish plugin)
			if (class_exists('Purge_Varnish')) {
				$purge_varnish = new Purge_Varnish();
				$purge_varnish->purge_varnish_url([
					get_site_url(), 			// Homepage
					get_site_url() . '/tv/' 	// Solvalla TV
				]);
			}

			// Success response
			$response = new WP_REST_Response([
				'status'        => 'youtube_updated',
				'response'      => 'YouTube videos in Solvalla TV were successfully updated.'
			]);
			$response->set_status(200);
		} else {

			// Not authenticated response
			$response = new WP_Error(
				'rest_api_invalid',
				'Could not authenticate the request.',
				array('status' => 401)
			);
		}

		return $response;
	}

	/** 
	 * Sort Slider Priority rules in Theme Settings by priority.
	 */
	function sort_slider_rules_by_priority( $value, $post_id, $field ) {
		$order = array();
		
		if (empty($value)) {
			return $value;
		}
		
		foreach ($value as $i => $row) {
			$order[$i] = $row['field_67d2d981f07d5'];
		}
		
		array_multisort($order, SORT_ASC, $value);
		
		return $value;
	}

	/**
	 * Remove Tickster Replace Event Details for expired events.
	 */
	function remove_tickster_replacements_for_expired_events() {
		error_log("Checking for expired events");
		$replacements = get_field('tickster_replace_event_details', 'option');
		if (!empty($replacements)) {
			// First collect all event IDs
			$event_ids = [];
			foreach ($replacements as $replacement) {
				if (isset($replacement['which_event']) && $replacement['which_event'] !== '') {
					$event_ids[] = $replacement['which_event'];
				}
			}
			
			// Remove duplicates
			$event_ids = array_unique($event_ids);
			
			// Get event details for all IDs
			$expired_ids = [];
			date_default_timezone_set("Europe/Stockholm");
			foreach ($event_ids as $event_id) {
				error_log("Checking event: " . $event_id);
				$event = Tickster_Events_Public::get_event_details_by_id($event_id);
				error_log("Event end: " . strtotime($event->event_end));
				error_log("Current time: " . time());
				if (isset($event->event_end) && strtotime($event->event_end) < time()) {
					error_log("Event expired, marking for removal");
					$expired_ids[] = $event_id;
				}
			}
			
			// Remove expired events from replacements
			foreach ($replacements as $key => $replacement) {
				if (isset($replacement['which_event']) && in_array($replacement['which_event'], $expired_ids)) {
					unset($replacements[$key]);
				}
			}
			
			update_field('tickster_replace_event_details', $replacements, 'option');
			error_log("Updating replacements");
		}
	}

	/*
	* Custom language switcher shortcode
	*/
	function trpc_custom_language_switcher() {
		if (function_exists('trp_custom_language_switcher')) {
			// Check whether TranslatePress can run on the current path or not. If the path is excluded from translation, trp_allow_tp_to_run will be false
			if (apply_filters('trp_allow_tp_to_run', true)) {
				$languages = trp_custom_language_switcher();
				$current_language = get_locale();
				$html = '';
				foreach ($languages as $item) {
					if ($current_language !== $item['language_code']) {
						$html .= "<li itemprop='availableLanguage' itemscope='itemscope' itemtype='https://schema.org/Language' class='nav-item nav-item-lang' data-no-translation>";
						$html .= "<a class='nav-link' href='{$item['current_page_url']}'>";
						$html .= "<img src='{$item['flag_link']}' alt='{$item['language_name']}'>";
						$html .= "<span>{$item['language_name']}</span>";
						$html .= "</a>";
						$html .= "</li>";
					}
				}
				// For testing - later when English language is activated, this can be removed
				if ($html === '' && current_user_can('administrator')) {
					$html .= '<li itemprop="availableLanguage" itemscope="itemscope" itemtype="https://schema.org/Language" class="nav-item nav-item-lang" data-no-translation=""><a class="nav-link" href="'. get_site_url() . '/eng/"><img src="'. get_site_url() . '/wp-content/plugins/translatepress-multilingual/assets/images/flags/en_US.png" alt="English"><span>English</span></a></li>';
				}
				return $html;
			}
		}
	}

	// Here you can add your own fuctions to Twig. Don't worry about this section if you don't come across a need for it.
	// See more here: http://twig.sensiolabs.org/doc/advanced.html
	// function add_to_twig( $twig ) {
	// 	$twig->addExtension( new Twig_Extension_StringLoader() );
	// 	$twig->addFilter( 'myfoo', new Twig_Filter_Function( 'myfoo' ) );
	// 	return $twig;
	// }

}

new StarterSite();

// Set temlates locations for twig files
Timber::$dirname = '/templates/views';

/** 
 * Routes.
 */
require('lib/routes.php');

/** 
 * Check if page has custom slider block.
 */
function has_custom_slider_block($blocks) {
	foreach ($blocks as $block) {
		// Check if this block is a slider
		if (isset($block['blockName']) && 
			(strpos($block['blockName'], 'block-slider') !== false || 
			$block['blockName'] == 'acf/block-slider' || 
			$block['blockName'] == 'acf/block-slider-custom')) {
			return true;
		}
		
		// Check inner blocks if this is a group or has innerBlocks
		if (isset($block['innerBlocks']) && !empty($block['innerBlocks'])) {
			if (has_custom_slider_block($block['innerBlocks'])) {
				return true;
			}
		}
	}
	return false;
}
