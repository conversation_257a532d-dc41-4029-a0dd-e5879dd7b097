let countdown = document.getElementById('countdown');
let countdown_ended = document.getElementById('countdown-ended');
if( typeof( countdown ) != 'undefined' && countdown != null ) {

    const second = 1000,
        minute = second * 60,
        hour = minute * 60,
        day = hour * 24;

    let countdown_date = countdown.dataset.date;
  
    const countDown = new Date(countdown_date).getTime(),
    x = setInterval(function () {

        let now = new Date();
        now = new Date(now.getTime() - now.getTimezoneOffset() * 60000);

        // Fix timezone offset
        now.setHours(now.getHours() - 1);
        
        now = now.getTime();
        let distance = countDown - now;

        document.getElementById("countdown-days").innerText = padWithZero(Math.floor(distance / (day))),
        document.getElementById("countdown-hours").innerText = padWithZero(Math.floor((distance % (day)) / (hour))),
        document.getElementById("countdown-minutes").innerText = padWithZero(Math.floor((distance % (hour)) / (minute))),
        document.getElementById("countdown-seconds").innerText = padWithZero(Math.floor((distance % (minute)) / second));

        // Hide countdown when it's over, show message
        if (distance < 0) {
            countdown.remove();
            countdown_ended.classList.remove('d-none');
            clearInterval(x);
        }
    }, 1000)

}

// Function to pad a number with a leading zero if it's a single digit
function padWithZero(number) {
    return (number < 10 ? "0" : "") + number;
}