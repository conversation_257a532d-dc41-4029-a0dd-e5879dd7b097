import axios from 'axios';

let block_leaderboard = document.querySelector('.block--leaderboard');

// const tokenEndpoint = '/token.php';
// const endpoint = 'https://www.solvalla.se/APIproxy.php';
const endpoint = '/sollvalaserien.php';
const yearSelect = document.getElementById('year-select');
const meetingSelect = document.getElementById('meeting-select');
const divisionsButtons = document.querySelectorAll('.js-divisions-serien');
const errorMeetings = document.querySelector('.js-error-meetings');
const meetTable = document.getElementById('meet-table');

let currentYear = new Date().getFullYear();
let meetingsEndpoint = `/meetings?year=${currentYear}&trackId=1`;
let meetId;
let divisionsEndpoint = '/meetings/divisions?meetingId=2467&trackId=1';
let leaderboardEndpoint = '/meetings/leaderboard?meetingId=2467&divisionId=150';

if (typeof block_leaderboard != 'undefined' && block_leaderboard != null) {
  yearSelect.addEventListener('change', handleYearChange);
  meetingSelect.addEventListener('change', handleMeetingChange);
  divisionsButtons.forEach((button) => {
    button.addEventListener('click', handleDivisionClick);
  });
}

async function handleYearChange() {
  meetingsEndpoint = `/meetings?year=${yearSelect.value}&trackId=1`;
  buttonsRemoveActive();
  await getMeetings();
  await getDivisions();
}

async function handleMeetingChange() {
  meetId = meetingSelect.value;
  divisionsEndpoint = `/meetings/divisions?meetingId=${meetId}&trackId=1`;
  buttonsRemoveActive();
  const firstButton = document.querySelector('.serien-150');
  const divisionId = firstButton.getAttribute('data-division');
  firstButton.classList.add('active');
  leaderboardEndpoint = `/meetings/leaderboard?meetingId=${meetId}&divisionId=${divisionId}`;
  await getScoreboard();
}

function handleDivisionClick() {
  buttonsRemoveActive();
  this.classList.add('active');
  const divisionId = this.getAttribute('data-division');
  leaderboardEndpoint = `/meetings/leaderboard?meetingId=${meetId}&divisionId=${divisionId}`;
  getScoreboard();
}

async function checkCurrentMeeting(meetingID) {
  try {
    const response = await axios.post(endpoint, {
      action: 'getData',
      url_params:
        '/meetings/leaderboard?meetingId=' + meetingID + '&divisionId=2446',
      token: localStorage.getItem('token'),
    });
    if (response.status === 200) {
      const racesArray = response.data['races'];
      let nowTs = Date.now();
      let minTs = Date.parse(racesArray[0]['raceDate']);
      let maxTs = Date.parse(racesArray[racesArray.length - 1]['raceDate']);
      if (nowTs >= minTs && nowTs <= maxTs) {
        return meetingID;
      }
    }
  } catch (error) {
    console.error(error);
  }
}

async function getMeetings() {
  try {
    const response = await axios.post(endpoint, {
      action: 'getData',
      url_params: meetingsEndpoint,
      token: localStorage.getItem('token'),
    });
    // console.log(response); // Add this line for debugging
    if (response.status === 200) {
      const data = response.data['_embedded']['meetingResources'];
      const options = data.map((item) => {
        return {
          name: item.name,
          link: item.meetingid,
        };
      });
      if (options.length === 0) {
        // Show message indicating that there is no data available for the selected year
        errorMeetings.textContent = 'No data available for selected year';
        errorMeetings.classList.add('active');
      } else {
        // Hide the error message and populate the meeting select with the options
        errorMeetings.classList.remove('active');
        meetingSelect.innerHTML = '';
        for (const option of options) {
          const optionElement = document.createElement('option');
          optionElement.text = option.name;
          optionElement.value = option.link;

          if ((await checkCurrentMeeting(option.link)) === option.link) {
            optionElement.selected = true;
          }
          meetingSelect.appendChild(optionElement);
        }
        meetId = meetingSelect.value;
        divisionsEndpoint = `/meetings/divisions?meetingId=${meetId}&trackId=1`;
        const firstButton = document.querySelector('.serien-150');
        const divisionId = firstButton.getAttribute('data-division');
        firstButton.classList.add('active');
        leaderboardEndpoint = `/meetings/leaderboard?meetingId=${meetId}&divisionId=${divisionId}`;
        await getDivisions();
        await getScoreboard();
      }
    }
  } catch (error) {
    console.error(error);
  }
}

async function getDivisions() {
  try {
    const response = await axios.post(endpoint, {
      action: 'getData',
      url_params: divisionsEndpoint,
      token: localStorage.getItem('token'),
    });
    if (response.status === 200) {
      const data = response.data['_embedded']['divisionResources'];
      const options = data.map((item) => {
        return {
          name: item.divisionId,
        };
      });
      divisionsButtons.forEach((button, index) => {
        button.setAttribute('data-division', options[index].name);
      });
    }
  } catch (error) {
    console.error(error);
  }
}

async function getScoreboard() {
  try {
    const response = await axios.post(endpoint, {
      action: 'getData',
      url_params: leaderboardEndpoint,
      token: localStorage.getItem('token'),
    });
    if (response.status === 200) {
      const data = response.data['leaderboard'];
      const options = data.map((item) => {
        return {
          races: item.numberOfRaces,
          horse: item.resultHorseResource.name,
          points: item.totalPoints,
          trainer: item.trainerResource.name,
        };
      });
      errorMeetings.classList.toggle('active', data.length === 0);
      meetTable.innerHTML = '';
      options.forEach((row, index) => {
        const tr = document.createElement('tr');
        const positionTd = document.createElement('td');
        positionTd.textContent = index + 1;
        tr.appendChild(positionTd);
        const horseTd = document.createElement('td');
        horseTd.textContent = row.horse;
        tr.appendChild(horseTd);
        const pointsTd = document.createElement('td');
        pointsTd.textContent = row.points;
        tr.appendChild(pointsTd);
        const racesTd = document.createElement('td');
        racesTd.textContent = row.races;
        tr.appendChild(racesTd);
        const trainerTd = document.createElement('td');
        trainerTd.textContent = row.trainer;
        tr.appendChild(trainerTd);
        meetTable.appendChild(tr);
      });
    }
  } catch (error) {
    console.error(error);
  }
}

function buttonsRemoveActive() {
  divisionsButtons.forEach((button) => {
    button.classList.remove('active');
  });
}

async function getToken() {
  try {
    const response = await fetch('/token.php', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });
    const data = await response.json();
    const token = data.token;

    // Store the token in a secure location (e.g. local storage)
    // You can store it as plain text or encrypt it
    localStorage.setItem('token', token);

    // Set the token expiration time to 24 hours from now
    const now = new Date();
    const expirationTime = now.getTime() + 24 * 60 * 60 * 1000;
    localStorage.setItem('tokenExpiration', expirationTime);

    return token;
  } catch (error) {
    console.error('Token retrieval error:', error);
  }
}

async function init() {
  const token = localStorage.getItem('token');
  const expiration = localStorage.getItem('tokenExpiration');
  if (!token || new Date().getTime() > expiration) {
    await getToken();
  }
  await getMeetings();
}

if (typeof block_leaderboard != 'undefined' && block_leaderboard != null) {
  init();
}
