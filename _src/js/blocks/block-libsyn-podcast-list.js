// THIS functionality is currently disables as requested by client, on build the commented out code is not included, since decided to keep it for future reference (this made the podcast block display an image on hover)
// import { gsap } from  'gsap';

// // Only run on page where <PERSON><PERSON>yn podcast list block is present
// let block_podcast_list = document.querySelector('.block--libsyn-podcast-list');
// if( typeof( block_podcast_list ) != 'undefined' && block_podcast_list != null ) {

//     // Only target mouse/touchpad devices (exclude touchscreens and stylus based screens)
//     const mediaQuery = window.matchMedia('(hover: hover) and (pointer: fine)');
//     if( mediaQuery.matches ) {

//         // Show image on hover over event and follow the cursor
//         gsap.set('.podcast-list__episode__img', { yPercent: 0, xPercent: 7.5 })

//         gsap.utils.toArray(".podcast-list__episode").forEach((el) => {
//             const episode_id = el.getAttribute('data-episode-id');
//             const image = document.querySelector('.podcast-list__episode__img[data-episode-id="' + episode_id + '"]'),
//             setX = gsap.quickSetter(image, "x", "px"),
//             setY = gsap.quickSetter(image, "y", "px"),
//             align = e => {
//                 setX(e.clientX);
//                 setY(e.clientY);
//             },
//             startFollow = () => document.addEventListener("mousemove", align),
//             stopFollow = () => document.removeEventListener("mousemove", align),
//             fade = gsap.to(image, {autoAlpha: 1, duration: 0.25, paused: true, onReverseComplete: stopFollow});

//             el.addEventListener('mouseenter', (e) => {
//                 fade.play();
//                 startFollow();
//                 align(e);
//             });

//             el.addEventListener('mouseleave', () => fade.reverse());

//             el.addEventListener('click', () => fade.reverse());

//             let calendar_links = el.querySelectorAll('.podcast-list__episode .text-end');
//             calendar_links.forEach( (el) => {
//                 el.addEventListener('mouseenter', () => fade.reverse());
//             });
//             calendar_links.forEach( (el) => {
//                 el.addEventListener('mouseleave', (e) => {
//                     fade.play();
//                     startFollow();
//                     align(e);
//                 });
//             });

//         });

//     }

// }
