/* global ActiveXObject */
/* global Plyr */
/* global smartcrop */

document.addEventListener( 'DOMContentLoaded', function() {

    /**
     * Fix viewport on iOS
     */
    let vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    // window.addEventListener('resize', () => {
    //     let vh = window.innerHeight * 0.01;
    //     document.documentElement.style.setProperty('--vh', `${vh}px`);
    // });

    /**
     * Scroll direction check
     */
    let doc = document.documentElement;
    let w = window;

    let prevScroll = w.scrollY || doc.scrollTop;
    let curScroll;
    let direction = 0;
    let prevDirection = 0;

    let body = document.querySelector('body');
    let header = document.querySelector('header.site-header');
    let top_image = document.querySelector('.top-image');
    let offset = header.offsetHeight;

    // Check if mouse is hovered over the header
    let header_mouse_over = false;
    header.addEventListener( 'mouseenter', function() {
        header_mouse_over = true;
    });
    header.addEventListener( 'mouseleave', function() {
        header_mouse_over = false;
    });

    let scroll_check = function() {

        top_image = document.querySelector('.top-image.active');
        let style_offset = offset;
        if( typeof( top_image ) != 'undefined' && top_image != null ) {
            if( top_image.offsetHeight > offset ) {
                style_offset = top_image.offsetHeight - offset;
            }
        }

        // scrolled
        if( w.scrollY >= style_offset ) {
            body.classList.add('scroll')
        } else {
            body.classList.remove('scroll')
        }

        /*
        ** Find the direction of scroll
        ** 0 - initial, 1 - up, 2 - down
        */
        curScroll = w.scrollY || doc.scrollTop;

        // scrolled up
        if( curScroll > prevScroll ) {
            direction = 2;
        }

        // scrolled down
        else if( curScroll < prevScroll ) {
            direction = 1;
        }

        if( direction !== prevDirection ) {
            toggle_header( direction, curScroll );
        }

        prevScroll = curScroll;
    };

    let toggle_header = function( direction, curScroll ) {
        if( direction === 2 && curScroll > offset ) {
            header.classList.add('hide');
            prevDirection = direction;
        }
        else if( direction === 1 ) {
            header.classList.remove('hide');
            prevDirection = direction;
        }
    };

    window.addEventListener( 'load', scroll_check );
    window.addEventListener( 'scroll', function() {

        // Only check scroll direction if mouse is not hovered over the header
        var mobile_meda_query = window.matchMedia("(max-width: 991px)");
        if( mobile_meda_query.matches === false ) {
            if( header_mouse_over === false ) {
                scroll_check();
            }
        } else {
            scroll_check();
        }

    } );

    /**
     * Mega menu
     */
    let mega_menu_item = document.querySelectorAll('.mega-menu__link');
    if( mega_menu_item.length > 0 ) {
        mega_menu_item.forEach( function( single_mega_menu_item ) {
            let mega_menu_parent = single_mega_menu_item.closest('.mega-menu__parent');
            mega_menu_parent.addEventListener( 'mouseenter', function() {
                mega_menu_parent.classList.add('show');
                body.classList.add('menu-open');
            });
            mega_menu_parent.addEventListener( 'mouseleave', function() {
                mega_menu_parent.classList.remove('show');
                body.classList.remove('menu-open');
            });
        });
    }

    /**
     * Mobile menu
     */
    let mobile_menu = document.getElementById('navbarNavDropdownMobile');
    mobile_menu.addEventListener( 'show.bs.collapse', function() {
        body.classList.add('menu-open');
        body.classList.add('mobile-menu-open');
    });
    mobile_menu.addEventListener( 'hide.bs.collapse', function() {
        body.classList.remove('menu-open');
        body.classList.remove('mobile-menu-open');
    });

    /**
     * Apply first link to element.
     */
    let apply_first_link = document.querySelectorAll('.apply-first-link,.wp-block-button');
    if( apply_first_link.length > 0 ) {
        apply_first_link.forEach( function( link ) {
            link.addEventListener( 'click', function(e) {
                if( e.target.tagName === 'A' ) {
                    e.stopPropagation();
                    return;
                }

                var a = this.querySelector('a:first-child');
                var a_href = a.getAttribute('href');
                var a_target = a.getAttribute('target');

                if( a_target === '_blank' ) {
                    window.open( a_href, '_blank' );
                } else {
                    window.location = a_href;
                }

                return false;
            })
        })
    }

    /**
     * Add data attribute (needed for text slice hover effect) to styled links, and wrap inner text by span tag.
     */
    let links_styled = document.querySelectorAll('.link-styled:not(.link-styled--arrow-top)');
    if( links_styled.length > 0 ) {
        links_styled.forEach( function( link ) {
            let link_inner = link.querySelector('a');
            let link_inner_text = link_inner.innerText;
            link.classList.add('d-flex', 'apply-first-link');
            link_inner.innerHTML = '<span>' + link_inner_text + '</span>';
            link_inner.setAttribute('data-link-alt', link_inner_text);
            link_inner.classList.add('d-flex', 'link-slice-hover', 'align-items-center');
        })
    }

    /**
     * Smart crop images for mobile and tablet
     * This functionality has been moved to the carousel-preloader module
     * to ensure it works with lazy-loaded slides
     */

    /**
     * Ajax call
     */
    function post_ajax( url, data, success ) {
        let params = typeof data == 'string' ? data : Object.keys(data).map(
            function( k ) { return encodeURIComponent( k ) + '=' + encodeURIComponent( data[k] ) }
        ).join('&');
        let xhr = window.XMLHttpRequest ? new XMLHttpRequest() : new ActiveXObject( "Microsoft.XMLHTTP" );
        xhr.open( 'POST', url );
        xhr.onreadystatechange = function() {
            if( xhr.readyState > 3 && xhr.status == 200 ) { success( xhr.responseText ); }
        };
        xhr.setRequestHeader( 'X-Requested-With', 'XMLHttpRequest' );
        xhr.setRequestHeader( 'Content-Type', 'application/x-www-form-urlencoded' );
        xhr.send(params);
        return xhr;
    }

    /**
     * Load more content
     */
    let load_more_btns = document.querySelectorAll('#load-more');
    load_more_btns.forEach( function( load_more ) {
        load_more.addEventListener( 'click', function(e) {
            let target = e.target;
            let button = target.closest( 'button' );
            if( button.classList.contains( 'loading' ) ) {
                return;
            }
            button.classList.add( 'loading' );
            button.querySelector( 'span' ).innerHTML = 'Loading';
            let page = parseInt( button.getAttribute('data-page') );
            let block_name = button.getAttribute('data-block-name');
            let block_id = button.getAttribute('data-block-id');
            if( block_name === 'acf/block-libsyn-podcast' ) {
                let block = document.querySelector( '#' + block_id );
                let block_container = block.querySelector( '.container-fluid' );
                let page_request = page + 1;
                post_ajax( window.global_object.ajax_url, { action: 'get_more_podcast_episodes_json', page: page_request }, function( response ) {
                    let response_obj = JSON.parse( response );
                    button.setAttribute( 'data-page', page_request );
                    block_container.innerHTML += create_podcast_html( response_obj );
                    button.classList.remove('loading');
                    button.querySelector( 'span' ).innerHTML = 'Load more';
                    document.activeElement.blur();
                })
            }
        })
    })

    /**
     * Create podcast HTML
     */
    function create_podcast_html( json ) {
        let links = json.links;
        let links_html= '';
        links.forEach( function( link ) {
            links_html += '<a class="btn btn--large btn--' + link.slug + '" href="' + link.url + '" target="_blank">' + link.name + '</a>';
        });
        let episodes = json.episodes;
        if( episodes.length ) {
            return episodes.map( function( episode ) {
                return `
                <div class="article--podcast row content-block border-custom border-custom--bottom">
                    <div class="article--podcast__col pe-lg-4 pe-md-3 mb-md-0 mb-4">
                        ${episode.thumbnail_url ? '<a href="/podcast/' + episode.slug + '"><img class="img-fluid" width="748" height="421" src="' + episode.thumbnail_url + '" alt="' + episode.title + '"></a>' : ''}
                    </div>
                    <div class="article--podcast__col ps-md-3">
                        <div class="article--podcast__col__inner">
                            <h3 class="text-uppercase h2">
                                <a class="link-mine-shaft text-decoration-none" href="/podcast/${episode.slug}">
                                    #${episode.episode_id} ${episode.title}
                                </a>
                            </h3>
                            <div class="article--podcast__description d-none d-md-block">
                                ${episode.excerpt ? episode.excerpt : ''}
                            </div>
                        </div>
                        <div class="d-block d-md-none fw-bold text-uppercase mt-4 mb-2">Listen us on:</div>
                        <div class="article--podcast__share-links d-flex flex-wrap gap-2 align-items-center">
                            ${links_html}
                        </div>
                        <div class="link-styled link-styled--arrow-top">
                            <a class="calendar__event__link link-mine-shaft" href="/podcast/${episode.slug}"></a>
                        </div>
                    </div>
                </div>
                `
            }).join('')
        } else {
            return `<div class="text-center">No more episodes</div>`;
        }
    }

    /**
     * Single podcast page - play/pause audio on thumbnail image click
     */
    let audio_player = document.querySelector('.article--podcast__player__audio');
    if( typeof( audio_player ) != 'undefined' && audio_player != null ) {

        // Initialize Plyr
        new Plyr( audio_player );

        let audio_control = document.querySelector('.article--podcast__player__control');

        audio_control.addEventListener('click', function(e) {
            let target = e.target;
            let button = target.closest('.article--podcast__player__control');
            if( button.classList.contains('playing') ) {
                audio_player.pause();
                button.classList.remove('playing');
            } else {
                audio_player.play();
                button.classList.add('playing');
            }
        })
        audio_player.addEventListener('play', function() {
            let button = document.querySelector('.article--podcast__player__control');
            button.classList.add('playing');
        })
        audio_player.addEventListener('pause', function() {
            let button = document.querySelector('.article--podcast__player__control');
            button.classList.remove('playing');
        })
    }

}, false );

// Import carousel preloader first to ensure it's available early
import '../modules/carousel-preloader.js';
import '../modules/equal-height-headings.js'; // Import our equal height headings script
import { dynamicImport } from "../common/imports";
import 'lightbox2/dist/js/lightbox.min.js';

dynamicImport("[data-slick]", "slick-carusel");
dynamicImport("[data-src]", "lazy-load");
dynamicImport("[data-banner]", "click-counter");
