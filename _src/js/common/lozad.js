import lozad from "lozad";
import { breakpoints } from "./breakpoints";

const lazyLoad = (domElement) => {
  let screen = document.documentElement.clientWidth;
  const images = document.querySelectorAll(`${domElement}`);
  const observer = lozad(images, {
    enableAutoReload: true,
    loaded: (element) => {
      const loadedElement = $(element);

      if (!loadedElement.attr("data-background-image")) {
        loadedElement.animate({ opacity: "1" }, 250);
      }
    },
    load: (item) => {
      const breakpointUp = item.getAttribute("data-src-load-after");
      const breakpointDown = item.getAttribute("data-src-load-before");

      if (breakpointUp && screen < breakpoints[breakpointUp]) {
        return;
      }

      if (breakpointDown && screen > breakpoints[breakpointDown]) {
        return;
      }

      if (item.getAttribute("data-background-image")) {
        let attr = item.getAttribute("data-background-image");

        if (screen >= breakpoints.md && item.getAttribute(`data-background-image-md`)) {
          attr = item.getAttribute(`data-background-image-md`);
        }

        item.style.backgroundImage = `url(${attr})`;
      } else if (item.getAttribute("data-image-md")) {
        let attr = item.getAttribute("data-src");

        if (screen < breakpoints.md && item.getAttribute(`data-mobile-width`) && item.getAttribute(`data-mobile-height`)) {
          item.setAttribute("width", item.getAttribute(`data-mobile-width`));
          item.setAttribute("height", item.getAttribute(`data-mobile-height`));
        }

        if (screen >= breakpoints.md && item.getAttribute(`data-image-md`)) {
          attr = item.getAttribute(`data-image-md`);
        }

        item.src = attr;
      } else {
        let attr = item.getAttribute("data-src");

        item.src = attr;
      }
    },
  });

  observer.observe();
};

export { lazyLoad };
