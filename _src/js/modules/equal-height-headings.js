/**
 * Equal Height Headings
 *
 * This script equalizes the height of headings within columns that have the 'equal-height-headings' class.
 * It dynamically adjusts the heights based on the tallest heading in each row.
 * Only applies on screens 782px and wider.
 */

(function() {
    'use strict';

    /**
     * Check if the screen is at least 782px wide
     * @returns {boolean} True if screen width is 782px or more
     */
    function isDesktopView() {
        return window.innerWidth >= 782;
    }

    /**
     * Equalizes the height of headings within columns
     */
    function equalizeHeadings() {
        // Find all column blocks with the equal-height-headings class
        const columnBlocks = document.querySelectorAll('.wp-block-columns.equal-height-headings');

        if (!columnBlocks.length) return;

        // Process each column block
        columnBlocks.forEach(block => {
            const headings = block.querySelectorAll('.wp-block-column > .wp-block-heading');

            // If on mobile (<782px), remove any height styles
            if (!isDesktopView()) {
                headings.forEach(heading => {
                    heading.style.height = '';
                });
                return;
            }

            // Reset heights first (important for resize events)
            headings.forEach(heading => {
                heading.style.height = 'auto';
            });

            // Get the tallest heading height
            let maxHeight = 0;
            headings.forEach(heading => {
                const height = heading.offsetHeight;
                if (height > maxHeight) {
                    maxHeight = height;
                }
            });

            // Set all headings to the tallest height
            if (maxHeight > 0) {
                headings.forEach(heading => {
                    heading.style.height = maxHeight + 'px';
                });
            }
        });
    }

    // Run on page load
    document.addEventListener('DOMContentLoaded', function() {
        equalizeHeadings();

        // Also run on window resize with debounce
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(equalizeHeadings, 250);
        });
    });

})();
