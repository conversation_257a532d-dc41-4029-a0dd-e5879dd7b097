/**
 * Enhanced carousel preloader
 * Preloads adjacent slides to ensure smooth transitions while maintaining lazy loading benefits
 */

(() => {
    // Function to check if SmartCrop is loaded
    function isSmartCropLoaded() {
        return typeof window.smartcrop !== 'undefined';
    }

    // Function to wait for SmartCrop to be available
    function waitForSmartCrop(callback, maxAttempts = 20) {
        let attempts = 0;

        const checkSmartCrop = function() {
            if (isSmartCropLoaded()) {
                callback();
            } else if (attempts < maxAttempts) {
                attempts++;
                //console.log('Waiting for SmartCrop to load... Attempt ' + attempts);
                setTimeout(checkSmartCrop, 200);
            } else {
                //console.error('SmartCrop failed to load after ' + maxAttempts + ' attempts');
            }
        };

        checkSmartCrop();
    }

    // Function to process SmartCrop for a slide
    function processSmartCrop(slide, desktopImg) {
        // Skip if already processed
        if (slide.dataset.smartcropProcessed === 'true') return;

        // Wait for SmartCrop to be available
        waitForSmartCrop(function() {
            // Mobile settings
            const mobileSettings = {
                debug: true,
                width: 600,
                height: 1200,
            };

            // Process mobile image
            window.smartcrop.crop(desktopImg, mobileSettings, function(result) {
                // Determine focus point
                let focusPoint = 'left';
                if (result.topCrop.x > (1920 / 2)) {
                    focusPoint = 'right';
                }

                // Remove any pre-positioning classes
                slide.classList.remove('pre-position-left', 'pre-position-right');

                // Add the focus point class
                slide.classList.add('focus-point-' + focusPoint);

                // Create cropped image
                const crop = result.topCrop;
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = mobileSettings.width;
                canvas.height = mobileSettings.height;
                ctx.drawImage(desktopImg, crop.x, crop.y, crop.width, crop.height, 0, 0, canvas.width, canvas.height);
                const base64 = canvas.toDataURL('image/jpeg', 0.85);

                // Update the image source
                const imgMobile = slide.querySelector('img.top-image__mobile');
                if (imgMobile) imgMobile.src = base64;

                // Mobile Large settings
                const mobileLargeSettings = {
                    debug: true,
                    width: 680,
                    height: 880,
                };

                // Process mobile large image
                window.smartcrop.crop(desktopImg, mobileLargeSettings, function(result) {
                    const crop = result.topCrop;
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = mobileLargeSettings.width;
                    canvas.height = mobileLargeSettings.height;
                    ctx.drawImage(desktopImg, crop.x, crop.y, crop.width, crop.height, 0, 0, canvas.width, canvas.height);
                    const base64 = canvas.toDataURL('image/jpeg', 0.85);

                    // Update the image source
                    const imgMobileLarge = slide.querySelector('img.top-image__mobile_large');
                    if (imgMobileLarge) imgMobileLarge.src = base64;

                    // Tablet settings
                    const tabletSettings = {
                        debug: true,
                        width: 991,
                        height: 800,
                    };

                    // Process tablet image
                    window.smartcrop.crop(desktopImg, tabletSettings, function(result) {
                        const crop = result.topCrop;
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        canvas.width = tabletSettings.width;
                        canvas.height = tabletSettings.height;
                        ctx.drawImage(desktopImg, crop.x, crop.y, crop.width, crop.height, 0, 0, canvas.width, canvas.height);
                        const base64 = canvas.toDataURL('image/jpeg', 0.85);

                        // Update the image source
                        const imgTablet = slide.querySelector('img.top-image__tablet');
                        if (imgTablet) imgTablet.src = base64;

                        // Mark as processed
                        slide.dataset.smartcropProcessed = 'true';
                    });
                });
            });
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the carousel
        const carousel = document.getElementById('home-carousel');

        // Handle standalone top-image-tickster elements (like on single event pages)
        const standaloneTicksterImages = document.querySelectorAll('.top-image-tickster:not(.carousel-item)');
        if (standaloneTicksterImages.length > 0) {
            standaloneTicksterImages.forEach(function(ticksterElement) {
                const desktopImg = ticksterElement.querySelector('.top-image__desktop');
                if (desktopImg) {
                    // Create a new image object to ensure we have a fully loaded image
                    const img = new Image();
                    img.crossOrigin = "Anonymous"; // Enable CORS for canvas operations

                    img.onload = function() {
                        //console.log('Standalone image loaded via Image object, processing with SmartCrop');
                        processSmartCrop(ticksterElement, img);
                    };

                    // If the original image is already loaded, we can still use it
                    if (desktopImg.complete && desktopImg.naturalWidth) {
                        //console.log('Processing already loaded standalone image with SmartCrop');
                        processSmartCrop(ticksterElement, desktopImg);
                    } else {
                        // Set the source to trigger loading
                        img.src = desktopImg.src;

                        // Also set up a load event on the original image as a fallback
                        //console.log('Setting up load event for standalone SmartCrop');
                        desktopImg.addEventListener('load', function() {
                            if (!ticksterElement.dataset.smartcropProcessed) {
                                //console.log('Standalone image loaded via event, processing with SmartCrop');
                                processSmartCrop(ticksterElement, desktopImg);
                            }
                        });
                    }
                }
            });
        }

        // If no carousel found, we're done (standalone images are already processed above)
        if (!carousel) return;

        // Function to preload a specific slide's content
        function preloadSlideContent(slide) {
            if (!slide) return;

            // If the slide is already marked as preloaded but it's a Tickster slide that hasn't been processed,
            // we should still process it with SmartCrop
            if (slide.classList.contains('preloaded') &&
                slide.classList.contains('top-image-tickster') &&
                !slide.dataset.smartcropProcessed) {
                //console.log('Slide was marked as preloaded but SmartCrop not processed yet');
            } else if (slide.classList.contains('preloaded')) {
                return;
            }

            // Preload images
            const lazyImages = slide.querySelectorAll('img[loading="lazy"]');
            lazyImages.forEach(img => {
                // Add a load event to fade in the image
                if (!img.hasAttribute('data-load-listener')) {
                    img.setAttribute('data-load-listener', 'true');
                    img.addEventListener('load', function() {
                        // Add a small delay to ensure smooth transition
                        setTimeout(function() {
                            img.style.opacity = '1';
                        }, 50);
                    });
                }

                // Change loading attribute to eager to force loading
                img.setAttribute('loading', 'eager');
            });

            // Preload videos
            const lazySources = slide.querySelectorAll('source[data-src]');
            if (lazySources.length > 0) {
                // Get the video element
                const lazyVideo = lazySources[0].closest('video');

                // Set preload to metadata to start loading just the metadata
                lazyVideo.preload = 'metadata';

                // Load each source
                lazySources.forEach(function(lazySource) {
                    lazySource.src = lazySource.dataset.src;
                });

                // Load the video
                lazyVideo.load();

                // Add event listener for when metadata is loaded
                lazyVideo.addEventListener('loadedmetadata', function() {
                    // Now that metadata is loaded, we can start loading the actual video
                    if ('MediaSource' in window && MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E, mp4a.40.2"')) {
                        //console.log('Using MediaSource for progressive loading');
                        // Modern browsers with MediaSource Extensions support
                    } else {
                        // Fallback for browsers without MSE support
                        lazyVideo.preload = 'auto';
                    }
                });
            }

            // Process SmartCrop for Tickster slides
            if (slide.classList.contains('top-image-tickster')) {
                const desktopImg = slide.querySelector('.top-image__desktop');
                if (desktopImg) {
                    // Force loading of the image by changing loading attribute
                    if (desktopImg.getAttribute('loading') === 'lazy') {
                        desktopImg.setAttribute('loading', 'eager');
                    }

                    // Create a new image object to ensure we have a fully loaded image
                    const img = new Image();
                    img.crossOrigin = "Anonymous"; // Enable CORS for canvas operations

                    img.onload = function() {
                        //console.log('Image loaded via Image object, processing with SmartCrop');
                        processSmartCrop(slide, img);
                    };

                    // If the original image is already loaded, we can still use it
                    if (desktopImg.complete && desktopImg.naturalWidth) {
                        //console.log('Processing already loaded image with SmartCrop');
                        processSmartCrop(slide, desktopImg);
                    } else {
                        // Set the source to trigger loading
                        img.src = desktopImg.src;

                        // Also set up a load event on the original image as a fallback
                        //console.log('Setting up load event for SmartCrop');
                        desktopImg.addEventListener('load', function() {
                            if (!slide.dataset.smartcropProcessed) {
                                //console.log('Image loaded via event, processing with SmartCrop');
                                processSmartCrop(slide, desktopImg);
                            }
                        });
                    }
                }
            }

            // Mark slide as preloaded
            slide.classList.add('preloaded');
        }

        // Function to preload adjacent slides (next and previous)
        function preloadAdjacentSlides(activeSlideIndex) {
            const slides = carousel.querySelectorAll('.carousel-item');
            const totalSlides = slides.length;

            // Calculate next and previous slide indices (with wrap-around)
            const nextIndex = (activeSlideIndex + 1) % totalSlides;
            const prevIndex = (activeSlideIndex - 1 + totalSlides) % totalSlides;

            // Preload next slide
            preloadSlideContent(slides[nextIndex]);

            // Preload previous slide
            preloadSlideContent(slides[prevIndex]);
        }

        // Preload adjacent slides when a slide is shown
        carousel.addEventListener('slide.bs.carousel', function(event) {
            const nextSlide = event.relatedTarget;

            // Ensure the next slide is preloaded
            preloadSlideContent(nextSlide);

            // Find the index of the next slide
            const slides = carousel.querySelectorAll('.carousel-item');
            const nextSlideIndex = Array.from(slides).indexOf(nextSlide);

            // Preload slides adjacent to the next slide
            preloadAdjacentSlides(nextSlideIndex);
        });

        // Handle the currently active slide and preload adjacent slides on page load
        const activeSlide = carousel.querySelector('.carousel-item.active');
        if (activeSlide) {
            //console.log('Processing active slide on page load');

            // Process the active slide first to ensure it gets SmartCrop applied
            // Don't mark it as preloaded yet - let the preloadSlideContent function do that
            if (activeSlide.classList.contains('preloaded')) {
                activeSlide.classList.remove('preloaded');
            }

            // Process the active slide
            preloadSlideContent(activeSlide);

            // Find the index of the active slide
            const slides = carousel.querySelectorAll('.carousel-item');
            const activeSlideIndex = Array.from(slides).indexOf(activeSlide);

            // Preload adjacent slides
            preloadAdjacentSlides(activeSlideIndex);
        }

        // Load remaining slides with a delay to ensure smooth browsing experience
        setTimeout(function() {
            const slides = carousel.querySelectorAll('.carousel-item:not(.preloaded)');
            slides.forEach(function(slide, index) {
                // Stagger the loading to avoid overwhelming the browser
                setTimeout(function() {
                    preloadSlideContent(slide);
                }, index * 200); // 200ms delay between each slide
            });
        }, 2000); // 2 second initial delay
    });
})();
