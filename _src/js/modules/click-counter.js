(() => {
  let observer = new IntersectionObserver(
    (entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting || entry.isVisible) {
          const postId = entry.target.getAttribute('data-banner-id');
          fetch('/wp-admin/admin-ajax.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=update_view_count&post_id=' + postId,
          })
            .then(function (response) {
              return response.json();
            })
            .then(function (data) {
              console.log(data);
            });
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0, rootMargin: '200px 0px 0px 0px' }
  );

  const banners = document.querySelectorAll('.js-banner');
  banners.forEach(function (banner) {
    observer.observe(banner);
    banner.addEventListener('click', function (e) {
      const postId = banner.getAttribute('data-banner-id');
      fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=update_click_count&post_id=' + postId,
      })
        .then(function (response) {
          return response.json();
        })
        .then(function (data) {
          console.log(data);
        });
    });
  });
})();
