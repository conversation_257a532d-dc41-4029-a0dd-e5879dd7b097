import "magnific-popup";

(() => {
  const magnificFn = {
    init() {
      this.courseGallery();
      this.autoGallery();
    },
    courseGallery() {
      const courseGallery = $("[data-magnific-gallery]");

      if (!courseGallery.length) {
        return;
      }

      courseGallery.each(function () {

        // the containers for all your galleries
        $(this).magnificPopup({
          delegate: "a[data-magnific-gallery-type]",
          callbacks: {
            elementParse: function (item) {
              item.type = item.el.attr("data-magnific-gallery-type");
            },
          },
          type: "image",
          gallery: {
            enabled: true,
          },
        });
        
      });
    },
    autoGallery() {
      const autoGallery = $(".magnific-gallery-auto");

      if( !autoGallery.length ) {
        return;
      }

      autoGallery.each( function() {

        if( autoGallery.find("a[data-image='true']").length ) {

          $(this).magnificPopup({
            delegate: "a[data-image='true']",
            type: "image",
            callbacks: {
              elementParse: function (item) {
                item.type = "image";
              },
            },
            gallery: {
              enabled: true,
            },
          });

        } else {

          $(this).magnificPopup({
            delegate: ".wp-block-image > a",
            type: "image",
            callbacks: {
              elementParse: function (item) {
                item.type = "image";
              },
            },
            gallery: {
              enabled: true,
            },
          });

        }
          
      });
    }
  };

  magnificFn.init();
})();
