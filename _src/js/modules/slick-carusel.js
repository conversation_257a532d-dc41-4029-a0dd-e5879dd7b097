// this is higly likely redundant code that has not been used in months, still compiling it for some time, but to be removed in the future

import slick from "slick-carousel";
import { breakpoints, mediaBreakpoint } from "../common/breakpoints";

(() => {
  const slickInits = {
    init() {
      this.homeHeroCarusel();
    },

    homeHeroCarusel() {
      const homeHero = $("[data-slick='home-hero']");

      if (!homeHero.length) {
        return;
      }

      homeHero.slick({
        dots: false,
        autoplay: true,
        infinite: true,
        speed: 1000,
        arrows: false,
        slidesToShow: 1,
        slidesToScroll: 1,
        rows: 0,
        fade: true,
        draggable: false,
        mobileFirst: true,
        pauseOnFocus: false,
        pauseOnHover: false,
        autoplaySpeed: 3000,
      });
    },
  };

  slickInits.init();
})();
