// Fonts
$helvetica: 'Helvetica Now Display', sans-serif;
$poppins: 'Poppins', sans-serif;

// Colours
// ---------------------
$galliano: #e7b00d;
$cod-grey: #121212;
$mine-shaft: #343434;
$alto: #ddd;
$white: #fff;
$chathams-blue: #133a63;
$bermuda-gray: #7189a1;
$bermuda-gray-dark: #4f5a69;
$tall-poppy: #ba2c2c;
$scarpa-flow: #51515f;
$shark: #2A2E36;
$razzmatazz: #D0006F;
$green: #45857c;

$theme-colors: (
	"primary":  		$galliano,
	"secondary": 		$chathams-blue,
	"cod-grey":			$cod-grey,
	"mine-shaft":		$mine-shaft,
	"alto":				$alto,
	"tall-poppy": 		$tall-poppy,
	"scarpa-flow": 		$scarpa-flow,
	"white": 			$white,
	"green": 			$green,
);

@each $name, $color in $theme-colors {
	.has-#{$name}-color {
		color: $color;
	}
	.has-#{$name}-background-color {
		background-color: $color;
	}
}

// Header
// ---------------------
$dropdown-spacer: 1px;

// Borders
// ---------------------
$border-color: $alto;
$border-radius: 15px;

// Layout
// ---------------------
$grid-gutter-width: 15px!default;

// Grid
// ---------------------
$grid-breakpoints: (
	xs: 0,
	sm: 576px,
	md: 782px, // align this to Gutenberg mobile breakpoint
	lg: 992px,
	xl: 1200px,
	xxl: 1400px
);

// Typography
// ---------------------
$font-family-sans-serif: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;

$browser-context: 16px;
$font-size-base: 1rem; // $browser-context

$h1-font-size: $font-size-base * 2.55; // 48px
$h2-font-size: $font-size-base * 2; // 32px
$h3-font-size: $font-size-base * 1; // 16px

$display-font-sizes: (
	1: 6.8rem,
	2: 3.825rem,
	3: 3.5rem,
	4: 3rem,
	5: 2.55rem,
	6: 2rem
);

// Container
// ---------------------
// $container-max-widths: (
//   sm: 540px,
//   md: 720px,
//   lg: 960px,
//   xl: 1120px
// );

// Pagination
// ---------------------
$pagination-padding-y: .0;
$pagination-padding-x: 0;
$pagination-padding-y-sm: 0;
$pagination-padding-x-sm: 0;
$pagination-padding-y-lg: 0;
$pagination-padding-x-lg: 0;

$pagination-color: $mine-shaft;
$pagination-border-radius: 50%;
$pagination-border-width: 1px;
$pagination-border-color: transparent;

$pagination-focus-color: $galliano;
$pagination-focus-bg: transparent;
$pagination-focus-box-shadow: none;
$pagination-focus-outline: 0;

$pagination-hover-color: $galliano;
$pagination-hover-bg: transparent;
$pagination-hover-border-color: transparent;

$pagination-disabled-color: $mine-shaft;
$pagination-disabled-bg: transparent;
$pagination-disabled-border-color: transparent;

$pagination-active-color: $mine-shaft;
$pagination-active-bg: transparent;
$pagination-active-border-color: $mine-shaft;

$pagination-transition: none;