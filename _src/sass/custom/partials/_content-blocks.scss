// Separator
hr, 
.wp-block-separator {
	background-color: $alto;
	border-bottom: 0;
	border-color: $alto;
	color: $alto;
	opacity: 1;
}

// Card
.card {
	margin-bottom: down(60);

	@media (max-width: 991px) {
		margin-bottom: down(50);
	}

	@media (max-width: 781px) {
		margin-bottom: down(35);
	}

	// Event card used in archive and events block
	&--event {
		align-items: flex-start;
		// margin-bottom: 0;

		.card-title {
			font-size: down(24);
			margin-bottom: 0;
			margin-top: down(2);

			+ .card-link {
				margin-top: down(10);
			}
		}

		.card-meta {
			color: $bermuda-gray-dark;
			font-size: down(18);
			margin-bottom: down(6);
			margin-top: down(2);
		}

		.card-description {
			margin-bottom: em(24);
			
			p:last-child {
				margin-bottom: 0;
			}
		}

		.card-body {
			display: flex;
			flex-direction: column;
		}

		.card-link {
			margin-top: auto;
		}

	}

	// News card used in archive and news block and winners block
	&--news, &--winners {
		.card-title {
			font-size: down(24);
			margin-bottom: down(11);
			margin-top: down(3);
		}

		.card-description {
			margin-bottom: down(17);
		}
	}

	&__img {
		overflow: hidden;
		position: relative;

		&::after {
			border-color: #fff;
			border-style: solid;
			border-width: 0;
			bottom: 0;
			box-sizing: border-box;
			content: "";
			display: block;
			height: 100%;
			left: 0;
			padding: 0;
			position: absolute;
			right: 0;
			top: 0;
			transition: border-width .25s ease;
			width: 100%;
		}

		img {
			object-fit: cover;
			transition: transform .5s ease;
		}

	}

	&:hover {
		.card__img {
			&::after {
				border-width: 5px;
			}

			img {
				transform: scale(1.05);
			}
		}
	}
}

// Image cover columns
.image-covers {
	gap: 15px;
	@media (max-width: 1399px) {
		flex-wrap: wrap !important;
	}

	.wp-block-column {
		overflow: hidden;

		@media (max-width: 1399px) {
			flex-basis: calc( 50% - 8px ) !important;
		}
		@media (max-width: 781px) {
			flex-basis: 100% !important;
		}

		&:hover {
			.wp-block-cover {
				.wp-block-cover__image-background {
					transform: scale(1.06);
				}

				.wp-block-cover__background {
					opacity: 0.35;
				}
			}
		}
	}

	.wp-block-cover {
		align-items: flex-end;
		cursor: pointer;
		height: 600px;
		overflow: hidden;
		padding: down(40, 15) down(36, 15);
		@media (max-width: 1699px) {
			height: 520px;
		}
		@media (max-width: 781px) {
			height: 460px;
			padding: down(26, 15) down(22, 15);
		}
		@media (max-width: 450px) {
			height: 360px;
			min-height: 0;
			padding: down(22, 13) down(18, 15);
		}

		p {
			font-size: down(18, 15);
			@media (max-width: 450px) {
				font-size: down(17, 15);
				line-height: 21px;
			}
		}

		h1, h2, h3, h4, h5, h6 {
			a {
				color: #fff;
				text-decoration: none;
			}
		}

		&__image-background {
			transition: transform .5s ease;
		}

		&__background {
			transition: opacity .5s ease;
		}

	}

}

// Partner logos
.partner-logos {
	justify-content: flex-start;
	@media (max-width: 1199px) {
		justify-content: center;
	}
	@media (max-width: 781px) {
		gap: 20px !important;
	}

	.wp-block-image {
		flex-grow: 0!important;
		@media (max-width: 1199px) {
			width: calc( 100% / 6 - var(--wp--style--unstable-gallery-gap, 16px) * 0.875 ) !important;
		}
		@media (max-width: 991px) {
			width: calc( 100% / 5 - var(--wp--style--unstable-gallery-gap, 16px) * 0.875 ) !important;
		}
		@media (max-width: 781px) {
			width: calc( 100% / 4 - var(--wp--style--unstable-gallery-gap, 16px) * 0.875 ) !important;
		}
		@media (max-width: 599px) {
			width: calc( 100% / 3 - var(--wp--style--unstable-gallery-gap, 16px) * 0.875 ) !important;
		}
	}
}

// Article (entry and single page)
.article {

	font-size: 1.1rem;
	@media (max-width: 1600px) {
		font-size: 1.05rem;
	}

	&__meta {
		align-items: flex-end;
		display: flex;
		justify-content: space-between;
		margin-bottom: down(40);

		@media (max-width: 991px) {
			display: block;
			margin-bottom: down(30);
		}
		@media (max-width: 781px) {
			margin-bottom: down(15);
		}
	}

	&__date {
		font-size: down(16);
	}

	&__img {
		margin-bottom: down(80);

		@media (max-width: 991px) {
			margin-bottom: down(60);
		}
		@media (max-width: 781px) {
			margin-bottom: down(30);
		}
	}

	&__content {
		line-height: 1.6;
		margin-bottom: down(170);

		@media (max-width: 991px) {
			margin-bottom: down(120);
		}
		@media (max-width: 781px) {
			margin-bottom: down(60);
		}
	}

	&__related {
		margin-bottom: down(120);
		
		@media (max-width: 991px) {
			margin-bottom: down(80);
		}
		@media (max-width: 781px) {
			margin-bottom: down(40);
		}

		&__title {
			margin-bottom: down(42, 32);
			margin-top: down(32, 32);

			@media (max-width: 991px) {
				margin-bottom: down(36, 32);
				margin-top: down(24, 32);
			}
			@media (max-width: 781px) {
				margin-bottom: down(26, 32);
				margin-top: down(18, 32);
			}
		}

		&--podcast {
			.row:first-child {
				margin-bottom: down(50);

				@media (max-width: 991px) {
					margin-bottom: down(40);
				}
				@media (max-width: 781px) {
					margin-bottom: down(30);
				}
			}

			.row:nth-child(2) {
				margin-bottom: down(50);

				@media (max-width: 991px) {
					margin-bottom: down(40);
				}
				@media (max-width: 781px) {
					margin-bottom: down(30);
				}
			}
		}
	}

	// Event
	&--event {
		&__meta {
			margin-bottom: down(100);
			margin-top: down(80);

			@media (max-width: 991px) {
				margin-bottom: down(90);
				margin-top: down(60);
			}
			@media (max-width: 781px) {
				margin-bottom: down(60);
				margin-top: down(40);
			}
		}

		&__title {
			line-height: 1.4;
			margin-bottom: down(60, 32);

			@media (max-width: 991px) {
				margin-bottom: down(40, 32);
			}
			@media (max-width: 781px) {
				margin-bottom: down(20, 32);
			}
		}

		&__subtitle {
			margin-bottom: down(40);
			padding-bottom: down(24);

			h2 {
				margin-bottom: 0;
			}
		}

		&__excerpt {
			line-height: 1.6;
			margin-bottom: down(50);

			@media (max-width: 991px) {
				margin-bottom: down(35);
			}
			@media (max-width: 781px) {
				margin-bottom: down(15);
				margin-top: down(25);
			}
		}

		&__table {

			@media (max-width: 781px) {
				margin-top: down(50);
			}

			tr:first-child {
				border-top-width: 1px;
			}

			th {
				padding-left: 0;
			}

			td {
				padding-right: 0;
				text-align: right ;
			}

			th, td {
				padding-bottom: down(22);
				padding-top: down(22);

				@media (max-width: 991px) {
					padding-bottom: down(18);
					padding-top: down(18);
				}
				@media (max-width: 781px) {
					padding-bottom: down(15);
					padding-top: down(15);
				}
			}
		}

		&__packages {
			margin-top: down(135);

			@media (max-width: 991px) {
				margin-top: down(90);
			}
			@media (max-width: 781px) {
				margin-top: down(60);
			}

			> h2 {
				margin-bottom: 0;
			}

			&__item {
				margin-bottom: down(180);

				@media (max-width: 991px) {
					margin-bottom: down(120);
				}
				@media (max-width: 781px) {
					margin-bottom: down(60);
				}
				
				&__inner {
					padding-top: em(40);

					> .row {
						--bs-gutter-x: 30px;

						@media (max-width: 781px) {
							--bs-gutter-y: 20px;
						}
					}
				}

				&__description {
					margin-bottom: down(40);
					margin-top: down(36);

					@media (max-width: 991px) {
						margin-bottom: down(36);
						margin-top: down(26);
					}
					@media (max-width: 781px) {
						margin-bottom: down(32);
						margin-top: down(18);
					}
				}
			}

		}

	}

	// Podcast episode
	&--podcast {
		margin-bottom: down(50);
		padding-bottom: down(50);
		position: relative;

		@media (max-width: 991px) {
			margin-bottom: down(40);
			padding-bottom: down(40);
		}
		@media (max-width: 781px) {
			margin-bottom: down(30);
			padding-bottom: down(30);
		}

		&__description {
			margin-bottom: down(40);
			margin-top: down(30);

			@media (max-width: 991px) {
				margin-bottom: down(35);
				margin-top: down(25);
			}
		}

		&__player {

			--plyr-color-main: #e7b00d;
			--plyr-audio-control-color: #343434;
			--plyr-audio-progress-buffered-background: #ddd;

			border: 1px solid $alto;
			margin-bottom: down(140);
			margin-top: down(200);
			padding: down(16);

			.plyr__controls {
				justify-content: flex-start;
				padding: 5px 0 0 10px;

				@media (max-width: 781px) {
					flex-wrap: wrap;
				}

				.plyr__progress__container {
					min-width: 160px;
				}

				.plyr__menu {
					display: none;
				}
			}

			&__img {
				cursor: pointer;
				flex-basis: 120px;
				flex-shrink: 0;
				overflow: hidden;
				position: relative;
				width: 120px;

				&::before {
					background-image: linear-gradient(180deg, rgba($shark, 0.3) 0%, rgba($shark, 0.3) 100%);
					bottom: 0;
					content: "";
					display: block;
					left: 0;
					opacity: 0;
					position: absolute;
					right: 0;
					top: 0;
					transition: opacity .25s ease;
					z-index: 1;
				}

				> div {
					align-items: center;
					bottom: 0;
					display: flex;
					justify-content: center;
					left: 0;
					opacity: 0;
					position: absolute;
					right: 0;
					top: 0;
					transition: opacity .25s ease;
					user-select: none;
					z-index: 2;

					svg {
						height: 44px;
						width: 44px;

						path {
							fill: $galliano;
						}
					}

					.article--podcast__player__control__pause {
						display: none;
					}

					&.playing {
						.article--podcast__player__control__pause {
							display: block;
						}

						.article--podcast__player__control__play {
							display: none;
						}
					}
				}

				@media (max-width: 991px) {
					flex-basis: 100px;
					width: 100px;
				}
				@media (max-width: 781px) {
					flex-basis: 80px;
					width: 80px;
				}

				img {
					max-width: 100%;
					width: 120px;

					@media (max-width: 781px) {
						width: 80px;
					}

					@media (max-width: 500px) {
						display: none;
					}
				}

				&:hover {
					&::before {
						opacity: 1;
						transition: opacity .25s ease;
					}

					> div {
						opacity: 1;
						transition: opacity .25s ease;
					}
				}
			}

			&__info {
				> div:first-child {
					padding-left: 20px;
					// position: relative;
					// z-index: 1;

					@media (max-width: 500px) {
						margin-top: 8.5px;
						padding-left: 8.5px;
					}
				}
			}

			@media (max-width: 991px) {
				margin-bottom: down(100);
				margin-top: down(160);
			}
			@media (max-width: 781px) {
				margin-bottom: down(60);
				margin-top: down(120);
			}
			@media (max-width: 500px) {
				flex-wrap: wrap;
				margin-bottom: down(40);
				margin-top: down(80);
			}
		}

		&__img {
			margin-bottom: down(20);

			@media (max-width: 991px) {
				margin-bottom: down(15);
			}
			@media (max-width: 781px) {
				margin-bottom: down(10);
			}
		}

		&__links {
			margin-top: down(20);
		}

		.article__content {
			margin-top: down(60);

			@media (max-width: 991px) {
				margin-top: down(40);
			}
			@media (max-width: 781px) {
				margin-top: down(20);
			}
		}

		&__col {
			&:first-child {
				flex-basis: calc( 100% / 3 );
				width: calc( 100% / 3 );

				@media (max-width: 781px) {
					flex-basis: 100%;
					width: 100%;
				}
			}

			&:last-child {
				flex-basis: calc( 100% / 3 * 2 );
				width: calc( 100% / 3 * 2 );

				@media (max-width: 781px) {
					flex-basis: 100%;
					width: 100%;
				}
			}

			&__inner {
				max-width: 100%;
				width: 720px;

				@media (max-width: 1250px) {
					padding-right: 50px;
					width: 100%;
				}

				@media (max-width: 781px) {
					padding-right: 0;
				}
			}

			.card__img {
				overflow: hidden;
				position: relative;

				&::after {
					border-color: #fff;
					border-style: solid;
					border-width: 0;
					bottom: 0;
					box-sizing: border-box;
					content: "";
					display: block;
					height: 100%;
					left: 0;
					padding: 0;
					position: absolute;
					right: 0;
					top: 0;
					transition: border-width .25s ease;
					width: 100%;
				}

				img {
					transition: transform .5s ease;
				}

				&:hover {
					&::after {
						border-width: 5px;
					}

					img {
						transform: scale(1.05);
					}
				}
			}
		}

		&__share-links {
			
			// Only target smartphones and touchscreens
			@media (hover: none) and (pointer: coarse) {
				flex-wrap: nowrap!important;
				margin-bottom: -1em;
				margin-right: calc( var(--bs-gutter-x) * -1 * 1.5 );
				margin-top: -1em;
				-ms-overflow-style: none;
				overflow-x: auto;
				padding-bottom: 1em;
				padding-top: 1em;
				scroll-behavior: smooth;
				scrollbar-width: none;
				white-space: nowrap;

				@media (max-width: 781px) {
					margin-right: calc( var(--bs-gutter-x) * -1 );
				}

				&::-webkit-scrollbar { 
					display: none;
				}
			}

		}

		// Right top arrow (read more)
		.link-styled {
			position: absolute;
			right: calc( var(--bs-gutter-x) * .5 );
			top: 0;

			@media (max-width: 781px) {
				display: none;
			}
		}

	}

	// Components for single podcast page
	&--podcast--single {
		margin-top: down(80);

		@media (max-width: 991px) {
			margin-top: down(50);
		}
		@media (max-width: 781px) {
			flex-wrap: wrap;
			margin-top: down(20);
		}

		&__col {
			flex-basis: 720px;
			width: 720px;

			@media (max-width: 781px) {
				flex-basis: 100%;
				width: 100%;
			}

			&:first-child {
				max-width: 50%;

				@media (max-width: 991px) {
					max-width: 55%;
				}
				@media (max-width: 781px) {
					max-width: 100%;
					order: 2;
				}
			}

			&:last-child {
				margin-left: auto;
				max-width: 45%;

				@media (max-width: 991px) {
					max-width: 40%;
				}
				@media (max-width: 781px) {
					margin-bottom: down(25);
					max-width: 100%;
					order: 1;
				}
			}
		}

	}

	// Proffsstränare
	&--trainer {
		margin-bottom: em(200);

		@media (max-width: 991px) {
			margin-bottom: em(160);
		}
		@media (max-width: 781px) {
			margin-bottom: em(100);
		}

		&__img {
			padding-right: 40px;

			img {
				width: 439px;
			}
		}

		.article__title {
			margin-bottom: down(18);
		}

		&__meta {
			margin-bottom: down(34);
			margin-top: down(30);

			div {
				margin-bottom: down(10);
			}
		}

		.article__body {
			margin-bottom: down(40);
		}
	}

	// Result Photos
	&--result-photos {
		.article__title {
			margin-bottom: 12px;
		}
	}

}

.single-article-details {
	display: flex;
	flex-direction: column;
	max-height: calc(100vh - 100px - em(120));
	overflow: hidden;
	width: auto;

	@media (max-width: 2000px) {
		max-height: calc(100vh - 100px + 30px - em(100));
	}
	@media (max-width: 1600px) {
		max-height: calc(100vh - 100px + 30px - em(80));
	}
	@media (max-width: 991px) {
		max-height: calc(100vh - 80px + 30px - em(40));
	}
	@media (max-width: 781px) {
		max-height: calc(100vh - 80px + 30px - em(40));
	}

	.article__img {
		display: flex;
		flex: 1;
		flex-direction: column;
		min-height: 0; // Allow flex item to shrink below content size

		img {
			margin: auto;
			max-height: 100%;
			max-width: 100%;
			width: auto!important;
		}

		// Caption styling to ensure it doesn't interfere with image sizing
		.d-block.text-center.mt-2 {
			flex-shrink: 0;
			margin-top: 0.5rem;
		}
	}

	// Ensure breadcrumbs and meta don't take up too much space
	nav[aria-label="breadcrumb"],
	.article__meta {
		flex-shrink: 0;
	}
}

// Page
.page {
	&__title {
		margin-bottom: 0;
		margin-top: em(120);

		@media (max-width: 2000px) {
			margin-bottom: 0;
			margin-top: em(100);
		}
		@media (max-width: 1600px) {
			margin-bottom: 0;
			margin-top: em(80);
		}
		@media (max-width: 1199px) {
			margin-bottom: 0;
			margin-top: em(80);
		}
		@media (max-width: 991px) {
			margin-bottom: 0;
			margin-top: em(40);
		}

		&--archive {
			margin-bottom: em(42);
			margin-top: down(120);

			@media (max-width: 2000px) {
				margin-top: em(100);
			}
			@media (max-width: 1600px) {
				margin-top: em(80);
			}
			@media (max-width: 1199px) {
				margin-bottom: 0;
				margin-top: em(80);
			}
			@media (max-width: 991px) {
				margin-bottom: em(30);
				margin-top: down(40);
			}
			@media (max-width: 781px) {
				margin-bottom: em(30);
				margin-top: down(40);
			}
		}
	}

	&__img {
		margin-bottom: down(80);
		margin-top: 1rem;

		@media (max-width: 991px) {
			margin-bottom: down(60);
		}
		@media (max-width: 781px) {
			margin-bottom: down(30);
		}
	}
}