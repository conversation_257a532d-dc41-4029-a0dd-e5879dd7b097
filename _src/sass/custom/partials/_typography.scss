html {
	font-size: $browser-context; // 16px
	@media (max-width: 991px) {
		font-size: 0.9375rem;
	}
}

body {
	color: $cod-grey;
	font-family: $helvetica;
	font-weight: 500;
	letter-spacing: -0.02em;
}

h1,h2,h3,h4,h5,h6 {
	font-family: $poppins;
	font-weight: 700;
	letter-spacing: -0.04em;
}

.display-1 {
	@media (max-width: 1600px) {
		font-size: 5.8rem;
	}
	@media (max-width: 1400px) {
		font-size: 5rem;
	}
	@media (max-width: 1200px) {
		font-size: 4rem;
	}
	@media (max-width: 781px) {
		font-size: 3.4rem;
	}
	@media (max-width: 680px) {
		font-size: 3rem;
	}
	@media (max-width: 500px) {
		font-size: em(32);
	}
}

.ls {
	&--normal {
		letter-spacing: -0.02em!important;
	}

	&--medium {
		letter-spacing: -0.02em!important;
	}
}
 
.fw-medium {
	font-weight: 500!important;
}

.lh-11 {
	line-height: 1.1;
}

.has-medium-font-size {
	@media (max-width: 991px) {
		font-size: 18px !important;
	}
	@media (max-width: 781px) {
		font-size: 16px !important;
	}
}

.content-block {
	a {
		color: $cod-grey;
	}
}

.text-chathams-blue {
	color: $chathams-blue;

	a, span {
		color: $chathams-blue;
	}

	&:hover {
		color: $chathams-blue;

		a, span {
			color: $chathams-blue;
		}
	}
}


.text-razzmatazz {
	color: $razzmatazz;

	a, span {
		color: $razzmatazz;
	}

	&:hover {
		color: $razzmatazz;

		a, span {
			color: $razzmatazz;
		}
	}
}