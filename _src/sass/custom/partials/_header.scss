// Main navigation side padding
.navbar-expand-lg .navbar-nav .nav-link {
	color: $cod-grey;
	padding-bottom: 38px;
	padding-top: 38px;
	position: relative;
	text-transform: uppercase;
	user-select: none;

	@media (max-width: 1199px) {
		padding-bottom: 28px;
		padding-top: 28px;
	}

	@media (max-width: 500px) {
		padding-bottom: 0;
		padding-top: 0;
	}

	@media (min-width: 992px) {
		font-size: 15px;
		padding-left: down(15);
		padding-right: down(15);

		&::after {
			border-bottom: 1px solid currentColor;
			border-left: 0;
			border-right: 0;
			border-top: 0;
			bottom: 38px;
			content: "";
			display: block;
			left: down(21);
			margin: 0;
			position: absolute;
			right: down(21);
			transition: width .3s ease;
			width: 0;

			@media (max-width: 1199px) {
				bottom: 28px;
			}
		}

		&:hover::after {
			width: calc(100% - down(21) * 2);
		}
	}

	@media (min-width: 1070px) {
		font-size: var(--bs-nav-link-font-size);
		padding-left: down(21);
		padding-right: down(21);
	}

}

.navbar-expand-lg .navbar-nav .nav-item-lang .nav-link {
	align-items: center;
	display: flex;

	img {
		margin-right: 6px;
	}
}

// Desktop navigation
#navbarNavDropdown {
	.mega-menu__item {
		color: $cod-grey;
		text-decoration: none;

		&:hover {
			background: none;
		}
	}

	.nav-link {
		&::before {
			content: "";
		}
	}

	.mega-menu__parent {
		&::after {
			content: "";
		}

		// Mega menu container
		.mega-menu {
			background: none;
			border: 0;
			border-radius: 0;
			display: flex;
			left: 0;
			list-style-type: none;
			margin-left: auto;
			margin-right: auto;
			max-width: 100%;
			min-height: 340px;
			opacity: 0;
			padding: 0;
			position: absolute;
			right: 0;
			top: calc(100% + 1px);
			// transition: opacity .3s ease;
			visibility: hidden;
			width: 1820px;

			@media (max-width: 1199px) {
				min-height: 260px;
			}

			&::before {
				background-color: #fff;
				border-bottom: 1px solid $alto;
				bottom: -1px;
				content: "";
				display: block;
				left: -200%;
				position: absolute;
				right: -200%;
				top: 0;
				width: 500%;
				z-index: -1;
			}

			>&__col {
				border-right: 1px solid $alto;
				padding: down(52) calc(var(--bs-gutter-x) * 2);

				@media (max-width: 1199px) {
					padding: down(36) calc(var(--bs-gutter-x) * 2);
				}

				&:last-child {
					border-right: 0;
				}

				&--3 {
					width: 25%;
				}

				&--6 {
					width: 50%;
				}

				&--9 {
					width: 75%;
				}

				&--12 {
					width: 100%;
				}

				&--6,
				&--9,
				&--12 {
					>.mega-menu__item:first-child {
						&::after {
							content: none;
						}
					}
				}

				&--6 {
					.mega-menu__child-menu {
						column-count: 2;
						column-gap: calc(var(--bs-gutter-x) * 4);
					}
				}

				&--9 {
					.mega-menu__child-menu {
						column-count: 3;
						column-gap: calc(var(--bs-gutter-x) * 4);
					}
				}

				&--12 {
					.mega-menu__child-menu {
						column-count: 4;
						column-gap: calc(var(--bs-gutter-x) * 4);
					}
				}

				// Mega menu level 1 item (level 2 WP menu item)
				>.mega-menu__item {
					align-items: center;
					display: flex;
					font-family: $poppins;
					font-size: down(24);
					font-weight: 700;
					letter-spacing: -0.03em;
					margin-bottom: down(14);
					margin-top: down(15);
					text-transform: uppercase;

					&::after {
						background-color: currentColor;
						content: "";
						display: inline-block;
						height: 9px;
						margin-left: auto;
						mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' class='bi bi-arrow-right' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z'/%3E%3C/svg%3E") no-repeat 50% 50%;
						mask-size: cover;
						position: relative;
						width: 17.5px;
					}
				}

				>.mega-menu__item:first-child {
					margin-top: 0;
				}

				&--elitloppet {
					>.mega-menu__item {
						&::after {
							background-color: $razzmatazz;
							height: 13px;
							margin-left: down(5);
							mask: url("data:image/svg+xml,%3Csvg width='13' height='13' viewBox='0 0 14 14' fill='currentColor' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.00065 0.333344C3.31865 0.333344 0.333984 3.31801 0.333984 7.00001C0.333984 10.682 3.31865 13.6667 7.00065 13.6667C10.6827 13.6667 13.6673 10.682 13.6673 7.00001C13.6673 3.31801 10.6827 0.333344 7.00065 0.333344ZM3.20524 6.19142C3.12857 6.00475 3.17063 5.78932 3.31396 5.64666L5.31396 3.64666C5.5093 3.45132 5.82599 3.45132 6.02132 3.64666C6.21666 3.84199 6.21666 4.15868 6.02132 4.35401L4.87467 5.50066H9.00065C9.27665 5.50066 9.50065 5.72466 9.50065 6.00066C9.50065 6.27666 9.27665 6.50066 9.00065 6.50066H3.66732C3.46465 6.49999 3.28257 6.37808 3.20524 6.19142ZM10.6873 8.35336L8.68734 10.3534C8.59 10.4507 8.46198 10.5 8.33398 10.5C8.20598 10.5 8.07797 10.4514 7.98063 10.3534C7.7853 10.158 7.7853 9.84134 7.98063 9.64601L9.12728 8.49936H5.00065C4.72465 8.49936 4.50065 8.27536 4.50065 7.99936C4.50065 7.72336 4.72465 7.49936 5.00065 7.49936H10.334C10.5367 7.49936 10.7187 7.62129 10.7961 7.80795C10.8734 7.99529 10.8307 8.2107 10.6873 8.35336Z' fill='%23D0006F'/%3E%3C/svg%3E");
							width: 13px;
						}

						span {
							color: $razzmatazz;
						}
					}
				}

			}

			// All links in mega menu
			&__item {
				span {
					position: relative;

					&::after {
						border-bottom: 1px solid currentColor;
						bottom: 1px;
						content: "";
						display: block;
						left: 0;
						position: absolute;
						right: 0;
						transition: width .3s ease;
						width: 0;
					}

				}

				&:hover {
					span {
						&::after {
							width: 100%;
						}
					}
				}

			}

			// Description under mega menu top level item
			&__desc {
				color: $cod-grey;
				display: block;
				font-size: 16px;
				letter-spacing: -0.01em;
				line-height: 24px;
				max-width: 100%;
				width: 280px;
			}

			// Mega menu child menu
			&__child-menu {
				list-style-type: none;
				margin-top: down(15);
				padding-left: 0;

				// Mega menu level 2 item (level 3 WP menu item)
				.mega-menu__item {
					display: block;
					padding: down(9) 0;
				}
			}

		}

		&.show {
			&::after {
				bottom: -1px;
				display: block;
				height: 38px;
				left: 0;
				position: absolute;
				right: 0;
				z-index: 1;

				@media (max-width: 1199px) {
					height: 28px;
				}
			}

			// Add some tolerance on sides for hovering over first and last top level mega menu items

			&:first-child {
				>.nav-link {
					&::before {
						@media (min-width: 1120px) {
							bottom: -1px;
							display: block;
							height: 100%;
							left: calc(var(--bs-gutter-x) * 2 * -1);
							position: absolute;
							top: 0;
							width: calc(var(--bs-gutter-x) * 2);
							z-index: 1;
						}
					}
				}
			}

			&:last-child {
				>.nav-link {
					&::before {
						@media (min-width: 1120px) {
							bottom: -1px;
							display: block;
							height: 100%;
							position: absolute;
							right: calc(var(--bs-gutter-x) * 2 * -1);
							top: 0;
							width: calc(var(--bs-gutter-x) * 2);
							z-index: 1;
						}
					}
				}
			}

			>.nav-link {
				&::after {
					width: calc(100% - down(21) * 2);
				}
			}

			.mega-menu {
				opacity: 1;
				visibility: visible;
			}
		}

	}
}

// Right navigation
#navbarNavDropdownRight {
	flex-grow: 0;
	margin-right: down(-21);
	position: relative;

	.navbar-nav .nav-link {
		color: $chathams-blue;
	}
}

// Mobile navigation
#navbarNavDropdownMobile {
	background-color: #fff;
	bottom: 0;
	height: calc(100% - 51.25px);
	left: 0;
	max-height: 100%;
	opacity: 0;
	overflow-y: scroll;
	padding-bottom: 180px;
	padding-left: 15px;
	padding-right: 15px;
	position: fixed;
	right: 0;
	top: 51.25px;
	transition: none;
	transition: opacity .3s ease;
	z-index: 999;

	@media (min-width: 992px) {
		display: none !important;
	}

	.nav-link {
		border-bottom: 1px solid #ddd;
		color: $cod-grey !important;
		padding: 17px 0;

		&::after {
			color: $cod-grey !important;
		}

		&:focus,
		&:focus-visible {
			outline: none;
		}

	}

	.nav-item {
		&:last-child {
			.nav-link {
				color: $chathams-blue !important;
				padding: 26px 0;

				&::after {
					color: $chathams-blue !important;
				}
			}
		}
	}

	&.collapse {
		opacity: 1;
	}

	&.collapsing {
		transition: none;
	}

	.dropdown-menu {
		border: 0;
		border-radius: 0;
		padding: 14px 0 24px;

		.dropdown-item {
			padding: 8px 0;

			&:hover {
				background: none;
			}
		}
	}

	.dropdown-link {
		color: $cod-grey;
		display: block;
		left: 0;
		position: absolute;
		text-decoration: none;
		text-transform: uppercase;
		top: 17.5px;
		width: calc(100% - 40px);
		z-index: 1;
	}

	.dropdown-toggle {
		align-items: center;
		display: flex;
		height: 57.5px;
		justify-content: flex-end;
		width: 100%;

		span {
			align-items: center;
			display: flex;
			justify-content: flex-end;
			width: 40px;
		}

		svg {
			margin-right: 10px;
		}

		&::after {
			content: none;
		}

		&[aria-expanded=true] {
			border-color: transparent;

			svg {
				transform: scale(-1, -1);
			}
		}
	}

	// Second menu level
	>.navbar-nav {
		>li {
			>.dropdown-menu {
				>li {
					>a {
						font-family: $poppins;
						font-size: down(24);
						font-weight: 700;
						letter-spacing: -0.03em;
						text-transform: uppercase;

					}

					// Third menu level
					.dropdown-item {
						align-items: center;
						display: flex;
						position: relative;

						&::after {
							background-color: #9D9D9D;
							bottom: 1px;
							content: "";
							display: inline-block;
							height: 9px;
							margin-left: down(8.25);
							mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' class='bi bi-arrow-right' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z'/%3E%3C/svg%3E") no-repeat 50% 50%;
							mask-size: cover;
							position: relative;
							width: 17.5px;
						}
					}
				}
			}
		}
	}

	.mobile-menu {
		&__desc {
			display: block;
			letter-spacing: -0.03em;
			line-height: 1.5;
			max-width: 100%;
			padding: 8px 0;
			padding: 0 0 24px;
			width: 250px;
		}

		&__child-menu {
			list-style-type: none;
			margin-top: -6px;
			padding: 0 0 24px;
			padding-left: 0;

			a {
				color: $cod-grey;
				display: block;
				letter-spacing: -0.03em;
				padding: 8px 0;
				text-decoration: none;
				text-transform: none;
			}
		}

	}

}

.admin-bar {
	@media (max-width: 600px) {
		position: relative;
		top: -46px;
	}

	#navbarNavDropdownMobile {
		height: calc(100% - 51.25px - 32px);
		top: calc(51.25px + 32px);

		@media (max-width: 782px) {
			height: calc(100% - 51.25px - 46px);
			top: calc(51.25px + 46px);
		}

		@media (max-width: 600px) {
			height: calc(100% - 51.25px);
			top: 51.25px;
		}
	}

	#wpadminbar {
		@media (max-width: 600px) {
			display: none !important;
		}
	}

	.site-header {
		top: 32px;

		@media (max-width: 782px) {
			top: 46px;
		}

		@media (max-width: 600px) {
			top: 0;
		}
	}

}

// Toggler edit to match design
.site-header .navbar-toggler {
	align-items: center;
	border: 0;
	border-radius: 0;
	color: $cod-grey;
	font-size: em(16);
	height: 51.25px;
	padding-left: 15px;
	padding-right: 15px;
	position: relative;
	position: absolute;
	right: 0;
	top: 0;

	@media (min-width: 992px) {
		display: none;
	}

	&:focus {
		box-shadow: none;
		outline: 0;
	}

	span {
		@media (max-width: 991px) {
			display: none;
		}
	}

	svg {
		&:nth-child(1) {
			margin-right: em(7);

			@media (max-width: 991px) {
				display: none;
			}
		}

		&:nth-child(2) {
			display: none;
			stroke: #000;
			transition: 0.2s;

			g {
				&:first-child {
					opacity: 1;
					transition: opacity 0s 0.2s;

					line {
						transform: translateY(0);
						transition: transform 0.2s 0.2s;
					}
				}

				&:last-child {
					opacity: 0;
					transition: opacity 0s 0.2s;

					line {
						transform: rotate(0deg);
						transform-origin: center;
						transition: transform 0.2s;
					}
				}
			}

			@media (max-width: 991px) {
				display: block;
			}
		}
	}

	// Expanded state
	&[aria-expanded=true] {
		svg {
			stroke: #fff;

			g {
				&:first-child {
					opacity: 0;

					line {
						transition: transform 0.2s;

						&:first-child {
							transform: translateY(7px);
						}

						&:last-child {
							transform: translateY(-7px);
						}
					}
				}

				&:last-child {
					opacity: 1;

					line {
						transition: transform 0.2s 0.2s;

						&:first-child {
							transform: rotate(45deg);
						}

						&:last-child {
							transform: rotate(-45deg);
						}
					}
				}
			}
		}
	}
}

// Header
.site-header {
	background-color: #fff;
	border-bottom: 1px solid $alto;
	left: 0;
	opacity: 1;
	position: fixed;
	right: 0;
	top: 0;
	transition: all .3s ease;
	visibility: visible;
	z-index: 999;

	.navbar {
		padding: 0;
		position: static;

		@media (max-width: 991px) {
			padding: 0 !important;
		}

		// Logo
		&-brand {
			&:first-child {
				margin-right: 9px;
			}

			svg {
				width: 68px;
			}

			&--alt {

				background: $razzmatazz;
				border-radius: 5px;
				line-height: 1;
				// margin-left: 9px;
				margin-right: 0;
				// position: relative;
				// top: -1px;
				padding: 6px 12px;

				svg {
					width: 84px;
				}
			}

			&__separator {
				margin-right: 9px;
				position: relative;
				top: -1px;
			}
		}
	}

	// when scrolled up
	&.hide {
		opacity: 0;
		visibility: hidden;
	}

	// Main wrapper top padding
	+main {
		padding-top: 100px;

		@media (max-width: 991px) {
			padding-top: 80px;
		}

		@media (max-width: 500px) {
			padding-top: 50px;
		}
	}
}

// Remove top padding from main content for some pages
// Also make header text white
.home,
.single-event,
.has-custom-slider {
	main {
		padding-top: 0;
	}

	.site-header {
		background: none;
		border-bottom: 1px solid transparent;

		.nav-link,
		.navbar-toggler {
			color: #fff !important;
		}

		.navbar-expand-lg .navbar-toggler {
			svg {
				&:nth-child(2) {
					stroke: #fff;
				}
			}
		}

		.navbar-brand {
			&--alt {
				svg path {
					fill: #fff;
				}
			}

			&__separator {
				svg path {
					fill: #D0006F;
				}
			}
		}

	}

	&.menu-open,
	&.scroll {
		.site-header {
			background-color: #fff;
			border-bottom: 1px solid $alto;

			.nav-link,
			.navbar-toggler {
				color: $cod-grey !important;
			}

			.navbar-expand-lg .navbar-toggler {
				svg {
					&:nth-child(2) {
						stroke: #000;
					}
				}
			}

			// .navbar-brand {
			// 	&--alt {
			// 		svg path {
			// 			fill: #D0006F;
			// 		}
			// 	}

			// 	&__separator {
			// 		svg path {
			// 			fill: #D0006F;
			// 		}
			// 	}
			// }

			&.hide {
				background: none;
				border-bottom: 1px solid $alto;

				.nav-link,
				.navbar-toggler {
					color: #fff !important;
				}

				.navbar-expand-lg .navbar-toggler {
					svg {
						&:nth-child(2) {
							stroke: #fff;
						}
					}
				}

			}
		}
	}

}

// Mobile menu open state
.mobile-menu-open {

	// Disable scroll on body when mobile menu is opened
	overflow: hidden;

	.site-header,
	.site-header.hide {
		background: #fff !important;
		opacity: 1;
		visibility: visible;

		.navbar-toggler {
			color: $cod-grey !important;
		}

		.navbar-expand-lg .navbar-toggler {
			svg {
				&:nth-child(2) {
					stroke: $cod-grey !important;
				}
			}
		}
	}
}

// Top image background used on some pages
.top-image {
	--small_text_font_size: var(--small_text_font_size, 1);
	--heading_font_size: var(--heading_font_size, 1);
	max-height: clamp(0px, 1080px, 100vh);
	max-height: clamp(0px, 1080px, calc(var(--vh, 1vh) * 100));
	overflow: hidden;
	position: relative;
	transition: height .3s ease;

	&::before {
		background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0) 54%, #000 100%);
		bottom: 0;
		content: '';
		left: 0;
		position: absolute;
		right: 0;
		top: 0;
		z-index: 1;

		@media (max-width: 781px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0.1) 40%, #000 100%);
		}

		@media (max-width: 680px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.16) 32%, rgba(0, 0, 0, 0.06) 54%, #000 100%);
		}
	}

	img {
		width: 100%;
	}

	&__content {
		bottom: em(60);
		color: #fff;
		left: 0;
		position: absolute;
		right: 0;
		z-index: 2;

		h1 {
			@media (min-width: 1600px) {
				font-size: clamp(1em, calc(1.35rem + 3.09vw), 100px);
				margin-bottom: down(4);
			}
		}

		@media (max-width: 991px) {
			bottom: em(50);
		}

		@media (max-width: 400px) {
			bottom: em(40);
		}

		// &__inner {
		// 	max-width: 880px;
		// 	width: 50%;
		// 	@media (max-width: 1400px) {
		// 		width: 65%;
		// 	}
		// 	@media (max-width: 991px) {
		// 		width: 70%;
		// 	}
		// 	@media (max-width: 781px) {
		// 		width: 85%;
		// 	}
		// 	@media (max-width: 680px) {
		// 		width: 100%;
		// 	}
		// }

		&__meta {
			flex-wrap: nowrap;
			margin-bottom: down(42);
			margin-left: 0;
			margin-right: 0;
			margin-top: down(20);
			max-width: 100%;
			width: 620px;

			@media (max-width: 1199px) {
				margin-bottom: down(35);
				margin-top: down(20);
			}

			@media (max-width: 400px) {
				margin-bottom: down(30);
				margin-top: down(20);
			}

			@media (max-width: 680px) {
				flex-wrap: wrap;
			}

			&--half {
				max-width: 880px;
				width: 60%;

				@media (max-width: 1400px) {
					width: 65%;
				}

				@media (max-width: 991px) {
					width: 75%;
				}

				@media (max-width: 781px) {
					width: 85%;
				}

				@media (max-width: 680px) {
					max-width: 100%;
					width: 320px;
				}
			}

			.col {
				border-right: 1px solid rgba(255, 255, 255, 0.4);
				padding-left: down(48);
				padding-right: down(48);

				@media (max-width: 1400px) {
					padding-left: down(30);
					padding-right: down(30);
				}

				@media (max-width: 680px) {
					flex-basis: 50%;
					padding-left: down(25);
					padding-right: down(25);
					width: 50%;
				}

				@media (max-width: 450px) {
					padding-left: down(15);
					padding-right: down(15);
				}

				@media (max-width: 380px) {
					padding-left: down(25);
				}

				&:first-child {
					padding-left: 0;

					@media (max-width: 680px) {
						margin-bottom: down(20);
					}
				}

				&:nth-child(2) {
					@media (max-width: 680px) {
						border-right: 0;
						margin-bottom: down(20);
						padding-right: 0;
					}
				}

				&:nth-child(3) {
					@media (max-width: 680px) {
						padding-left: 0;
					}
				}

				&:last-child {
					border-right: 0;
					padding-right: 0;

					// @media (max-width: 380px) {
					// 	flex-basis: 100%;
					// 	margin-top: down(20);
					// 	padding-left: 0;
					// 	width: 100%;
					// }
				}

				div {
					color: $alto;
					white-space: nowrap;
				}

				>div:first-child {
					color: #fff;
					font-weight: 700;
				}
			}
		}
	}

	&__mobile {
		display: none;

		@media (max-width: 450px) {
			display: block;
		}
	}

	&__mobile_large {
		display: none;

		@media (min-width: 451px) and (max-width: 680px) {
			display: block;
		}
	}

	&__tablet {
		display: none;

		@media (min-width: 681px) and (max-width: 991px) {
			display: block;
		}
	}

	&__desktop {
		@media (max-width: 991px) {
			display: none;
		}
	}
}