.countdown {
	@media (max-width: 991px) {
		display: none;
	}
}

#countdown {
	display: block;
	margin: 0 auto;
	text-align: center;
	text-decoration: none;
	user-select: none;

	ul {
		display: flex;
		justify-content: center;
		list-style-type: none;
		margin-bottom: 0;
		padding-left: 0;
	}

	li {
		align-items: center;
		color: $cod-grey;
		display: flex;
		flex-direction: column;
		font-size: 13px;
		min-width: 64px;
		padding: 0 5px;
		
		@media (max-width: 1199px) {
			min-width: 0;
		}
	}

	span {
		background-color: #fcedf4;
		border-radius: 5px;
		color: $razzmatazz;
		display: block;
		font-size: 16px;
		margin-bottom: 1px;
		min-width: 40px;
		padding: 0 3px;

		@media (max-width: 1199px) {
			font-size: 14px;
			padding: 0 2px;
		}
	}
}

.mobile-menu-open #countdown {
	display: none;
}

#countdown-ended {
	color: #fff;
	text-align: center;
}

.home .site-header, 
.single-event .site-header,
.has-custom-slider .site-header {
	#countdown {
		li {
			color: #fff;
		}

		span {
			background-color: rgba(0,0,0,0.6);
			color: #fff;
		}
	}
}

.home.menu-open .site-header, 
.home.scroll .site-header, 
.single-event.menu-open .site-header, 
.single-event.scroll .site-header,
.has-custom-slider.menu-open .site-header, 
.has-custom-slider.scroll .site-header {
	#countdown {
		li {
			color: $cod-grey;
		}

		span {
			background-color: #fcedf4;
			color: $razzmatazz;
		}
	}
}