body.elitloppet {

	// Header
	#navbarNavDropdownRight {
		.navbar-nav .nav-link {
			color: $razzmatazz;
		}
	}

	#navbarNavDropdownMobile {
		.nav-item {
			&:last-child {
				.nav-link {
					color: $razzmatazz!important;

					&::after {
						color: $razzmatazz!important;
					}
				}
			}
		}
	}

	// Footer logo
	.site-footer__col:first-child {
		svg {
			height: 38px;
		}
	}

	// Buttons
	.btn-white {
		&::before {
			background-color: $razzmatazz;
		}
	}

	.btn-custom {
		&::before {
			background-color: $razzmatazz;
		}
	}

	.btn-secondary {
		background-color: $razzmatazz;
		border-color: $razzmatazz;

		&:hover, &:focus, &:active {
			color: $razzmatazz !important;

			a {
				color: $razzmatazz !important;
			}
		}

		&.btn--dot {
			&:hover, &:focus, &:active {
				border-color: $razzmatazz !important;
			}
		}
	}

	.btn--dot.btn-secondary {
		&::before {
			background-color: #ec99c5;
		}
	}

	.page-link:hover,
	.pagination.pagination-lg .page-item.active a:hover {
		color: $razzmatazz;
	}

}

body.elitloppet #navbarNavDropdownMobile .nav-item.nav-item-lang:last-child .nav-link {
	color: $cod-grey !important;
}