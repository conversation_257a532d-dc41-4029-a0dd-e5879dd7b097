.site-footer {
	h6 {
		font-family: $helvetica;
		font-size: down(18);
		font-weight: 500;
	}
    
	p, li {
		font-size: down(18);
	}

	&__row {
		justify-content: flex-start;

		// Footer row with menus and address
		&:first-child {
			margin-bottom: down(130);
			padding-top: down(90);

			@media (max-width: 991px) {
				margin-bottom: down(90);
				padding-top: down(80);
			}

			@media (max-width: 781px) {
				margin-bottom: down(60);
				padding-top: down(50);
			}
		}

		// Copyright row
		&:nth-child(2) {
			padding-bottom: down(28);
			padding-top: down(28);
		}
	}

	&__text {
		svg {
			margin-bottom: down(30);

			+ div {
				margin-top: 0;
			}
		}

		h6 {
			align-items: center;
			display: flex;
			height: 40.88px;
			margin-bottom: down(20);

			@media (max-width: 781px) {
				height: auto;
				margin-bottom: 0;
			}
		}

		> div {
			margin-top: down(30);
			@media (max-width: 781px) {
				margin-top: down(20);
			}
		}
	}

	&__menu {

		@media (max-width: 781px) {
			margin-bottom: down(40);
		}
		@media (max-width: 600px) {
			margin-bottom: down(20);
		}

		h6 {
			align-items: center;
			display: flex;
			height: 40.88px;
			margin-bottom: down(24);

			@media (max-width: 781px) {
				height: auto;
				margin-bottom: down(12);
			}
		}

		li
		li.link-styled {
			line-height: 1.5;
			padding: down(6) 0;

			a {
				margin: 0;
				padding: 0;
			}
		}

	}

	&__address {
		margin-bottom: down(60);
		margin-top: down(12);

		@media (max-width: 781px) {
			margin-bottom: down(40);
		}
	}

	&__col {
		flex-basis: 16%;
		flex-grow: 0;
		width: 16%;

		@media (max-width: 1200px) {
			flex-basis: 30%;
			width: 30%;
		}

		@media (max-width: 781px) {
			flex-basis: 100%!important;
			margin-bottom: 0;
			margin-top: down(30)!important;
			width: 100%!important;
		}

		&:first-child {
			flex-basis: 26%;
			width: 26%;

			@media (max-width: 1200px) {
				flex-basis: 40%;
				width: 40%;
			}

			div {
				width: 65%;
				
				@media (max-width: 1400px) {
					width: 80%;
				}

				@media (max-width: 781px) {
					max-width: 100%;
					width: 290px;
				}
			}
		}

		&:nth-child(4) {
			flex-basis: 26%;
			width: 26%;

			@media (max-width: 1200px) {
				flex-basis: 40%;
				margin-top: down(60);
				width: 40%;
			}

			div {
				width: 65%;
				
				@media (max-width: 1400px) {
					width: 80%;
				}

				@media (max-width: 781px) {
					max-width: 100%;
					width: 290px;
				}
			}
		}

		&:nth-child(5) {
			@media (max-width: 1200px) {
				margin-top: down(60);
			}
		}
	}

}

.cookies-settings-btn {
	align-items: center;
	background: #fff;
	border: 1px solid #ddd;
	border-bottom: 0;
	bottom: 0;
	cursor: pointer;
	display: none;
	display: flex;
	font-size: .95em;
	justify-content: center;
	margin: 0;
	padding: 5px 12px;
	position: fixed;
	right: 30px;
	transition: all 0.3s;
	z-index: 10;

	&:hover {
		background: #f7f7f7;
	}

	&:focus,
	&:visited {
		outline: none;
	}

	@media (max-width: 991px) {
		padding: 4px 8px;
		right: 22px;
	}

	@media (max-width: 600px) {
		border-right: 0;
		right: 0;
	}
	
	svg {
		display: none;
		
		@media (max-width: 600px) {
			display: block;
		}
	}

	span {
		@media (max-width: 600px) {
			display: none;
		}
	}
}

html.show--consent {
	.cookies-settings-btn {
		display: none;
	}
}