/* Helper Functions & Mixins */

// Calculate rem
// -----------------------------
@function rem($pixels, $context: $browser-context) {
	@if (unitless($pixels)) {
		$pixels: calc( $pixels * 1px );
	}

	@if (unitless($context)) {
		$context: calc( $context * 1px );
	}

	@return calc( $pixels / $context * 1rem );
}

// Calculate em
// -----------------------------
@function em($pixels, $context: $browser-context) {
	@if (unitless($pixels)) {
		$pixels: calc( $pixels * 1px );
	}

	@if (unitless($context)) {
		$context: calc( $context * 1px );
	}

	@return calc( $pixels / $context * 1em );
}

// Calculate line-height
// -----------------------------
@function lh($line-height, $font-size: $browser-context) {
	@if (unitless($line-height)) {
		$line-height: calc( $line-height * 1px );
	}

	@if (unitless($font-size)) {
		$font-size: calc( $font-size * 1px );
	}

	@return calc( $line-height / $font-size );
}

// Downscale to 85%
// -----------------------------
@function down($pixels, $context: $browser-context) {
	@if (unitless($pixels)) {
		$pixels: calc( $pixels * 1px * 0.85 );
	}

	@if (unitless($context)) {
		$context: calc( $context * 1px );
	}

	@return calc( $pixels / $context * 1em );
}