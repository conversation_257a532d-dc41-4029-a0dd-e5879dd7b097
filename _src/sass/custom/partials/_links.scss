// Links with icons
.link-styled {
	user-select: none;

	&--padding {
		padding: calc( 0.4em + 1px ) 0;
	}

	&--padding-large {
		padding: calc( 0.6em + 1px ) 0;
	}

	> a, 
	> span {
		border-radius: 0;
		font-weight: 500;
		margin-bottom: .5rem;
		margin-top: .5rem;
		padding-bottom: 0;
		padding-left: 0;
		padding-right: 0;
		padding-top: 0;
		text-decoration: none;
		
		@media (max-width: 781px) {
			padding: 0;
		}
	}

	// Dot before the text
	&--dot {
		> a, 
		> span {
			&::before {
				background-color: currentcolor;
				bottom: 1px;
				content: "";
				display: inline-block;
				height: 10px;
				margin-right: em(7);
				mask: url("data:image/svg+xml,%3Csvg width='10' height='10' viewBox='0 0 10 10' fill='currentColor' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='5' cy='5' r='4.5' fill='currentColor'/%3E%3C/svg%3E") no-repeat 50% 50%;
				mask-size: cover;
				position: relative;
				width: 10px;
			}
		}
	}

	// Right arrow after the text
	&--arrow {
		> a, 
		> span {
			&::before {
				content: none;
			}
            
			&::after {
				background-color: currentcolor;
				content: "";
				display: inline-block;
				height: 14px;
				margin-left: em(7);
				mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' class='bi bi-arrow-right' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z'/%3E%3C/svg%3E") no-repeat 50% 50%;
				mask-size: cover;
				position: relative;
				top: 1px;
				width: 14px;
			}
		}
	}

	// Top right arrow after the text
	&--arrow-top {
		> a, 
		> span {
			&::before {
				content: none;
			}
            
			&::after {
				background-color: currentcolor;
				content: "";
				display: inline-block;
				height: 12px;
				margin-left: em(7);
				mask: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 13.9 13.9' enable-background='new 0 0 13.9 13.9' xml:space='preserve'%3E%3Cpath fill='none' stroke='currentColor' stroke-width='1.7' stroke-linecap='round' stroke-linejoin='round' d='M0.9,13.1L13.1,0.9'/%3E%3Cpath fill='none' stroke='currentColor' stroke-width='1.7' stroke-linecap='round' stroke-linejoin='round' d='M2.6,0.9h10.5v10.5'/%3E%3C/svg%3E");
				mask-size: cover;
				position: relative;
				width: 12px;
			}
		}
	}

	// Ticket icon before the text
	&--ticket {
		> a, 
		> span {
			&::before {
				background-color: currentcolor;
				content: "";
				display: inline-block;
				height: 14px;
				margin-right: em(7);
				mask: url("data:image/svg+xml,%3Csvg width='16' height='15' fill='currentColor' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.5 5.833v-2.5c0-1.666-.833-2.5-2.5-2.5h-1.625a.25.25 0 0 0-.25.25V3.75a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625V1.083a.25.25 0 0 0-.25-.25H3c-1.667 0-2.5.834-2.5 2.5v2.5c.917 0 1.667.75 1.667 1.667S1.417 9.167.5 9.167v2.5c0 1.666.833 2.5 2.5 2.5h6.625a.25.25 0 0 0 .25-.25V11.25a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v2.667c0 .138.112.25.25.25H13c1.667 0 2.5-.834 2.5-2.5v-2.5c-.917 0-1.667-.75-1.667-1.667s.75-1.667 1.667-1.667Zm-4.375 2.709a.625.625 0 0 1-1.25 0V6.458a.625.625 0 0 1 1.25 0v2.084Z' fill='%23E7B00D'/%3E%3C/svg%3E");
				mask-size: cover;
				width: 16px;
			}
		}
	}

	// Program icon before the text
	&--program {
		position: relative;

		> a, 
		> span {
			&::before {
				background-color: currentcolor;
				content: "";
				display: inline-block;
				height: 18.33px;
				margin-right: em(7);
				mask: url("data:image/svg+xml,%3Csvg width='18' height='20' viewBox='0 0 18 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0.75 15.1663L0.75 4.83301C0.750001 2.62387 2.54086 0.833008 4.75 0.833008L13.25 0.833008C15.4591 0.833008 17.25 2.62387 17.25 4.83301V10.1762C17.25 10.5057 17.2093 10.8311 17.1308 11.1455H13.5833C11.1786 11.1455 9.22917 13.0949 9.22917 15.4997V19.0471C8.91471 19.1257 8.58936 19.1663 8.25981 19.1663H4.75C2.54086 19.1663 0.75 17.3755 0.75 15.1663ZM10.6042 18.4074C10.7752 18.2837 10.9371 18.1459 11.0882 17.9948L16.0784 13.0046C16.2295 12.8535 16.3673 12.6915 16.491 12.5205H13.5833C11.938 12.5205 10.6042 13.8543 10.6042 15.4997V18.4074Z' fill='%23133A63'/%3E%3C/svg%3E");
				mask-size: cover;
				width: 16.5px;
			}
		}
	}

	&--galliano {
		> a, 
		> span {
			&::before {
				background-color: $galliano;
			}
		}
	}

	&--tall-poppy {
		> a, 
		> span {
			&::before {
				background-color: $tall-poppy;
			}
		}
	}

	&--chathams-blue {
		> a, 
		> span {
			&::before {
				background-color: $chathams-blue;
			}
		}
	}

	&--razzmatazz {
		> a, 
		> span {
			&::before {
				background-color: $razzmatazz;
			}
		}
	}
}

// Text slice hover effects
.link-slice-hover {
	animation-duration: 1s;
	animation-fill-mode: both;
	display: block;
	overflow: hidden;
	position: relative;
	text-align: center;
	user-select: none;

	span {
		display: block;
		// transition: transform 500ms cubic-bezier( 0.680, -0.550, 0.265, 1.550 );
		transition: transform .3s ease;
	}

	&::after {
		content: attr( data-link-alt );
		display: inline;
		left: 0;
		position: absolute;
		right: 0;
		text-align: right;
		top: 100%;
		// transition: top 500ms cubic-bezier( 0.680, -0.550, 0.265, 1.550 );
		transition: top .3s ease;
	}

	&:hover {
		span {
			transform: translateY( -100% ); 
		}

		&::after {
			top: 0;
		}
	}
}

.apply-first-link {
	&:hover {
		.link-slice-hover {
			span {
				transform: translateY( -100% );
			}

			&::after {
				top: 0;
			}
		}
	}
}