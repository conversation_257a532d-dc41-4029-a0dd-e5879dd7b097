// Pagination
.page-pagination {
	margin-top: down(60);
	@media (max-width: 991px) {
		margin-top: down(40);
	}
	@media (max-width: 781px) {
		margin-top: down(25);
	}
}

// Extending changes to Bootstrap variables in custom/_variables.scss
.pagination.pagination-lg {
	.page-item {
		&.active {
			a:hover {
				color: $galliano;
			}
		}
	}

	.page-link {
		align-items: center;
		border-radius: 50%;
		display: flex;
		font-family: $poppins;
		font-weight: 700;
		height: down(69, 20);
		justify-content: center;
		width: down(69, 20);
		@media (max-width: 991px) {
			font-size: down(18);
			height: down(50, down(18));
			width: down(50, down(18));
		}

		&.dots {
			border-color: transparent;
		}

		&--prev,
		&--next {
			span {
				bottom: 1px;
				position: relative;
			}
		}
	}
}