.breadcrumb {
	display: flex;
	// Prevent wrapping to next line
	flex-wrap: nowrap;
	font-size: 16px;
	letter-spacing: -0.01em;
	margin-bottom: down(15);
	overflow: hidden;

	@media (max-width: 991px) {
		margin-bottom: down(10);
	}
	@media (max-width: 781px) {
		margin-bottom: down(5);
	}

	&-item {
		// Prevent individual items from shrinking too much, except the last one
		flex-shrink: 0;
		// Ensure text doesn't wrap within items
		white-space: nowrap;

		a {
			color: $mine-shaft;
			text-decoration: none;

			&:hover {
				text-decoration: underline;
			}
		}

		&.active {
			color: $mine-shaft;
			// Allow the last (active) item to shrink and show ellipsis
			flex-shrink: 1;
			// Ensure it takes available space but can shrink
			min-width: 0;
			opacity: 0.8;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}

.single-event {
	.breadcrumb {
		margin-bottom: down(15);

		&-item {
			a {
				color: $white;
			}

			&.active {
				color: $alto;
				// Ensure ellipsis styling is maintained for single-event pages
				flex-shrink: 1;
				min-width: 0;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}

	.breadcrumb-item + .breadcrumb-item {
		&::before {
			color: $alto;
		}
	}
}