// Layout
// ---------------------
main {
	padding-bottom: 200px;

	@media (max-width: 991px) {
		padding-bottom: 120px;
	}
	@media (max-width: 781px) {
		padding-bottom: 60px;
	}
}

.single main {
	padding-bottom: 0;
}

.content-block {
	font-size: 1.1rem;
	@media (max-width: 1600px) {
		font-size: 1.05rem;
	}
}

.home main,
.has-custom-slider main {
	padding-bottom: 170px;

	@media (max-width: 991px) {
		padding-bottom: 120px;
	}
	@media (max-width: 782px) {
		padding-bottom: 60px;
	}
}

.container-fluid {
	max-width: 100%;
	width: 1820px;
}

.container {
	max-width: 900px;
}

// Custom gutters
.gx-custom {
	--bs-gutter-x: 30px;
}

// Add side padding to fluid container
.container,
.container-fluid {
	padding-left: calc( var(--bs-gutter-x) * 2 );
	padding-right: calc( var(--bs-gutter-x) * 2 );

	@media (max-width: 991px) {
		padding-left: calc( var(--bs-gutter-x) * 1.5 );
		padding-right: calc( var(--bs-gutter-x) * 1.5 );
	}
	@media (max-width: 781px) {
		padding-left: calc( var(--bs-gutter-x) );
		padding-right: calc( var(--bs-gutter-x) );
	}
}

body .is-layout-flex.wp-block-columns {
	
	// Lower bottom margin and gap on mobile devices
	@media (max-width: 781px) {
		margin-bottom: 1em;
		row-gap: 12px;
	}

	// Align to left on mobile devices
	.is-content-justification-right {
		@media (max-width: 781px) {
			justify-content: flex-start;
		}
	}
}

// Custom border with padding on sides (to be aligned with text inside Bootstrap container)
// ---------------------
.border-custom {
	position: relative;

	&--top {
		&::before {
			border-bottom: 1px solid $alto;
			content: "";
			display: block;
			height: 1px;
			left: calc( 0.5 * var(--bs-gutter-x) );
			position: absolute;
			right: calc( 0.5 * var(--bs-gutter-x) );
			top: 0;
		}
	}

	&--bottom {
		&::after {
			border-bottom: 1px solid $alto;
			bottom: 0;
			content: "";
			display: block;
			height: 1px;
			left: calc( 0.5 * var(--bs-gutter-x) );
			position: absolute;
			right: calc( 0.5 * var(--bs-gutter-x) );
		}
	}

}

// Fix section tag and container-fluid being added twice to for Gutenberg blocks (causing extra side padding)
section .container-fluid section .container-fluid {
	padding-left: 0;
	padding-right: 0;
}

.apply-first-link {
	cursor: pointer;
}

// Gravity Forms
.gform_required_legend {
	display: none;
}

// CookieYes
[data-cky-tag=detail-powered-by] {
	display: none!important;
}