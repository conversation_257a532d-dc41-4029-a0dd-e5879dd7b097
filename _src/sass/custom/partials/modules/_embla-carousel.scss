.embla {
	--slide-spacing: 1.6rem;
	--slide-size: 25%;
	--slide-height: 19rem;
	overflow: hidden;
}

.embla__container {
	backface-visibility: hidden;
	display: flex;
	gap: var(--slide-spacing);
	touch-action: pan-y;
}

.embla__slide {
	flex: 0 0 var(--slide-size);
	min-width: 0;
	position: relative;
}

.embla__slide__img {
	display: block;
	height: var(--slide-height);
	object-fit: cover;
	width: 100%;
}

.embla__slide__number {
	background-color: rgba(var(--background-site-rgb-value), 0.85);
	border-radius: 50%;
	font-weight: 900;
	height: 4.6rem;
	line-height: 4.6rem;
	pointer-events: none;
	position: absolute;
	right: 0.6rem;
	text-align: center;
	top: 0.6rem;
	width: 4.6rem;
	z-index: 1;
}

.embla__slide__number > span {
	background-clip: text;
	background-clip: text;
	background-image: linear-gradient(
		45deg,
		var(--brand-primary),
		var(--brand-secondary)
	);
	bottom: 0;
	color: var(--brand-primary);
	display: block;
	font-size: 1.6rem;
	left: 0;
	position: absolute;
	right: 0;
	-webkit-text-fill-color: transparent;
	top: 0;
}

.embla__button {
	appearance: none;
	background-color: transparent;
	border: 0;
	cursor: pointer;
	display: inline-flex;
	margin: 0;
	padding: 0;
	text-decoration: none;
	touch-action: manipulation;
}

.embla__navigation {
	align-items: center;
	display: flex;
}

.embla__button {
	align-items: center;
	color: var(--background-site);
	cursor: pointer;
	display: flex;
	display: none;
	height: 4rem;
	justify-content: center;
	width: 4rem;
	z-index: 1
}

.embla__button:disabled {
	opacity: 0.3;
}

.embla__button__svg {
	height: 65%;
	width: 65%;
}

.embla__dots {
	align-items: center;
	bottom: 1.6rem;
	display: flex;
	gap: .4rem;
	justify-content: start
}

.embla__dot {
	align-items: center;
	appearance: none;
	background-color: transparent;
	border: 0;
	cursor: pointer;
	display: inline-flex;
	display: flex;
	height: 2.4rem;
	margin: 0;
	padding: 0;
	text-decoration: none;
	touch-action: manipulation;
	width: 2.4rem;
}

.embla__dot::after {
	background: #a7a7a7;
	content: '';
	height: 0.3rem;
	width: 100%;
}

.embla__dot--selected::after {
	background: #121212;
}
