.faq {
	margin-bottom: 80px;
	
	@include media-breakpoint-up(lg) {
		margin-bottom: 160px;
	}

	.tab-content > .tab-pane {
		display: block;
	}


	&__title {
		font-size: 16px;
		margin-bottom: 22px;
		text-transform: uppercase;
		@include media-breakpoint-up(lg) {
			font-size: 24px;
			margin-bottom: 38px;
		}
	}

	&__category {
		border-bottom: 1px solid #DDD;

		&:last-child {
			border-bottom: 0;
		}
	}

	.accordion-item {
		background-color: #fff;
	}

	.accordion-button:not(.collapsed) {
		background: transparent;
		box-shadow: none;
		color: initial;

		&::after {
			background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
		}
	}
	
	.accordion-button,
	.accordion-body {
		background-color: #fff;
		font-size: 16px;
		font-weight: 500;
		padding-left: 0;
		padding-right: 0;
		@include media-breakpoint-up(lg) {
			font-size: 24px;
		}

		&:focus {
			background: transparent;
			border-color: transparent;
			box-shadow: none;
		}
	}
}