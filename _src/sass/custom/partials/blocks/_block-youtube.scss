.block--youtube {
	small {
		align-items: center;
		display: flex;
		font-size: 13px;
		letter-spacing: normal;
		padding: 2px 8px;
		position: absolute;
		right: 10px;
		top: 10px;
		user-select: none;
        
		&::before {
			animation: blinking 2s linear infinite;
			background-color: $tall-poppy;
			content: "";
			display: inline-block;
			height: 9px;
			margin-right: em(6);
			mask: url("data:image/svg+xml,%3Csvg width='10' height='10' viewBox='0 0 10 10' fill='currentColor' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='5' cy='5' r='4.5' fill='currentColor'/%3E%3C/svg%3E") no-repeat 50% 50%;
			mask-size: cover;
			width: 9px;
		}
	}

	&__img {
		position: relative;
	}
}

@keyframes blinking {
	50% {
		opacity: 0;
	}
}