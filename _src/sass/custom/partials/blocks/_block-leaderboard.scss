.block--leaderboard {
	.table-container table {
		width: 100%;
	}

	.js-error-meetings {
		display: none;
		margin-top: 30px;
		text-align: center;
	}

	.js-error-meetings.active {
		display: block;
	}

	.table-container .table-horse {
		width: 25%;
	}

	.table-container .year-select {
		appearance: none;
		background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
		background-position: right 10px center;
		background-repeat: no-repeat;
		background-size: 1em;
		height: 45px;
		padding: 0;
		padding: 0 10px;
		width: 150px;
	}

	.table-container .meeting-select {
		appearance: none;
		background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
		background-position: right 10px center;
		background-repeat: no-repeat;
		background-size: 1em;
		height: 45px;
		padding: 0;
		padding: 0 10px;
		width: 175px;
	}

	.filters {
		align-items: center;
		column-gap: 10px;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
		margin-bottom: 30px;
		margin-top: 30px;
		row-gap: 10px;

		select {
			border-radius: 0;
			user-select: none;

			&:focus,
			&:focus-visible {
				border-color: #000;
				border-radius: 0;
				outline: none;
			}
		}
	}

	.selects {
		column-gap: 10px;
		display: flex;
	}

	.divisions {
		column-gap: 5px;
		display: flex;

		button {
			line-height: 1;
		}
	}

	.divisions-serien {
		background: #FFC20D;
		border: 1px solid #FFC20D;
		border-radius: 0;
		color: #000;
		cursor: pointer;
		font-size: 16px;
		font-weight: 400;
		height: 45px;
		letter-spacing: -0.025em;
		line-height: 18px;
		padding: 14px;
	}

	.divisions-serien.active {
		background: #FFF;
		border: 1px solid #FFC20D;
		border-radius: 0;
		cursor: pointer;
		height: 45px;
		outline: none;
	}

	.table-container img {
		margin-right: auto;
	}

	.table-container tr {
		@media (max-width: 781px) {
			contain-intrinsic-size: 60px;
			content-visibility: auto;
		}
	}

	.table-container td,
	.table-container th {
		border-bottom: 1px solid #E5E5E5;
		padding: 20px 30px;

		@media (max-width: 1199px) {
			padding: 15px 20px;
		}
		@media (max-width: 781px) {
			padding: 10px;

			&:first-child {
				padding-left: 0;
			}

			&:last-child {
				padding-right: 0;
			}
		}
	}

	@media (max-width: 1050px) {
		.table-container {
			.filters {
				display: flex;

				img {
					order: 1;
				}

				.divisions {
					order: 2;
				}

				.selects {
					column-gap: 5px;
					flex-basis: 100%;
					justify-content: flex-end;
					order: 3;
				}
			}
		}
	}

	@media (max-width: 781px) {
		.table-container img {
			display: block;
			margin-bottom: 5px;
			margin-right: auto;
		}

		.table-container .filters {
			width: 100%;
		}

		.table-container .divisions {
			display: flex;
			flex-wrap: wrap;
			width: 100%;
		}

		.table-container .selects select {
			width: calc( 50% - 2.5px );
		}

		.table-container .year-select,
		.table-container .meeting-select,
		.table-container button {
			width: calc( ( 100% / 3 ) - ( 10px / 3 ) );
		}

		.table-wrapper {
			overflow-x: scroll;
		}

		.table-container {
			// th {
			// 	position: sticky;
			// 	top: 0;
			// }

			td {
				// &:first-child {
				// 	position: sticky;
				// }

				// &:nth-child(2) {
				// 	position: sticky;
				// }

				&:last-child {
					white-space: nowrap;
				}
			}
		}

		/* Force table to not be like tables anymore */
		// table, thead, tbody, th, td, tr { 
		// 	display: block; 
		// }
		
		/* Hide table headers (but not display: none;, for accessibility) */
		// thead tr {
		// 	left: -9999px; 
		// 	position: absolute;
		// 	top: -9999px;
		// }
		
		// .table-container .meeting tr:nth-child(odd) {
		// 	background-color: #E5E5E5;
		// }
		
		// .table-container td { 
		// 	/* Behave  like a "row" */
		// 	border-bottom: 1px solid #eee;
		// 	padding-left: 50%; 
		// 	position: relative; 
		// }
		
		// .table-container td::before {
		// 	left: 16px; 
		// 	padding-right: 10px; 
		// 	/* Now like a table header */
		// 	position: absolute;
		// 	/* Top/left values mimic padding */
		// 	top: 50%;
		// 	transform: translateY(-50%); 
		// 	white-space: nowrap;    
		// 	width: 45%;
		// }
		
		// /*
		// Label the data
		// */
		// td:nth-of-type(1)::before { content: "Plats"; }
		// td:nth-of-type(2)::before { content: "Häst"; }
		// td:nth-of-type(3)::before { content: "Poäng"; }
		// td:nth-of-type(4)::before { content: "Starter"; }
		// td:nth-of-type(5)::before { content: "Tränare"; }
	}

	@media (max-width: 580px) {
		.divisions {
			row-gap: 5px;
		}
		// .table-container .selects select {
		// 	width: 100%;
		// }

		.table-container .year-select,
		.table-container .meeting-select,
		.table-container button {
			width: 100%;
		}
	}

}