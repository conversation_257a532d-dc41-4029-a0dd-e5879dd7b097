.calendar {
	@media (min-width: 1500px) {
		display: grid;
		gap: 0 8rem;
		grid-template-columns: 1fr 1fr;
	}


	&__separator {
		border-bottom: 1px solid $alto;
		display: flex;
		grid-column: 1 / -1;
		// font-size: 16px;
		padding-bottom: down(26);
		padding-top: down(60);
	}

	&__event {
		border-bottom: 1px solid $alto;
		display: flex;
		gap: 0 down(32);
		// font-size: 16px;
		padding-bottom: down(26);
		padding-top: down(26);
		position: relative;

		@media (max-width: 991px) {
			flex-wrap: wrap;
		}

		h4 {
			margin-bottom: down(10);
		}

		&__img {
			width: fit-content;
		}

		img {
			@media (min-width: 1000px) {
				max-width: 400px;
			}


			// height: 257.69px;
			// left: 0;
			// object-fit: cover;
			// opacity: 0;
			// pointer-events: none;
			// position: fixed;
			// top: 0;
			// visibility: hidden;
			// width: 459px;
			// z-index: 9;



			// @media (max-width: 991px) {
			// 	display: none;
			// }
		}

		&__details {
			max-width: 100%;

			@media (max-width: 991px) {
				width: 100%;
			}

			div:first-child {
				margin-bottom: down(3);
			}
		}

		&__content {
			align-items: stretch;
			display: grid;
			grid-template-columns: 100%;
			max-width: 100%;
			row-gap: down(26);		
			width: 100%;
		}

		&__links {
			align-items: center;
			display: flex;
			flex-wrap: wrap;
			gap: down(26);
			justify-content: flex-end;
			margin-top: auto;
			max-width: 100%;
			position: relative;

			@media (max-width: 991px) {
				justify-content: flex-start;
			}

			// This is to prevent image hover
			&::before {
				bottom: calc( -1.38125em - 1px );
				content: "";
				display: block;
				left: 0;
				position: absolute;
				right: 0;
				top: calc( -1.38125em - 1px );

				@media (max-width: 991px) {
					bottom: 13.75px;
					top: 13.75px;
				}
			}

			> div {
				// 	margin-left: em(49);
				position: relative;
				text-align: right;
				white-space: nowrap;
				z-index: 1;

				@media (max-width: 1500px) {
					text-align: left;
				}
				@media (max-width: 991px) {
					margin-left: 0;
					margin-right: em(49);
					margin-top: down(16);
				}

				&:last-child {
					margin-right: 0;
				}

			}

		}

		&__meta {
			max-width: 100%;
			width: 450px;

			> div {
				&:first-child {
					@media (max-width: 991px) {
						margin-bottom: down(12);
					}
				}

				> div:first-child {
					font-weight: 700;
					margin-bottom: down(3);
				}
			}
		}

	}
    
}