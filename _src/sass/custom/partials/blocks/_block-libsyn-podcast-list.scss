.block--libsyn-podcast-list {
	th {
		font-weight: 400;
	}

	th, td {
		padding-bottom: down(26);
		padding-top: down(26);

		@media (max-width: 991px) {
			padding-bottom: down(20);
			padding-top: down(20);
		}
		@media (max-width: 781px) {
			padding-bottom: down(15);
			padding-top: down(15);
		}

		&:first-child {
			max-width: 100%;
			padding-left: 0;
			width: 100px;

			@media (max-width: 991px) {
				width: 80px;
			}
			@media (max-width: 781px) {
				width: 60px;
			}
			@media (max-width: 600px) {
				width: 45px;
			}
		}

		&:last-child {
			padding-right: 0;
		}
	}

}
    
.podcast-list__episode {
	position: relative;

	&__img {
		height: 257.69px;
		left: 0;
		object-fit: cover;
		opacity: 0;
		pointer-events: none;
		position: fixed;
		top: 0;
		visibility: hidden;
		width: 459px;
		z-index: 9;
	}
}