.block--accordion {
	.accordion-item {
		border-radius: 0;

		.accordion-button {
			background-color: #f9f9f9;
			border-radius: 0;

			&:focus {
				box-shadow: none;
			}

			&::after {
				background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
			}

			&:not(.collapsed) {
				box-shadow: inset 0 calc(-1*var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
				color: $cod-grey;
			}
		}

		.accordion-body {
			padding-bottom: 30px;
			padding-top: 30px;

			@media (max-width: 991px) {
				padding-bottom: 20px;
				padding-top: 20px;
			}

			@media (max-width: 781px) {
				padding-bottom: 10px;
				padding-top: 10px;
			}
		}
	}

    
}