.block--slider {
	background-color: #000; /* Add black background to the slider container */
	position: relative;
	user-select: none;

	/* Add black background to carousel items */
	.carousel-item {
		background-color: #000;
	}

	.carousel-inner {
		background-color: #000; /* Add black background to the carousel inner container */
	}

	.top-image {
		--small_text_font_size: var(--small_text_font_size, 1);
		--heading_font_size: var(--heading_font_size, 1);
		--overlay_opacity: var(--overlay_opacity, 0.6);
		background-color: #000; /* Add black background to each slide */
		position: relative;

		&__content {
			bottom: 50%;
			transform: translateY(50%);

			@media (max-width: 781px) {
				bottom: em(90);
				transform: none;
			}

			@media (max-width: 680px) {
				bottom: down(60);
				top: auto;
			}

			&__meta {
				font-size: clamp(1em, calc(down(20) * var(--small_text_font_size, 1)), 60px);
				margin-bottom: down(11);
				margin-top: 0;

				@media (max-width: 680px) {
					margin-bottom: down(6);
					order: 2;
				}

				@media (max-width: 500px) {
					margin-bottom: down(2);
					width: 100%;
				}
			}

			&__btns {
				@media (max-width: 680px) {
					order: 5;
				}

				.btn {
					--bs-btn-color: var(--button_acf_text_color, #000);
					--bs-btn-bg: var(--button_acf_background, #fff);
					--bs-btn-border-color: var(--button_acf_background, #fff);
					--bs-btn-hover-border-color: var(--button_acf_border_hover_color, #fff);
					--bs-btn-hover-color: var(--button_acf_text_hover_color, #fff);
					--bs-btn-hover-bg: transparent;
					--bs-btn-focus-shadow-rgb: 217, 217, 217;
					--bs-btn-active-color: #000;
					--bs-btn-active-bg: #fff;
					--bs-btn-active-border-color: #fff;
					--bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
					background-color: var(--bs-btn-bg);
					color: var(--bs-btn-color);

					a {
						color: var(--bs-btn-color);
					}

					&:hover {
						background-color: var(--bs-btn-hover-bg);
						border-color: var(--bs-btn-hover-border-color);
						color: var(--bs-btn-hover-color);

						a {
							color: var(--bs-btn-hover-color);
						}
					}
				}

				.btn--large {
					border-radius: em(34);
					padding: em(12, 15) em(24, 15);

					@media (max-width: 1199px) {
						padding: em(9, 15) em(20, 15);
					}
				}
			}

			&__inner {
				margin-left: auto;
				max-width: 100%;
				position: relative;
				width: 50%;

				// @media (max-width: 1400px) {
				// 	width: 100%;
				// }
				@media (max-width: 991px) {
					width: 50%;
				}

				@media (max-width: 781px) {
					width: 100%;
				}

				@media (max-width: 680px) {
					display: flex;
					flex-direction: column;
				}

				@media (max-width: 600px) {
					width: 100%;
				}
			}

			&__desc {
				display: none;
				max-width: 100%;
				width: 450px;

				@media (max-width: 680px) {
					display: block;
					order: 4;
				}
			}

			h1 {
				margin-bottom: down(7);

				@media (max-width: 1600px) {
					font-size: calc((1.5075rem + 2.5vw) * var(--heading_font_size, 1));
				}

				@media (max-width: 1199px) {
					font-size: calc((1.5075rem + 2vw) * var(--heading_font_size, 1));
				}

				@media (max-width: 781px) {
					font-size: calc(1.38rem + 1.56vw);
				}

				@media (max-width: 680px) {
					// margin-bottom: 3px;
					// margin-bottom: down(6);
					order: 3;
				}

				@media (min-width: 1600px) {
					font-size: clamp(1em, calc((1.35rem + 3.09vw) * var(--heading_font_size, 1)), 100px);
					margin-bottom: down(4);
				}
			}
		}

		&.focus-point-right {
			.top-image__content {
				&__inner {
					left: 2.5%;
					margin-left: 0;
					padding-right: 0;

					@media (max-width: 991px) {
						left: 0;
					}
				}
			}
		}
	}

	&__nav {
		bottom: 0;
		left: 0;
		position: absolute;
		right: 0;
		z-index: 1;

		@media (max-width: 680px) {
			bottom: auto;
			top: 50px;
		}

		&__row {
			@media (max-width: 680px) {
				border-bottom: 1px solid rgba(234, 234, 234, 0.5);
				flex-wrap: wrap;
				gap: 0 !important;
			}
		}

		&__col {
			border-top: 1px solid rgba(234, 234, 234, 0.8);
			flex-basis: calc(100% / 3);
			transition: border-color .3s ease;
			width: calc(100% / 3);

			@media (max-width: 680px) {
				border-top: 1px solid rgba(234, 234, 234, 0.5);
				flex-basis: 100%;
				width: 100%;
			}

			&:hover {
				border-top: 1px solid rgba(234, 234, 234, 1);
			}

			a {
				display: block;
				margin: down(30) 0;

				@media (max-width: 1200px) {
					margin: down(20) 0;
				}

				@media (max-width: 680px) {
					margin: down(11) 0;
				}

				svg {
					margin-right: em(8);

					@media (max-width: 680px) {
						margin-right: em(13);
					}
				}
			}
		}

	}

	.carousel-indicators-wrapper {
		bottom: em(110);
		left: 0;
		position: absolute;
		right: 0;

		@media (max-width: 1199px) {
			bottom: em(90);
		}

		@media (max-width: 680px) {
			// position: relative;
			bottom: 0;
			// top: auto;
		}
	}

	.carousel-indicators {
		bottom: auto;
		bottom: -13px;
		justify-content: flex-end;
		left: auto;
		margin-bottom: 0;
		margin-left: 0;
		margin-right: 0;
		margin-right: -3px;
		position: relative;

		@media (max-width: 680px) {
			justify-content: flex-start;
			margin-bottom: down(15);
			position: static;
			right: auto;
			top: auto;
		}

	}

	// .carousel-control-prev, .carousel-control-next {
	// 	height: 100px;
	// 	top: 50%;
	// 	transform: translateY(-50%);
	// }

	/* Video aspect ratios are now handled by the carousel-item aspect ratio */

	/* Style for placeholder while images are loading - using aspect ratio */
	.carousel-item,
	.carousel-item .img-fluid:has(video) {
		/* Default aspect ratio for all screen sizes */
		aspect-ratio: 16 / 9;
		overflow: hidden; /* Ensure content doesn't overflow */
		position: relative;
		width: 100%;

		/* Responsive aspect ratios */
		@media (max-width: 450px) {
			aspect-ratio: 357 / 715; /* Mobile portrait */
		}

		@media (min-width: 451px) and (max-width: 680px) {
			aspect-ratio: 500 / 646; /* Mobile landscape */
		}

		@media (min-width: 681px) and (max-width: 991px) {
			aspect-ratio: 900 / 726; /* Tablet */
		}

		@media (min-width: 992px) {
			aspect-ratio: 1200 / 675; /* Desktop */
		}
	}

	.top-image.focus-point-right::before {
		background: 
			linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0) 54%, #000 100%),
			linear-gradient(90deg, rgba(0, 0, 0, var(--overlay_opacity)) 0%, transparent 100%);

		@media (max-width: 781px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0.1) 40%, #000 100%);
		}

		@media (max-width: 680px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.16) 32%, rgba(0, 0, 0, 0.06) 54%, #000 100%);
		}
	}

	.top-image.focus-point-left::before {
		background: 
			linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0) 54%, #000 100%),
			linear-gradient(-90deg, rgba(0, 0, 0, var(--overlay_opacity)) 0%, transparent 100%);

		@media (max-width: 781px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0.1) 40%, #000 100%);
		}

		@media (max-width: 680px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.16) 32%, rgba(0, 0, 0, 0.06) 54%, #000 100%);
		}
	}
}

.carousel--custom {
	.carousel-indicators-wrapper {
		bottom: 18px;

		@media (max-width: 1199px) {
			bottom: 18px;
		}

		@media (max-width: 680px) {
			bottom: 0;
		}
	}

	.carousel-indicators {
		bottom: 0;
	}

	.top-image__content {
		@media (max-width: 781px) {
			bottom: down(60);
		}
	}

	.top-image.focus-point-right::before {
		background: 
			linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0) 54%, transparent 100%),
			linear-gradient(90deg, rgba(0, 0, 0, var(--overlay_opacity)) 0%, transparent 100%);

		@media (max-width: 781px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0.1) 40%, #000 100%);
		}

		@media (max-width: 680px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.16) 32%, rgba(0, 0, 0, 0.06) 54%, #000 100%);
		}
	}

	.top-image.focus-point-left::before {
		background: 
			linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0) 54%, transparent 100%),
			linear-gradient(-90deg, rgba(0, 0, 0, var(--overlay_opacity)) 0%, transparent 100%);

		@media (max-width: 781px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.13) 16%, rgba(0, 0, 0, 0.1) 40%, #000 100%);
		}

		@media (max-width: 680px) {
			background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0.16) 32%, rgba(0, 0, 0, 0.06) 54%, #000 100%);
		}
	}
}
