@font-face {
	font-display: swap;
	font-family: 'Helvetica Now Display';
	font-style: normal;
	font-weight: 500;
	src: url('../../fonts/HelveticaNowDisplay-500.eot');
	src: url('../../fonts/HelveticaNowDisplay-500.eot?#iefix') format('embedded-opentype'),
		url('../../fonts/HelveticaNowDisplay-500.woff2') format('woff2'),
		url('../../fonts/HelveticaNowDisplay-500.woff') format('woff'),
		url('../../fonts/HelveticaNowDisplay-500.ttf') format('truetype');
}
@font-face {
	font-display: swap;
	font-family: 'Helvetica Now Display';
	font-style: normal;
	font-weight: 700;
	src: url('../../fonts/HelveticaNowDisplay-700.eot');
	src: url('../../fonts/HelveticaNowDisplay-700.eot?#iefix') format('embedded-opentype'),
		url('../../fonts/HelveticaNowDisplay-700.woff2') format('woff2'),
		url('../../fonts/HelveticaNowDisplay-700.woff') format('woff'),
		url('../../fonts/HelveticaNowDisplay-700.ttf') format('truetype');
}
@font-face {
	font-display: swap;
	font-family: 'Poppins';
	font-style: normal;
	font-weight: 700;
	src: url('../../fonts/Poppins-700.eot');
	src: url('../../fonts/Poppins-700.eot?#iefix') format('embedded-opentype'),
		url('../../fonts/Poppins-700.woff2') format('woff2'),
		url('../../fonts/Poppins-700.woff') format('woff'),
		url('../../fonts/Poppins-700.ttf') format('truetype');
}
@font-face {
	font-display: swap;
	font-family: 'Poppins';
	font-style: normal;
	font-weight: 800;
	src: url('../../fonts/Poppins-800.eot');
	src: url('../../fonts/Poppins-800.eot?#iefix') format('embedded-opentype'),
		url('../../fonts/Poppins-800.woff2') format('woff2'),
		url('../../fonts/Poppins-800.woff') format('woff'),
		url('../../fonts/Poppins-800.ttf') format('truetype');
}