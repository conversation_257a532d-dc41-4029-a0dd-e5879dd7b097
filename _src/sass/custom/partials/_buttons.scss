@keyframes spin {
	100% {
		transform: rotate( 360deg );
	}
}

.btn {
	align-items: center;
	border-radius: em(24);
	display: inline-flex!important;
	font-size: 15px;
	font-weight: 500;
	padding: em(6, 15) em(14, 15);
	text-transform: uppercase;

	// Dot before button text
	&--dot {;
		&::before {
			color: $bermuda-gray;
			content: "";
			display: inline-block;
			height: 10px;
			margin-right: em(7);
			mask: url("data:image/svg+xml,%3Csvg width='10' height='10' viewBox='0 0 10 10' fill='currentColor' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='5' cy='5' r='4.5' fill='currentColor'/%3E%3C/svg%3E") no-repeat 50% 50%;
			mask-size: cover;
			width: 10px;
		}
	}

	// Ticket icon before button text
	&--ticket {
		&::before {
			content: "";
			display: inline-block;
			height: 13.33px;
			margin-right: em(9);
			mask: url("data:image/svg+xml,%3Csvg width='16' height='15' fill='currentColor' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.5 5.833v-2.5c0-1.666-.833-2.5-2.5-2.5h-1.625a.25.25 0 0 0-.25.25V3.75a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625V1.083a.25.25 0 0 0-.25-.25H3c-1.667 0-2.5.834-2.5 2.5v2.5c.917 0 1.667.75 1.667 1.667S1.417 9.167.5 9.167v2.5c0 1.666.833 2.5 2.5 2.5h6.625a.25.25 0 0 0 .25-.25V11.25a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v2.667c0 .138.112.25.25.25H13c1.667 0 2.5-.834 2.5-2.5v-2.5c-.917 0-1.667-.75-1.667-1.667s.75-1.667 1.667-1.667Zm-4.375 2.709a.625.625 0 0 1-1.25 0V6.458a.625.625 0 0 1 1.25 0v2.084Z' fill='%23E7B00D'/%3E%3C/svg%3E");
			mask-size: cover;
			position: relative;
			top: -1px;
			width: 15px;
		}
	}

	// Download icon before button text
	&--download {
		&::before {
			content: "";
			display: inline-block;
			height: 19px;
			margin-right: em(10);
			mask: url("data:image/svg+xml,%3Csvg width='18' height='19' viewBox='0 0 18 19' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 11V16C18 18 17 19 15 19H3C1 19 0 18 0 16V11C0 9 1 8 3 8H8.25V12.189L6.53003 10.469C6.23703 10.176 5.76199 10.176 5.46899 10.469C5.17599 10.762 5.17599 11.237 5.46899 11.53L8.46899 14.53C8.53799 14.599 8.62089 14.654 8.71289 14.692C8.80489 14.73 8.902 14.75 9 14.75C9.098 14.75 9.19511 14.73 9.28711 14.692C9.37911 14.654 9.46201 14.599 9.53101 14.53L12.531 11.53C12.824 11.237 12.824 10.762 12.531 10.469C12.238 10.176 11.763 10.176 11.47 10.469L9.75 12.189V8H15C17 8 18 9 18 11ZM9.75 1C9.75 0.586 9.414 0.25 9 0.25C8.586 0.25 8.25 0.586 8.25 1V8H9.75V1Z' fill='currentColor'/%3E%3C/svg%3E");
			mask-size: cover;
			position: relative;
			top: -1px;
			width: 18px;
		}
	}

	// Spotify icon before button text
	&--spotify {
		border: 1px solid $alto;
		color: $chathams-blue !important;
		text-transform: none;
		transition: border-color .15s ease-in-out;

		&::before {
			background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 22 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.0063 0.5C5.22783 0.5 0.5 5.22783 0.5 11.0063C0.5 16.7848 5.22783 21.5126 11.0063 21.5126C16.7848 21.5126 21.5126 16.7848 21.5126 11.0063C21.5126 5.22783 16.8373 0.5 11.0063 0.5ZM15.8392 15.6816C15.6291 15.9968 15.2614 16.1019 14.9462 15.8917C12.4772 14.3683 9.37782 14.0531 5.70062 14.8936C5.3329 14.9987 5.01771 14.736 4.91265 14.4208C4.80758 14.0531 5.07024 13.7379 5.38543 13.6329C9.37782 12.7398 12.8449 13.1076 15.5765 14.7886C15.9443 14.9462 15.9968 15.3664 15.8392 15.6816ZM17.1 12.7924C16.8373 13.1601 16.3645 13.3177 15.9968 13.055C13.1601 11.3215 8.85251 10.7962 5.54302 11.8468C5.12277 11.9519 4.64999 11.7417 4.54493 11.3215C4.43986 10.9012 4.64999 10.4285 5.07024 10.3234C8.90504 9.1677 13.6329 9.74554 16.8898 11.7417C17.205 11.8993 17.3626 12.4247 17.1 12.7924ZM17.205 9.85061C13.843 7.85441 8.22213 7.64428 5.01771 8.64238C4.49239 8.79998 3.96708 8.48479 3.80948 8.012C3.65189 7.48669 3.96708 6.96137 4.43986 6.80378C8.1696 5.70062 14.3158 5.91074 18.2031 8.22213C18.6759 8.48479 18.8335 9.11517 18.5708 9.58795C18.3082 9.95567 17.6778 10.1133 17.205 9.85061Z' fill='%2310BC4C'/%3E%3C/svg%3E%0A");
			content: "";
			display: inline-block;
			height: 20px;
			margin-right: em(9);
			position: relative;
			width: 20px;
		}

		&:hover, &:focus, &:active {
			border-color: $chathams-blue !important;
		}
	}

	// Podbean icon before button text
	&--podbean {
		border: 1px solid $alto;
		color: $chathams-blue !important;
		text-transform: none;
		transition: border-color .15s ease-in-out;

		&::before {
			background-image: url("data:image/svg+xml,%3Csvg width='20' height='18' viewBox='0 0 20 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.5542 0.124432C12.2251 0.300659 12.9116 0.4539 13.5746 0.653114C15.7745 1.31971 17.6233 2.51499 19.0899 4.26194C19.3005 4.51479 19.519 4.76763 19.7062 5.03581C20.1586 5.67942 20.0806 6.41498 19.5268 6.77509C18.9417 7.16586 18.1928 6.99729 17.7325 6.33069C17.0071 5.30398 16.0787 4.43817 15.0022 3.77923C13.7541 3.00536 12.3967 2.62226 10.9379 2.4537C9.37772 2.27747 7.89554 2.51499 6.45237 3.07432C4.78297 3.71027 3.34759 4.82893 2.32567 6.2694C2.09944 6.58354 1.85761 6.86704 1.45976 6.92833C0.976103 7.00495 0.508046 6.92833 0.219411 6.48394C-0.0692243 6.03954 -0.0848259 5.53384 0.242814 5.12775C0.851288 4.37687 1.45976 3.61066 2.17745 2.97471C3.59722 1.71048 5.29003 0.898299 7.14665 0.430914C7.58351 0.315983 8.02816 0.208714 8.47281 0.101445C9.95499 0.00183849 10.267 -0.0747818 11.5542 0.124432Z' fill='%23BBDD8D'/%3E%3Cpath d='M9.76775 10.2154C11.952 10.1541 13.754 12.0313 13.7072 14.1537C13.6604 16.2914 11.8896 18 9.71314 18C7.44307 17.9924 5.74246 16.0539 5.76586 14.0387C5.78927 12.1615 7.41966 10.1311 9.76775 10.2154Z' fill='%238EC642'/%3E%3Cpath d='M9.70424 12.0696C8.51849 12.0772 7.66039 12.9507 7.67599 14.123C7.69159 15.2493 8.62771 16.1534 9.77445 16.1458C10.9056 16.1305 11.8339 15.1804 11.8183 14.0617C11.8027 12.9201 10.89 12.0619 9.70424 12.0696Z' fill='white'/%3E%3Cpath d='M3.44922 7.88611C3.54283 7.6256 3.58964 7.33445 3.75346 7.11225C6.65541 3.12032 12.826 2.94409 15.8371 7.12757C16.4222 7.94741 16.0477 8.88218 15.0882 9.01244C14.5578 9.08906 14.1599 8.82855 13.8479 8.39947C13.1458 7.44938 12.2253 6.79044 11.0473 6.50694C9.15951 6.04722 7.09226 6.66785 5.90652 8.18493C5.7427 8.39181 5.57888 8.60635 5.36825 8.76725C4.99381 9.05075 4.57255 9.1197 4.1201 8.90517C3.69105 8.70595 3.48042 8.37648 3.44922 7.88611Z' fill='%23A5D167'/%3E%3C/svg%3E%0A");
			content: "";
			display: inline-block;
			height: 18px;
			margin-right: em(9);
			position: relative;
			width: 20px;
		}

		&:hover, &:focus, &:active {
			border-color: $chathams-blue !important;
		}
	}

	// Google Podcasts icon before button text
	&--google-podcasts {
		border: 1px solid $alto;
		color: $chathams-blue !important;
		text-transform: none;
		transition: border-color .15s ease-in-out;

		&::before {
			background-image: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.57331 11.1695V12.7886C3.57331 13.6061 2.9088 14.2706 2.09134 14.2706C1.27389 14.2706 0.609375 13.6061 0.609375 12.7833V11.1643C0.609375 10.3468 1.27389 9.68229 2.09134 9.68229C2.9088 9.68229 3.57331 10.3468 3.57331 11.1695Z' fill='%230066D9'/%3E%3Cpath d='M21.3773 11.212C21.3773 11.1962 21.3773 11.1856 21.3773 11.1698C21.3773 10.3524 22.0418 9.68785 22.8592 9.68785C23.6767 9.68785 24.3412 10.3524 24.3412 11.1698C24.3412 11.1856 24.3412 11.1962 24.3412 11.212V12.8311C24.3201 13.6327 23.6661 14.2709 22.8592 14.2709C22.0523 14.2709 21.3984 13.6275 21.3773 12.8311V11.212Z' fill='%234285F4'/%3E%3Cpath d='M8.69807 16.0213V17.6404C8.69807 18.4579 8.03356 19.1224 7.2161 19.1224C6.39865 19.1224 5.73413 18.4579 5.73413 17.6404V16.0213C5.73413 15.2039 6.39865 14.5394 7.2161 14.5394C8.03356 14.5394 8.69807 15.2039 8.69807 16.0213ZM8.69807 6.31206V11.6071C8.68225 12.4087 8.02301 13.0574 7.2161 13.0574C6.4092 13.0574 5.74996 12.414 5.73413 11.6071V6.31206C5.73413 5.4946 6.39865 4.83009 7.2161 4.83009C8.03356 4.83009 8.69807 5.4946 8.69807 6.31206Z' fill='%23EA4335'/%3E%3Cpath d='M16.2525 7.93115C16.2525 8.7486 16.917 9.41311 17.7345 9.41311C18.5519 9.41311 19.2164 8.7486 19.2164 7.93115V6.31206C19.2164 5.4946 18.5519 4.83009 17.7345 4.83009C16.917 4.83009 16.2525 5.4946 16.2525 6.31206V7.93115Z' fill='%2334A853'/%3E%3Cpath d='M10.9942 3.21105C10.9942 4.0285 11.6588 4.69302 12.4762 4.69302C13.2937 4.69302 13.9582 4.0285 13.9582 3.21105V1.59195C13.9582 0.774498 13.2937 0.109985 12.4762 0.109985C11.6588 0.109985 10.9942 0.774498 10.9942 1.59195V3.21105ZM10.9942 20.7415C10.9942 19.9241 11.6588 19.2596 12.4762 19.2596C13.2937 19.2596 13.9582 19.9241 13.9582 20.7415V22.3606C13.9582 23.1781 13.2937 23.8426 12.4762 23.8426C11.6588 23.8426 10.9942 23.1781 10.9942 22.3606V20.7415Z' fill='%23FAB908'/%3E%3Cpath d='M16.2525 12.3824C16.2525 11.565 16.917 10.9004 17.7345 10.9004C18.5519 10.9004 19.2164 11.565 19.2164 12.3824V17.6405C19.2164 18.458 18.5519 19.1225 17.7345 19.1225C16.917 19.1225 16.2525 18.458 16.2525 17.6405V12.3824Z' fill='%2334A853'/%3E%3Cpath d='M13.9582 7.66277V16.2909C13.9582 17.1084 13.2937 17.7729 12.4762 17.7729C11.6588 17.7729 10.9942 17.1084 10.9942 16.2909V7.66277C10.9942 6.84532 11.6588 6.1808 12.4762 6.1808C13.2937 6.1808 13.9582 6.84004 13.9582 7.66277Z' fill='%23FAB908'/%3E%3C/svg%3E%0A");
			content: "";
			display: inline-block;
			height: 24px;
			margin-right: em(9);
			position: relative;
			width: 25px;
		}

		&:hover, &:focus, &:active {
			border-color: $chathams-blue !important;
		}
	}

	// Apple iTunes icon before button text
	&--apple-itunes {
		border: 1px solid $alto;
		color: $chathams-blue !important;
		text-transform: none;
		transition: border-color .15s ease-in-out;

		&::before {
			background-image: url("data:image/svg+xml,%3Csvg width='21' height='22' viewBox='0 0 21 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.2588 13.9988C13.2212 13.66 13.1083 13.4153 12.8827 13.1894C12.4596 12.7471 11.7168 12.4554 10.8423 12.4554C9.96792 12.4554 9.22513 12.7377 8.80202 13.1894C8.58577 13.4247 8.46354 13.66 8.42593 13.9988C8.35071 14.6575 8.39772 15.2222 8.47294 16.135C8.54816 17.0008 8.6892 18.1583 8.86784 19.3253C8.99947 20.1628 9.1029 20.6145 9.19693 20.9345C9.35677 21.4615 9.93031 21.9132 10.8423 21.9132C11.7544 21.9132 12.3373 21.4521 12.4878 20.9345C12.5818 20.6145 12.6852 20.1628 12.8168 19.3253C12.9955 18.1489 13.1365 17.0008 13.2118 16.135C13.2964 15.2222 13.334 14.6575 13.2588 13.9988Z' fill='url(%23paint0_linear_2013_4749)'/%3E%3Cpath d='M13.1539 9.19901C13.1539 10.4789 12.1197 11.5141 10.8409 11.5141C9.56221 11.5141 8.52795 10.4789 8.52795 9.19901C8.52795 7.91915 9.56221 6.88397 10.8409 6.88397C12.1197 6.88397 13.1539 7.92856 13.1539 9.19901Z' fill='url(%23paint1_linear_2013_4749)'/%3E%3Cpath d='M10.814 0.080032C5.41706 0.0988535 0.997934 4.48427 0.922715 9.88604C0.8663 14.262 3.65881 18.0075 7.56081 19.3815C7.65483 19.4191 7.74886 19.3344 7.73945 19.2403C7.69244 18.9015 7.63603 18.5628 7.59842 18.224C7.57961 18.1016 7.50439 18.0075 7.40097 17.9511C4.31698 16.6053 2.16383 13.5092 2.20144 9.92368C2.24845 5.21831 6.08463 1.38813 10.7764 1.35048C15.5811 1.31284 19.5019 5.20889 19.5019 10.0084C19.5019 13.5562 17.3581 16.6053 14.3023 17.9511C14.1895 17.9981 14.1143 18.1016 14.1049 18.224C14.0579 18.5628 14.0109 18.9015 13.9638 19.2403C13.945 19.3439 14.0485 19.4191 14.1425 19.3815C18.0069 18.0263 20.7806 14.3373 20.7806 10.0084C20.7618 4.53132 16.2956 0.0706212 10.814 0.080032Z' fill='url(%23paint2_linear_2013_4749)'/%3E%3Cpath d='M10.5871 3.37371C7.07058 3.50547 4.24986 6.41339 4.20285 9.93301C4.17464 12.2481 5.33114 14.2996 7.10819 15.5136C7.19281 15.5701 7.31504 15.5042 7.31504 15.4007C7.28684 14.996 7.28684 14.6384 7.30564 14.262C7.31504 14.1396 7.26803 14.0267 7.17401 13.942C6.09273 12.9256 5.43456 11.4764 5.47217 9.87655C5.54739 7.05332 7.81337 4.7571 10.6341 4.64417C13.6993 4.52183 16.2191 6.98745 16.2191 10.0177C16.2191 11.5611 15.5609 12.9539 14.5173 13.942C14.4327 14.0267 14.3856 14.1396 14.3856 14.262C14.4045 14.629 14.3951 14.9866 14.3762 15.3913C14.3668 15.4948 14.4891 15.5701 14.5831 15.5042C16.3319 14.309 17.4884 12.2857 17.4884 10.0083C17.4978 6.26282 14.3668 3.22314 10.5871 3.37371Z' fill='url(%23paint3_linear_2013_4749)'/%3E%3Cdefs%3E%3ClinearGradient id='paint0_linear_2013_4749' x1='10.8512' y1='0.0800171' x2='10.8512' y2='21.9132' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23F452FF'/%3E%3Cstop offset='1' stop-color='%23832BC1'/%3E%3C/linearGradient%3E%3ClinearGradient id='paint1_linear_2013_4749' x1='10.8512' y1='0.0800171' x2='10.8512' y2='21.9132' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23F452FF'/%3E%3Cstop offset='1' stop-color='%23832BC1'/%3E%3C/linearGradient%3E%3ClinearGradient id='paint2_linear_2013_4749' x1='10.8512' y1='0.0800171' x2='10.8512' y2='21.9132' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23F452FF'/%3E%3Cstop offset='1' stop-color='%23832BC1'/%3E%3C/linearGradient%3E%3ClinearGradient id='paint3_linear_2013_4749' x1='10.8512' y1='0.0800171' x2='10.8512' y2='21.9132' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23F452FF'/%3E%3Cstop offset='1' stop-color='%23832BC1'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E%0A");
			content: "";
			display: inline-block;
			height: 22px;
			margin-right: em(9);
			position: relative;
			width: 21px;
		}

		&:hover, &:focus, &:active {
			border-color: $chathams-blue !important;
		}
	}

	// Apple iTunes icon before button text
	&--youtube {
		border: 1px solid $alto;
		color: $chathams-blue !important;
		text-transform: none;
		transition: border-color .15s ease-in-out;

		&::before {
			background-image: url("data:image/svg+xml,%3Csvg width='24' height='16' viewBox='0 0 24 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M21.2066 0.476364C22.1896 0.741818 22.9621 1.51636 23.2233 2.49818C23.6984 4.28 23.6984 8 23.6984 8C23.6984 8 23.6984 11.72 23.2233 13.5018C22.9585 14.4873 22.1859 15.2618 21.2066 15.5236C19.4293 16 12.2984 16 12.2984 16C12.2984 16 5.17117 16 3.39026 15.5236C2.40731 15.2582 1.63474 14.4836 1.37359 13.5018C0.898438 11.72 0.898438 8 0.898438 8C0.898438 8 0.898438 4.28 1.37359 2.49818C1.63837 1.51273 2.41094 0.738182 3.39026 0.476364C5.17117 0 12.2984 0 12.2984 0C12.2984 0 19.4293 0 21.2066 0.476364ZM15.9437 8L10.0206 11.4291V4.57091L15.9437 8Z' fill='%23FF3000'/%3E%3C/svg%3E%0A");
			content: "";
			display: inline-block;
			height: 16px;
			margin-right: em(9);
			position: relative;
			width: 24px;
		}

		&:hover, &:focus, &:active {
			border-color: $chathams-blue !important;
		}
	}

	// Apple iTunes icon before button text
	&--facebook {
		border: 1px solid $alto;
		color: $chathams-blue !important;
		text-transform: none;
		transition: border-color .15s ease-in-out;

		&::before {
			background-image: url("data:image/svg+xml,%3Csvg width='13' height='22' viewBox='0 0 13 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.8308 12.343L12.4434 8.45379H8.66941V5.92581C8.66941 4.86235 9.1963 3.82321 10.8811 3.82321H12.6211V0.511306C11.6078 0.349868 10.584 0.262531 9.55778 0.25C6.45157 0.25 4.42365 2.1156 4.42365 5.48827V8.45379H0.980469V12.343H4.42365V21.75H8.66941V12.343H11.8308Z' fill='%23337FFF'/%3E%3C/svg%3E");
			content: "";
			display: inline-block;
			height: 22px;
			margin-right: em(9);
			position: relative;
			width: 13px;
		}

		&:hover, &:focus, &:active {
			border-color: $chathams-blue !important;
		}
	}

	// Load more icon before button text
	&--load-more {
		// border: 0;
		position: relative;
		transition: width .5s ease;

		svg {
			margin-right: em(9);
			opacity: 0;
			position: absolute;
			transition: opacity .5s ease;
			visibility: hidden;
			width: 20px;
		}

		&.loading {
			pointer-events: none;

			svg {
				animation: spin 1s linear infinite;
				opacity: 1;
				position: static;
				transition: opacity .5s ease;
				visibility: visible;
			}
		}
	}

	// Larger button
	&--large {
		border-radius: em(30);
		font-size: 16px;
		padding: em(9, 15) em(20, 15);


		&:hover,
		&:focus {
			border-color: $alto;
		}
	}
}

// Remove padding and background from nested link automatically added to Gutenberg button
.wp-block-button.btn {
	a {
		background-color: transparent!important;
		padding: 0;
	}
}

// Aply correct colors to icon buttons
.btn--dot.btn-primary {
	color: $white;

	&::before {
		background-color: rgba( $white, .6);
	}
}

.btn--dot.btn-secondary {
	&::before {
		background-color: $alto;
	}
}

.btn--dot.btn-green {
	&::before {
		background-color: $white;
	}
}

.btn--dot.btn-white, .btn--dot.btn-custom {
	&::before {
		background-color: $chathams-blue;
	}
}

.btn--ticket.btn-secondary, .btn--ticket.btn-white, .btn--ticket.btn-custom, .btn--download.btn-secondary {
	&::before {
		background-color: $galliano;
	}
}

// Hover effects
.btn-secondary {
	a, span {
		transition: color .15s ease-in-out,
			background-color .15s ease-in-out,
			border-color .15s ease-in-out,
			box-shadow .15s ease-in-out,
			-webkit-box-shadow .15s ease-in-out;
	}

	&:hover, &:focus, &:active {
		background-color: #fff !important;
		border-color: $alto !important;
		color: $chathams-blue !important;

		a {
			color: $chathams-blue !important;
		}
	}

	&.btn--dot {
		&:hover, &:focus, &:active {
			border-color: $chathams-blue !important;
		}
	}
}

.btn-white {
	a, span {
		transition: color .15s ease-in-out,
			background-color .15s ease-in-out,
			border-color .15s ease-in-out,
			box-shadow .15s ease-in-out,
			-webkit-box-shadow .15s ease-in-out;
	}

	&:hover, &:focus, &:active {
		background-color: transparent !important;
		border-color: #fff !important;
		color: #fff !important;

		a {
			color: #fff !important;
		}
	}

	&.btn--dot {
		&:hover, &:focus, &:active {
			border-color: #fff !important;
		}
	}
}

.btn-green {
	a, span {
		transition: color .15s ease-in-out,
			background-color .15s ease-in-out,
			border-color .15s ease-in-out,
			box-shadow .15s ease-in-out,
			-webkit-box-shadow .15s ease-in-out;
	}

	&:hover, &:focus, &:active {
		background-color: #fff !important;
		border-color: $green !important;
		color: $green !important;

		a {
			color: $green !important;
		}
		
		&::before {
			background-color: $alto !important;
		}
	}

	&.btn--dot {
		&:hover, &:focus, &:active {
			border-color: $green !important;
		}
	}
}