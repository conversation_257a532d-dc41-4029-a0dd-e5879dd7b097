<?php

/**
 * The template for displaying events from <PERSON><PERSON><PERSON>.
 */

$context = Timber::get_context();

$context['title'] = __('Inbjudna Hästar', 'solvalla');
$context['posts'] = new Timber\PostQuery();

foreach ($context['posts'] as $key => $value) {
    $context['posts'][$key]->fields = [];
    foreach (get_field_objects($post->ID) as $field_key => $field_array) {
        foreach ($field_array as $field_param_name => $field_param_value) {
            if ($field_param_name == 'label' || $field_param_name == 'value' || $field_param_name == 'name') {
                $context['posts'][$key]->fields[$field_key][$field_param_name] = $field_param_value;
            }
        }
    }
}


Timber::render('archive-invited-horses.twig', $context);
