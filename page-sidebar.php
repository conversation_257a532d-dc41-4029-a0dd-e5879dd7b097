<?php
/**
 * Template Name: Page with sidebar
 * Description: A Page Template with the sidebar on the right side.
 */

$context = Timber::get_context();
$post = new TimberPost();

$context['post'] = $post;

// Add page ancestors for breadcrumbs
$ancestors = get_post_ancestors( $post->ID );
$breadcrumb_pages = [];
if ( $ancestors ) {
    // Reverse the array to get correct order (top parent first)
    $ancestors = array_reverse( $ancestors );
    foreach ( $ancestors as $ancestor_id ) {
        $ancestor_post = new TimberPost( $ancestor_id );
        $breadcrumb_pages[] = $ancestor_post;
    }
}
$context['breadcrumb_pages'] = $breadcrumb_pages;

$context['dynamic_sidebar'] = Timber::get_widgets('sidebar-1');

Timber::render( array( 'page-' . $post->post_name . '.twig', 'page-sidebar.twig' ), $context );