<?php

/**
 * The template for displaying events from <PERSON><PERSON><PERSON>.
 */

$context = Timber::get_context();

global $wp;

$paged = (get_query_var('paged')) ? get_query_var('paged') : '';

$queried_object = get_queried_object();

if (!$queried_object->name) {
    Timber::render('404.twig', $context);
}

$context['title'] = __('Vinnare', 'solvalla') . ' – ' . $queried_object->name;

if ($paged) {
    $context['title'] = $context['title'] . ' – ' . __('Sida', 'solvalla') . ' ' . $paged;
}

// Archive url for breadcrumbs
$context['which_event_archive'] = true; 
$context['archive_title'] = __( 'Vinnare', 'solvalla' );
$context['archive_url'] = get_site_url() . '/vinnare/';

$context['posts'] = new Timber\PostQuery();

Timber::render('archive-winners.twig', $context);
