<?php

/*
 *
 * Taxonomies
 *
 */

// Same as with Custom Types, you only need the arguments and register_taxonomy function here. They are hooked into WordPress in functions.php.
register_taxonomy('groupped', ['banners'], [
    'labels'            => [
        'name'              => _x('Groups', 'taxonomy general name', 'solvalla'),
        'singular_name'     => _x('Group', 'taxonomy singular name', 'solvalla'),
        'search_items'      => __('Search Groups', 'solvalla'),
        'all_items'         => __('All Groups', 'solvalla'),
        'parent_item'       => __('Parent Group', 'solvalla'),
        'parent_item_colon' => __('Parent Group:', 'solvalla'),
        'edit_item'         => __('Edit Group', 'solvalla'),
        'update_item'       => __('Update Group', 'solvalla'),
        'add_new_item'      => __('Add New Group', 'solvalla'),
        'new_item_name'     => __('New Group Name', 'solvalla'),
        'menu_name'         => __('Groups', 'solvalla'),
    ],
    'hierarchical'      => true,
    'public'            => true,
    'show_ui'           => true,
    'show_admin_column' => true,
    'show_in_nav_menus' => true,
    'show_tagcloud'     => false,
    'rewrite'           => array('slug' => 'groupped'),
]);


// invited horses category (elitkampen, elitloppet)
register_taxonomy('which-event-invited-horses', ['invited-horses'], [
    'labels'            => [
        'name'              => _x('WhichEvent', 'taxonomy general name', 'solvalla'),
        'singular_name'     => _x('WhichEvent', 'taxonomy singular name', 'solvalla'),
        'search_items'      => __('Search WhichEvent', 'solvalla'),
        'all_items'         => __('All WhichEvent', 'solvalla'),
        'parent_item'       => __('Parent WhichEvent', 'solvalla'),
        'parent_item_colon' => __('Parent WhichEvent:', 'solvalla'),
        'edit_item'         => __('Edit WhichEvent', 'solvalla'),
        'update_item'       => __('Update WhichEvent', 'solvalla'),
        'add_new_item'      => __('Add New WhichEvent', 'solvalla'),
        'new_item_name'     => __('New WhichEvent Name', 'solvalla'),
        'menu_name'         => __('WhichEvent', 'solvalla'),
    ],
    'hierarchical'      => true,
    'public'            => true,
    'show_ui'           => true,
    'show_admin_column' => true,
    'show_in_rest'      => true, // add support for Gutenberg editor
    'show_in_nav_menus' => true,
    'show_tagcloud'     => false,
    'rewrite'           => [
        'slug' => 'inbjudna-hastar', // Set the desired front base
        'with_front' => false, // Set to true or false as needed
        'hierarchical' => false // Adjust if needed
    ]
]);

// invited horses category (elitkampen, elitloppet)
register_taxonomy('which-event-winners', ['winners'], [
    'labels'            => [
        'name'              => _x('WhichEvent', 'taxonomy general name', 'solvalla'),
        'singular_name'     => _x('WhichEvent', 'taxonomy singular name', 'solvalla'),
        'search_items'      => __('Search WhichEvent', 'solvalla'),
        'all_items'         => __('All WhichEvent', 'solvalla'),
        'parent_item'       => __('Parent WhichEvent', 'solvalla'),
        'parent_item_colon' => __('Parent WhichEvent:', 'solvalla'),
        'edit_item'         => __('Edit WhichEvent', 'solvalla'),
        'update_item'       => __('Update WhichEvent', 'solvalla'),
        'add_new_item'      => __('Add New WhichEvent', 'solvalla'),
        'new_item_name'     => __('New WhichEvent Name', 'solvalla'),
        'menu_name'         => __('WhichEvent', 'solvalla'),
    ],
    'hierarchical'      => true,
    'public'            => true,
    'show_ui'           => true,
    'show_admin_column' => true,
    'show_in_rest'      => true, // add support for Gutenberg editor
    'show_in_nav_menus' => true,
    'show_tagcloud'     => false,
    'rewrite'           => [
        'slug' => 'vinnare', // Set the desired front base
        'with_front' => false, // Set to true or false as needed
        'hierarchical' => false // Adjust if needed
    ]
]);


// custom slug rewrites:
add_filter('register_taxonomy_args', function ($args, $taxonomy) {

    if ('which-event-invited-horses' === $taxonomy) {
        $args['rewrite'] = [
            'slug' => 'inbjudna-hastar/%we-ih%', // Set the desired front base
            'with_front' => false, // Set to true or false as needed
            'hierarchical' => false // Adjust if needed
        ];
        return $args;
    }

    if ('which-event-winners' === $taxonomy) {
        $args['rewrite'] = [
            'slug' => 'vinnare/%we-w%', // Set the desired front base
            'with_front' => false, // Set to true or false as needed
            'hierarchical' => false // Adjust if needed
        ];
        return $args;
    }


    return $args;
}, 10, 2);
