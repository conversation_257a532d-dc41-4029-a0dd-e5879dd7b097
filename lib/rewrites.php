<?php

/*
 * THIS FILE CONTAINS REWRITE RULES which are ***CRITICAL*** to the proper functioning of the site.
 *
 */

// custom slug rewrites
add_filter('register_post_type_args', function ($args, $post_type) {
	if ('invited-horses' === $post_type) {
		$args['rewrite'] = [
			'slug' => 'inbjudna-hastar/%we-ih%',
			'with_front' => false
		];
	}

	if ('winners' === $post_type) {
		$args['rewrite'] = [
			'slug' => 'vinnare/%we-w%',
			'with_front' => false
		];
	}

	return $args;
}, 10, 2);


// Make sure post links work properly (rewrite them)
add_filter('post_type_link', function ($post_link, $wp_post = 0) {
	if (!is_object($wp_post)) {
		$wp_post = get_post($wp_post);
	}
	if ('winners' === $wp_post->post_type) {
		$winners_terms = wp_get_object_terms($wp_post->ID, 'which-event-winners');
		if ($winners_terms) {
			return str_replace('%we-w%', $winners_terms[0]->slug, $post_link);
		}
	}

	if ('invited-horses' === $wp_post->post_type) {
		$invited_horses_terms = wp_get_object_terms($wp_post->ID, 'which-event-invited-horses');
		if ($invited_horses_terms) {
			return str_replace('%we-ih%', $invited_horses_terms[0]->slug, $post_link);
		}
	}

	return $post_link;
}, 1, 3);

// Make sure term links work properly (rewrite them)
add_filter('term_link', function ($term_link, $term = 0) {

	if (!is_object($term)) {
		$term = get_term($term);
	}
	if ('which-event-winners' === $term->taxonomy) {
		if ($term->slug) {
			return str_replace('%we-w%', $term->slug, $term_link);
		}
	}

	if ('which-event-invited-horses' === $term->taxonomy) {
		if ($term->slug) {
			return str_replace('%we-ih%', $term->slug, $term_link);
		}
	}

	return $term_link;
}, 10, 2);

//template fix for taxonomy which-event-winners so it uses the file taxonomy-which-event-winners.php
add_filter('template_include', function ($template) {
	if (get_query_var('post_type') === 'winners' && get_query_var('taxonomy') === 'which-event-winners') {
		return get_stylesheet_directory() . '/taxonomy-which-event-winners.php';
	} elseif (get_query_var('post_type') === 'invited-horses' && get_query_var('taxonomy') === 'which-event-invited-horses') {
		return get_stylesheet_directory() . '/taxonomy-which-event-invited-horses.php';
	}
	return $template;
});

// invited horses rewrites
add_rewrite_rule(
	'^inbjudna-hastar/page/(\d+)/?$',
	'index.php?post_type=invited-horses&paged=$matches[1]',
	'top'
);
add_rewrite_rule(
	'^inbjudna-hastar/(?!page/\d+/)(.*)/page/(.*)/?$',
	'index.php?post_type=invited-horses&taxonomy=which-event-invited-horses&term=$matches[1]&paged=$matches[2]',
	'top'
);
add_rewrite_rule(
	'^inbjudna-hastar/(?!page/\d+/)(.*)/(.*)/?$',
	'index.php?post_type=invited-horses&which-event-invited-horses=$matches[1]&name=$matches[2]',
	'top'
);
add_rewrite_rule(
	'^inbjudna-hastar/(?!page/\d+/)(.*)/?$',
	'index.php?post_type=invited-horses&taxonomy=which-event-invited-horses&term=$matches[1]',
	'top'
);
add_rewrite_rule(
	'^inbjudna-hastar/?$',
	'index.php?post_type=invited-horses',
	'top'
);

// winners rewrites, order is important
add_rewrite_rule(
	'^vinnare/page/(\d+)/?$',
	'index.php?post_type=winners&paged=$matches[1]',
	'top'
);
add_rewrite_rule(
	'^vinnare/(?!page/\d+/)(.*)/page/(.*)/?$',
	'index.php?post_type=winners&taxonomy=which-event-winners&term=$matches[1]&paged=$matches[2]',
	'top'
);
add_rewrite_rule(
	'^vinnare/(?!page/\d+/)(.*)/(.*)/?$',
	'index.php?post_type=winners&which-event-winners=$matches[1]&name=$matches[2]',
	'top'
);
add_rewrite_rule(
	'^vinnare/(?!page/\d+/)(.*)/?$',
	'index.php?post_type=winners&taxonomy=which-event-winners&term=$matches[1]',
	'top'
);
add_rewrite_rule(
	'^vinnare/?$',
	'index.php?post_type=winners',
	'top'
);
// flush_rewrite_rules(); // use only once
