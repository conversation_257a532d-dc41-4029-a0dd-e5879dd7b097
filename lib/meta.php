<?php

/** 
 * Banners.
 */
// Clicks count
register_post_meta('banners', 'click_count', array(
  'type' => 'integer',
  'single' => true,
  'show_in_rest' => true, // Enable REST API support
  'default' => 0 // Set the default value to 0
));

// views count
register_post_meta('banners', 'view_count', array(
  'type' => 'integer',
  'single' => true,
  'show_in_rest' => true, // Enable REST API support
  'default' => 0 // Set the default value to 0
));

function add_banner_columns($columns) {
  $date_col = $columns['date'];
  unset($columns['date']);
  $columns['click_count'] = __('Click Count', 'solvalla');
  $columns['view_count'] = __('View Count', 'solvalla');
  $columns['date'] = $date_col;
  return $columns;
}
add_filter('manage_banners_posts_columns', 'add_banner_columns');

function display_banner_column($column, $post_id) {
  if ($column === 'click_count') {
    $click_count = get_post_meta($post_id, 'click_count', true);
    echo $click_count;
  }
  if ($column === 'view_count') {
    $view_count = get_post_meta($post_id, 'view_count', true);
    echo $view_count;
  }
}
add_action('manage_banners_posts_custom_column', 'display_banner_column', 10, 2);

function update_click_count() {
  $post_id = intval($_POST['post_id']);
  $click_count = get_post_meta($post_id, 'click_count', true);
  $click_count = intval($click_count);
  update_post_meta($post_id, 'click_count', $click_count + 1);
  wp_send_json_success(array('click_count' => $click_count + 1));
}
add_action('wp_ajax_update_click_count', 'update_click_count');
add_action('wp_ajax_nopriv_update_click_count', 'update_click_count');

function update_view_count() {
  $post_id = intval($_POST['post_id']);
  $view_count = get_post_meta($post_id, 'view_count', true);
  $view_count = intval($view_count);
  update_post_meta($post_id, 'view_count', $view_count + 1);
  wp_send_json_success(array('view_count' => $view_count + 1));
}
add_action('wp_ajax_update_view_count', 'update_view_count');
add_action('wp_ajax_nopriv_update_view_count', 'update_view_count');



/** 
 * Programs.
 */
function add_program_date_column($columns) {
  $date_col = $columns['date'];
  unset($columns['date']);
  $columns['event_date'] = __('Event Date', 'solvalla');
  $columns['date'] = $date_col;
  return $columns;
}
add_filter('manage_programs_posts_columns', 'add_program_date_column');

function display_program_date_column($column, $post_id) {
  if ($column === 'event_date') {
    $event_date = get_field('date', $post_id);
    echo date('d F Y', strtotime($event_date));
  }
}
add_action('manage_programs_posts_custom_column', 'display_program_date_column', 10, 2);

function program_date_sortable_columns($columns) {
  $columns['event_date'] = 'event_date';
  return $columns;
}
add_filter('manage_edit-programs_sortable_columns', 'program_date_sortable_columns');

function program_date_orderby($query) {
  if (!is_admin() || !$query->is_main_query()) {
    return;
  }

  if ($query->get('post_type') == 'programs' && $query->get('orderby') === '') {
    $query->set('orderby', 'meta_value');
    $query->set('order', 'desc');
    $query->set('meta_key', 'date');
    $query->set('meta_type', 'datetime');
  }

  if ($query->get('post_type') == 'programs' && $query->get('orderby') === 'event_date') {
    $query->set('orderby', 'meta_value');
    $query->set('meta_key', 'date');
    $query->set('meta_type', 'datetime');
  }
}
add_action('pre_get_posts', 'program_date_orderby');

/** 
 * Trainers.
 */
function trainers_title_orderby($query) {
  if (!is_admin() || !$query->is_main_query()) {
    return;
  }

  if ($query->get('post_type') == 'trainers' && $query->get('orderby') === '') {
    $query->set('orderby', 'title');
    $query->set('order', 'asc');
  }
}
add_action('pre_get_posts', 'trainers_title_orderby');
