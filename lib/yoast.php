<?php

add_filter('wpseo_canonical', 'solvalla_filter_canonical');
/**
 * Filter the canonical URL so that paged custom queries include the page number in the canonical URL
 * Example: https://example.com/videos/page/2
 *
 * @param $canonical
 * @return string
 */
function solvalla_filter_canonical($canonical) {

    global $wp;
    if (is_post_type_archive()) {
        // global $wp;
        // home_url($wp->request)
        $canonical = get_post_type_archive_link(get_post_type());
        $canonical = strtok($canonical, '?'); // Remove GET variables
    }
    if (is_paged()) {
        global $paged;
        $pageNumLink = get_pagenum_link($paged);
        $canonical = strtok($pageNumLink, '?'); // Remove GET variables
    }

    if (str_contains(home_url($wp->request), 'nyheter')) {
        $canonical = home_url($wp->request) . '/';
    }
    if (str_contains($canonical, '%we-w%')) {
        $canonical = str_replace('%we-w%/', '', $canonical);
    }
    if (str_contains($canonical, '%we-ih%')) {
        $canonical = str_replace('%we-ih%/', '', $canonical);
    }
    return $canonical;
}

add_filter('wpseo_adjacent_rel_url', 'solvalla_filter_nex_prev_rel_meta', 10, 3);
/**
 * Filter the adjacent URL so that paged custom queries include the page number in the adjacent URL
 * Example: https://example.com/videos/page/2
 *
 * @param $canonical
 * @return string
 */
function solvalla_filter_nex_prev_rel_meta($canonical, $next_or_prev) {

    // global $wp;
    if (is_paged()) {
        global $paged;
        if ('next' === $next_or_prev && get_next_posts_link()) { // && check if there is a next page
            $next_link = get_pagenum_link($paged + 1);
            $canonical = strtok($next_link, '?'); // Remove GET variables
        } elseif ('prev' === $next_or_prev) {
            $prev_link = get_pagenum_link($paged - 1);
            $canonical = strtok($prev_link, '?'); // Remove GET variables
        }
    }

    if (str_contains($canonical, '%we-w%')) {
        $canonical = str_replace('%we-w%/', '', $canonical);
        // echo '<script>console.log(' . json_encode($canonical) . ')</script>';
    }
    if (str_contains($canonical, '%we-ih%')) {
        $canonical = str_replace('%we-ih%/', '', $canonical);
    }
    return $canonical;
}

add_filter('wpseo_title', 'solvalla_filter_wpseo_title', 10, 3);

function solvalla_filter_wpseo_title($title) {

    $queried_object = get_queried_object();

    if (is_tax('which-event-invited-horses')) {
        $title = 'Inbjudna hästar ' . $queried_object->name;
    } elseif (is_tax('which-event-winners')) {
        $title = 'Vinnare ' . $queried_object->name;
    }
    return $title;
}
