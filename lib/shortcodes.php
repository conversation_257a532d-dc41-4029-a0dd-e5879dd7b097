<?php

function random_cpt_post_shortcode($atts)
{
    $atts = shortcode_atts([
        'taxonomy'          => 'groupped',
        'term_id'           => '7',
        'align'             => 'center',
    ], $atts);

    $banners_group = get_posts([
        'post_type'      => 'banners',
        'tax_query'      => [
            [
                'taxonomy'  => $atts['taxonomy'],
                'field'     => 'term_id',
                'terms'     => $atts['term_id'],
            ],
        ],
        'orderby'        => 'rand',
        'posts_per_page' => 1,
        'fields'         => 'ids',
    ]);
    if ($banners_group)
    {
        $banners_group_id = $banners_group[0];
    }
    else
    {
        $banners_group_id = false;
    }

    $output = '';
    if ($banners_group_id)
    {
        $background_image           = ($banners_group_id && $banners_group_id != '') ? get_field('background_image', $banners_group_id) : '';
        $banner_link                = ($banners_group_id && $banners_group_id != '') ? get_field('button', $banners_group_id) : '';
        $background_image_mobile    = ($banners_group_id && $banners_group_id != '') ? get_field('background_image_mobile', $banners_group_id) : '';
        $banner_alignment           = ($atts['align'] && $atts['align'] != '') ? $atts['align'] : '';

        // Check if background image fields exist
        if ($background_image && $background_image_mobile)
        {
            $output .= '<section class="block--banner">';
            $output .= '<div class="container-fluid">';
            $output .= '<div class="row">';
            $output .= '<div class="col-12 d-flex justify-content-' . $banner_alignment . '">';

            // Check if banner link exists
            if ($banner_link)
            {
                $output .= '<a href="' . esc_url($banner_link['url']) . '" class="js-banner" data-banner data-post-id="' . get_the_ID() . '" data-banner-id="' . $banners_group_id . '" target="' . esc_attr($banner_link['target']) . '" title="' . esc_attr($banner_link['title']) . '">';
            }
            $output .= '<img data-lightbox="gallery" width="' . esc_attr($background_image['width']) . '" height="' . esc_attr($background_image['height']) . '" src="" data-src="' . esc_url($background_image_mobile['url']) . '" data-image-md="' . esc_url($background_image['url']) . '" data-mobile-width="' . esc_attr($background_image_mobile['width']) . '" data-mobile-height="' . esc_attr($background_image_mobile['height']) . '" class="block-banner__image img-fluid" alt="banner">';
            if ($banner_link)
            {
                $output .= '</a>';
            }
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</section>';
        }
    }
    return $output;
}
add_shortcode('random_cpt_post', 'random_cpt_post_shortcode');
