<?php

/** 
 * Routes.
 */

// News
Routes::map('nyheter', function () {
    $query = 'posts_per_page=12&post_type=post';
    Routes::load('archive-news.php', null, $query, 200);
});

// News - pagination
Routes::map('nyheter/page/:pg', function ($params) {
    $query = 'posts_per_page=12&post_type=post&paged=' . $params['pg'];
    Routes::load('archive-news.php', $params, $query);
});

// Events
Routes::map('travtavlingar', function () {
    $query = 'post_type=event';
    Routes::load('archive-event.php', null, $query, 200);
});

// Events - pagination
Routes::map('travtavlingar/page/:pg', function ($params) {
    $query = 'paged=' . $params['pg'];
    Routes::load('archive-event.php', $params, $query);
});

// Single racing event
Routes::map('travtavlingar/:event_slug', function ($params) {
    $params['event_type'] = 'travtavlingar';
    $query = 'event_slug=' . $params['event_slug'] . '&event_type=' . $params['event_type'];
    Routes::load('single-event.php', $params, $query);
});

// Single non-racing event
Routes::map('event/:event_slug', function ($params) {
    $params['event_type'] = 'event';
    $query = 'event_slug=' . $params['event_slug'] . '&event_type=' . $params['event_type'];
    Routes::load('single-event.php', $params, $query);
});

// Single podcast episode
Routes::map('podcast/:podcast_slug', function ($params) {
    $query = 'podcast_slug=' . $params['podcast_slug'];
    Routes::load('single-podcast.php', $params, $query);
});

// Feed
Routes::map('nyheter/custom-feed', function ($params) {
    Routes::load('nyheter-rss-feed.php', $params);
});
