<?php

/**
* Endpoint for running banners unpublish cron job
*/
function unpublish_banners_endpoint() {
    register_rest_route(
        'unpublish_banners/v1',
        '/unpublish-all',
        array(
            'methods' => 'GET',
            'callback' => 'unpublish_banners',
            'permission_callback' => '__return_true',
            'show_in_index' => false
        )
    );
}

/**
* Unpublish outdated banners.
*/
function unpublish_banners() {
  
    date_default_timezone_set("Europe/Stockholm"); // we set this to ensure it expires on local time, not server time
    $today = date('Y-m-d H:i:s');
    $posts = get_posts([
        'post_type' => 'banners',
        'post_status' => 'publish',
        'posts_per_page' => -1,
    ]);

    // Count unpublished banners
    $unpublished = 0;

    foreach ($posts as $p) {
        $expiredate = get_field('unpublish_time', $p->ID, false, false); // get the raw date from the db. false, false will convert to Ymd while allowing you to use any date format output choice on the field itself
        if (($expiredate < $today) && ($expiredate != "")) { // if date is less than today, but also not empty to accomodate the coupons that don't expire
            $unpublished++;
            $postdata = array(
                'ID' => $p->ID,
                'post_status' => 'draft'
            );
            wp_update_post($postdata);
        }
    }

    // Purge cache for all pages
    if ($unpublished > 0 && class_exists('Purge_Varnish')) {
        $purge_varnish = new Purge_Varnish();
        $purge_varnish->purge_varnish_all_cache_manually();
    }

    // Message
    if ($unpublished > 0) {
        $message = 'Outdated banners have been sucessfully unpublished.';
    } else {
        $message = 'No banners to unpublish.';
    }

    // Success response
    $response = new WP_REST_Response([
        'status' => 'banners_unpublished',
        'response' => $message
    ]);
    $response->set_status( 200 );

    return $response;
}

add_action( 'rest_api_init', 'unpublish_banners_endpoint' );