<?php

/**
 * Create custom Yoast SEO sitemaps
 */
add_filter('wpseo_sitemap_index', 'solvalla_add_sitemap_events');
add_filter('wpseo_sitemap_index', 'solvalla_add_sitemap_podcasts');
add_action('init', 'init_wpseo_do_sitemap_actions');

// Add events sitemap to sitemap index
function solvalla_add_sitemap_events($sitemaps) {
    // Round to the nearest hour since that's our CRON schedule
    $date = date('c', strtotime(date('Y-m-d H:00:00')));
    
    $smp = '';
    $smp .= '<sitemap>' . "\n";
    $smp .= '<loc>' . site_url() . '/events-sitemap.xml</loc>' . "\n";
    $smp .= '<lastmod>' . htmlspecialchars($date) . '</lastmod>' . "\n";
    $smp .= '</sitemap>' . "\n";

    return $sitemaps . $smp;
}

// Add podcasts sitemap to sitemap index
function solvalla_add_sitemap_podcasts($sitemaps) {  // Added $sitemaps parameter
    $date = date('c');  // Default to current time
    if (class_exists('Libsyn_Podcast_Public')) {
        $latest_episode = Libsyn_Podcast_Public::get_episodes(1, 1);
        if (!empty($latest_episode)) {
            $date = date('c', strtotime($latest_episode[0]->pub_date));
        }
    }

    $smp = '';
    $smp .= '<sitemap>' . "\n";
    $smp .= '<loc>' . site_url() . '/podcasts-sitemap.xml</loc>' . "\n";
    $smp .= '<lastmod>' . htmlspecialchars($date) . '</lastmod>' . "\n";
    $smp .= '</sitemap>' . "\n";

    return $sitemaps . $smp;  // Changed to append to existing sitemaps
}

function init_wpseo_do_sitemap_actions() {
    add_action("wpseo_do_sitemap_podcasts", 'solvalla_generate_origin_combo_sitemap_podcasts');
    add_action("wpseo_do_sitemap_events", 'solvalla_generate_origin_combo_sitemap_events');
}

function solvalla_generate_origin_combo_sitemap_events() {
    global $wpseo_sitemaps;
    $events = Tickster_Events_Public::get_events();
    $output = '';
    if (!empty($events)) {
        $chf = 'hourly';
        $pri = 1.0;
        foreach ($events as $event) {
            // Determine the correct URL based on event tags
            if (is_array($event->tags) && in_array('Event', $event->tags)) {
                $url = site_url() . '/event/' . $event->slug;
            } elseif (is_array($event->tags) && in_array('Racing', $event->tags)) {
                $url = site_url() . '/travtavlingar/' . $event->slug;
            } else {
                $url = site_url() . '/event/' . $event->slug; // Default fallback
            }

            $output .= '<url>' . "\n";
            $output .= '<loc>' . esc_url($url) . '</loc>' . "\n";
            $output .= '<lastmod>' . htmlspecialchars(date('c', strtotime($event->event_date))) . '</lastmod>' . "\n";
            $output .= '<changefreq>' . $chf . '</changefreq>' . "\n";
            $output .= '<priority>' . $pri . '</priority>' . "\n";
            if (!empty($event->image_url)) {
                $output .= '<image:image>' . "\n";
                $output .= '<image:loc>' . esc_url($event->image_url) . '</image:loc>' . "\n";
                $output .= '</image:image>' . "\n";
            }
            $output .= '</url>' . "\n";
        }
    }

    if (empty($output)) {
        $wpseo_sitemaps->bad_sitemap = true;
        return;
    }

    // Build the full sitemap
    $sitemap = '<urlset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
    $sitemap .= 'xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" ';
    $sitemap .= 'xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd" ';
    $sitemap .= 'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    $sitemap .= $output . '</urlset>';

    $wpseo_sitemaps->set_sitemap($sitemap);
}

function solvalla_generate_origin_combo_sitemap_podcasts() {
    global $wpseo_sitemaps;

    $episodes = [];
    if (class_exists('Libsyn_Podcast_Public')) {
        $episodes = Libsyn_Podcast_Public::get_episodes(99999, 1);
    }

    $output = '';
    if (!empty($episodes)) {
        $chf = 'daily';
        $pri = 1.0;
        foreach ($episodes as $episode) {

            // Overwrite episode image if there is rule in options
            $image_changes = get_field('images_changes', 'option');
            foreach ($image_changes as $image_change) {
                $libsyn_episode_id = $image_change['libsyn_episode_id'];
                $libsyn_image_url = $image_change['libsyn_image_url'];
                if ($libsyn_episode_id && $libsyn_image_url && $episode && $episode->id == $libsyn_episode_id) {
                    $episode->image_url = $libsyn_image_url;
                }
            }

            $url = site_url() . '/podcast/' . $episode->slug;
            $output .= '<url>' . "\n";
            $output .= '<loc>' . esc_url($url) . '</loc>' . "\n";
            $output .= '<lastmod>' . htmlspecialchars(date('c', strtotime($episode->pub_date))) . '</lastmod>' . "\n";
            $output .= '<changefreq>' . $chf . '</changefreq>' . "\n";
            $output .= '<priority>' . $pri . '</priority>' . "\n";
            $output .= '<image:image>' . "\n";
            $output .= '<image:loc>' . esc_url($episode->image_url) . '</image:loc>' . "\n";
            $output .= '</image:image>' . "\n";
            $output .= '</url>' . "\n";
        }
    }

    if (empty($output)) {
        $wpseo_sitemaps->bad_sitemap = true;
        return;
    }

    // Build the full sitemap
    $sitemap = '<urlset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
    $sitemap .= 'xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" ';
    $sitemap .= 'xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd" ';
    $sitemap .= 'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    $sitemap .= $output . '</urlset>';

    $wpseo_sitemaps->set_sitemap($sitemap);
}

/*********************************************************
 *  OR we can use $wpseo_sitemaps->register_sitemap( 'CUSTOM_KEY', 'METHOD' );
 ********************************************************/

add_action('init', 'solvalla_register_my_new_sitemap', 99);
/**
 * On init, run the function that will register our new sitemap as well
 * as the function that will be used to generate the XML. This creates an
 * action that we can hook into built around the new
 * sitemap name - 'wp_seo_do_sitemap_my_new_sitemap'
 */
function solvalla_register_my_new_sitemap() {
    global $wpseo_sitemaps;
    $wpseo_sitemaps->register_sitemap('events', 'solvalla_generate_origin_combo_sitemap_events');
    $wpseo_sitemaps->register_sitemap('podcasts', 'solvalla_generate_origin_combo_sitemap_podcasts');
}
add_action('init', 'init_do_sitemap_actions');

function init_do_sitemap_actions() {
    add_action('wp_seo_do_sitemap_our-events', 'solvalla_generate_origin_combo_sitemap_events');
    add_action('wp_seo_do_sitemap_our-podcasts', 'solvalla_generate_origin_combo_sitemap_podcasts');
}
