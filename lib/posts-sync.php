<?php
// use FileBird\Model\Folder as FolderModel;

// /**
//  * When post is added to another child site from multisite by Broadcast plugin, this function ensures the image uploaded to media library is added to the correct Filebird folder.
//  */
// function broadcast_add_to_filebird_folder($action) {
// 	$bcd = $action->broadcasting_data;
// 	if($bcd->new_child_created) {
//         // get the post ID of the new post imported by Broadcast
// 		$post_ids = [$bcd->new_post->ID];
//         // get the folder ID for current site
//         $folder = (int) get_field('blog_folder_id', 'options');
//         // add the post to the folder
// 		FolderModel::setFoldersForPosts($post_ids, $folder);
// 	}
// }
// add_action('threewp_broadcast_broadcasting_after_update_post', 'broadcast_add_to_filebird_folder');