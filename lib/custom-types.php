<?php

/*
 *
 * Custom Post Types
 *
 */

// Note that you only need the arguments and register_post_type function here. They are hooked into WordPress in functions.php.
if (defined('VM_CPT')) {
	foreach (VM_CPT as $type) {

		$defaults = [
			'hierarchical'        => false,
			'supports'            => ['title', 'editor', 'thumbnail'],
			'public'              => true,
			'show_ui'             => true,
			'show_in_menu'        => true,
			'show_in_nav_menus'   => true,
			'show_in_rest'        => true,
			'publicly_queryable'  => true,
			'exclude_from_search' => false,
			'has_archive'         => true,
			'query_var'           => true,
			'can_export'          => true,
			'capability_type'     => 'post',
			'menu_name'			  => $type['label_plural'],
			'post_type_name'	  => $type['label_plural'],
		];
		$type = array_merge($defaults, $type);

		$CPTLabels = [
			'name'               => __($type['label_plural'], 'visionmate'),
			'singular_name'      => __($type['label_singular'], 'visionmate'),
			'add_new'            => __('Add New', 'visionmate'),
			'add_new_item'       => __('Add New ' . $type['label_singular'], 'visionmate'),
			'edit_item'          => __('Edit ' . $type['label_singular'], 'visionmate'),
			'new_item'           => __('New ' . $type['label_singular'], 'visionmate'),
			'view_item'          => __('View ' . $type['label_singular'], 'visionmate'),
			'search_items'       => __('Search ' . $type['label_plural'], 'visionmate'),
			'not_found'          => __('No ' . $type['label_singular'] . 's found', 'visionmate'),
			'not_found_in_trash' => __('No ' . $type['label_singular'] . 's found in Trash', 'visionmate'),
			'parent_item_colon'  => __('Parent ' . $type['label_singular'] . ':', 'visionmate'),
			'menu_name'          => __($type['menu_name'], 'visionmate'),
		];

		$CPTArgs = [
			'labels'              => $CPTLabels,
			'hierarchical'        => $type['hierarchical'],
			'supports'            => $type['supports'],
			'public'              => $type['public'],
			'show_ui'             => $type['show_ui'],
			'show_in_menu'        => $type['show_in_menu'],
			'show_in_nav_menus'   => $type['show_in_nav_menus'],
			'show_in_rest'        => $type['show_in_rest'],
			'publicly_queryable'  => $type['publicly_queryable'],
			'exclude_from_search' => $type['exclude_from_search'],
			'has_archive'         => $type['has_archive'],
			'query_var'           => $type['query_var'],
			'can_export'          => $type['can_export'],
			'rewrite'             => ['slug' => $type['slug'], 'with_front' => false],
			'menu_icon'           => 'dashicons-' . $type['icon'],
			'capability_type'     => $type['capability_type'],
		];

		register_post_type($type['post_type_name'], $CPTArgs);
	}
}
