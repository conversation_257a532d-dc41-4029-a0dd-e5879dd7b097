<?php

/*
 *
 * YouTube for Solvalla TV
 *
 */

/**
 * Get videos from YouTube XML feed and add them to a custom database table.
 */
function get_youtube_videos_from_xml(string $channel_id) {
    if (!$channel_id) {
        return;
    }
    global $wpdb;
    // the commented out link is for fetching just elitloppet videos
    // $xml_url = 'https://www.youtube.com/feeds/videos.xml?playlist_id=PLUi8ErgbBAOaf2gYJUJgV4hjlScipPsr6';
    $xml_url = 'https://www.youtube.com/feeds/videos.xml?channel_id=' . $channel_id . '&orderby=published';

    // Load the XML content.
    $xml = simplexml_load_file($xml_url);

    if ($xml) {
        // Register namespaces for XPath queries.
        $xml->registerXPathNamespace('yt', 'http://www.youtube.com/xml/schemas/2015');
        $xml->registerXPathNamespace('media', 'http://search.yahoo.com/mrss/');

        // Use XPath to iterate through entries.
        foreach ($xml->entry as $entry) {
            $video_id = (string) $entry->children('yt', true)->videoId;
            $title = (string) $entry->title;
            $video_url = (string) $entry->link['href'];
            $published = date('Y-m-d H:i:s', strtotime((string) $entry->published));
            print_r($entry);
            // Use XPath to retrieve thumbnail_url and views.
            $thumbnail_url = (string) $entry->xpath('media:group/media:thumbnail/@url')[0];
            $views = (int) $entry->xpath('media:group/media:community/media:statistics/@views')[0];
            $description = (string) $entry->xpath('media:group/media:description')[0];
            $hashtags = extract_hashtags($description);
            $event_type = in_array('#elitloppet', $hashtags) ? '#elitloppet' : (in_array('#elitkampen', $hashtags) ? '#elitkampen' : '');

            // Check if video already exists in database.
            $video_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM {$wpdb->prefix}youtube WHERE video_id = %s",
                    $video_id
                )
            );

            // If video doesn't exist, add it to database.
            if (!$video_exists) {
                $db_feedback = $wpdb->insert(
                    $wpdb->prefix . 'youtube',
                    [
                        'video_id' => $video_id,
                        'title' => $title,
                        'video_url' => $video_url,
                        'published' => $published,
                        'thumbnail_url' => $thumbnail_url,
                        'views' => $views,
                        'description' => $description,
                        'hashtags' => $hashtags,
                        'event_type' => $event_type,
                        'live' => 0,
                    ]
                );
            } else {
                $db_feedback = $wpdb->update(
                    $wpdb->prefix . 'youtube',
                    [
                        'video_url' => $video_url,
                        'title' => $title,
                        'video_url' => $video_url,
                        'published' => $published,
                        'thumbnail_url' => $thumbnail_url,
                        'views' => $views,
                        'description' => $description,
                        'hashtags' => maybe_serialize($hashtags),
                        'event_type' => $event_type,
                        'live' => 0,
                    ],
                    [
                        'video_id' => $video_id,
                    ]
                );
            }
            // Check for errors
            if ($db_feedback === false) {
                // There was an error
                $error_message = $wpdb->last_error;
                echo "Update failed. Error: $error_message";
            } else {
                // Update successful
                $rows_affected = $db_feedback;
                echo "Update successful. Rows affected: $rows_affected";
            }
        }
    }
}

/**
 * Get videos from database and sort them by publish date.
 */
function get_youtube_videos($limit = 20, $which_event = []) {
    global $wpdb;

    if (!empty($which_event)) {
        $placeholders = '(' . implode(', ', array_fill(0, count($which_event), '%s')) . ')';
        $query = $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}youtube WHERE event_type IN $placeholders ORDER BY published DESC LIMIT %d",
            array_merge($which_event, [$limit])
        );
    } else {
        $query = $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}youtube ORDER BY published DESC LIMIT %d",
            $limit
        );
    }

    $videos = $wpdb->get_results($query, ARRAY_A);
    return $videos;
}

/**
 * Check if channel is live streaming and return video ID.
 */
function get_live_youtube_video_id(string $channel_name) {
    if (!$channel_name) {
        return;
    }

    // Because YouTube is not generous regarding their API, we have to use a workaround.
    // We load the channel's live page and check if we get redirected to a video.
    // If we do, we know that this video is live.
    $url = 'https://www.youtube.com/' . $channel_name . '/live';
    $html = file_get_contents($url);

    $dom = new DOMDocument;
    libxml_use_internal_errors(true);
    $dom->loadHTML($html);
    $xpath = new DOMXPath($dom);
    $canonical = $xpath->evaluate("string(//link[@rel='canonical']/@href)");

    if (strpos($canonical, 'watch?v=') !== false) {
        $url_parts = parse_url($canonical);
        parse_str($url_parts['query'], $query);
        $video_id = $query['v'];
    } else {
        $video_id = false;
    }

    return $video_id;
}

/**
 * If video is live, save the reference to database.
 */
function save_live_youtube_video_id(string $channel_name) {
    if (!$channel_name) {
        return;
    }
    global $wpdb;
    $video_id = get_live_youtube_video_id($channel_name);

    // Set video as live, unset other videos.
    if ($video_id) {
        $wpdb->update(
            $wpdb->prefix . 'youtube',
            ['live' => 1],
            ['video_id' => $video_id]
        );
        $wpdb->update(
            $wpdb->prefix . 'youtube',
            ['live' => 0],
            ['video_id !=' => $video_id]
        );
    } else {
        $wpdb->update(
            $wpdb->prefix . 'youtube',
            ['live' => 0],
            ['live' => 1]
        );
    }
}

function extract_hashtags($text) {
    preg_match_all('/#\w+/', $text, $matches);
    return $matches[0];
}
