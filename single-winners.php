<?php

/**
 * The template for displaying single trainer.
 */

$context = Timber::get_context();
$post = Timber::query_post();
$context['post'] = $post;


$related_posts = Timber::get_posts([
    'post_type' => $post->post_type,
    'posts_per_page' => '4',
    'post__not_in' => [$post->ID],
    'orderby' => 'date',
    'order' => 'DESC'
]);
$context['related_posts'] = $related_posts;

// Post type slug for breadcrumbs
$post_type = get_post_type();
if ($post_type) {
    $post_type_data = get_post_type_object($post_type);
    $post_type_slug = $post_type_data->rewrite['slug'];
    $context['post_type_slug'] = $post_type_slug;
}

// Archive url for breadcrumbs
$context['archive_title'] = __('Vinnare', 'solvalla');
$archive_url = get_post_type_archive_link('winners');
$archive_url = str_replace('%we-w%/', '', $archive_url);
$context['archive_url'] = $archive_url;

// Get taxonomy terms for breadcrumbs
$taxonomy_terms = get_the_terms($post->ID, 'which-event-winners');
if ($taxonomy_terms && !is_wp_error($taxonomy_terms)) {
    $primary_term = $taxonomy_terms[0]; // Get the first term
    $context['taxonomy_term'] = $primary_term;
    $context['taxonomy_archive_url'] = get_term_link($primary_term);
}

// Add ACF fields to context.
$context['fields'] = [];

if ( post_password_required( $post->ID ) ) {
    Timber::render( 'single-password.twig', $context );
} else {
    Timber::render( 'single-winners.twig', $context );
}
