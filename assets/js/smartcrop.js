!function(){"use strict";var t={};function e(t,e,i){for(var r=i.data,a=i.width,n=~~t.x,o=~~(t.x+t.width),h=~~t.y,s=~~(t.y+t.height),d=255*t.weight,u=h;u<s;u++)for(var g=n;g<o;g++)r[4*(u*a+g)+3]+=d}function i(t,e,i){for(var a={detail:0,saturation:0,skin:0,boost:0,total:0},n=e.data,o=t.scoreDownSample,h=1/o,s=e.height*o,d=e.width*o,u=e.width,g=0;g<s;g+=o)for(var c=0;c<d;c+=o){var f=4*(~~(g*h)*u+~~(c*h)),l=r(t,i,c,g),p=n[f+1]/255;a.skin+=n[f]/255*(p+t.skinBias)*l,a.detail+=p*l,a.saturation+=n[f+2]/255*(p+t.saturationBias)*l,a.boost+=n[f+3]/255*l}return a.total=(a.detail*t.detailWeight+a.skin*t.skinWeight+a.saturation*t.saturationWeight+a.boost*t.boostWeight)/(i.width*i.height),a}function r(t,e,i,r){if(e.x>i||i>=e.x+e.width||e.y>r||r>=e.y+e.height)return t.outsideImportance;i=(i-e.x)/e.width,r=(r-e.y)/e.height;var a=2*g(.5-i),n=2*g(.5-r),o=Math.max(a-1+t.edgeRadius,0),h=Math.max(n-1+t.edgeRadius,0),s=(o*o+h*h)*t.edgeWeight,d=1.41-c(a*a+n*n);return t.ruleOfThirds&&(d+=1.2*Math.max(0,d+s+.5)*(l(a)+l(n))),d+s}function a(t,e,i,r){var a=c(e*e+i*i+r*r),n=e/a-t.skinColor[0],o=i/a-t.skinColor[1],h=r/a-t.skinColor[2];return 1-c(n*n+o*o+h*h)}function n(t,e,i){this.width=t,this.height=e,this.data=i?new Uint8ClampedArray(i):new Uint8ClampedArray(t*e*4)}function o(t,e){for(var i=t.data,r=t.width,a=Math.floor(t.width/e),o=Math.floor(t.height/e),h=new n(a,o),s=h.data,d=1/(e*e),u=0;u<o;u++)for(var g=0;g<a;g++){for(var c=4*(u*a+g),f=0,l=0,p=0,w=0,m=0,v=0,x=0;x<e;x++)for(var y=0;y<e;y++){var M=4*((u*e+x)*r+(g*e+y));f+=i[M],l+=i[M+1],p+=i[M+2],w+=i[M+3],m=Math.max(m,i[M]),v=Math.max(v,i[M+1])}s[c]=f*d*.5+.5*m,s[c+1]=l*d*.7+.3*v,s[c+2]=p*d,s[c+3]=w*d}return h}function h(t,e){var i=document.createElement("canvas");return i.width=t,i.height=e,i}function s(e){return{open:function(i){var r=i.naturalWidth||i.width,a=i.naturalHeight||i.height,n=e(r,a),o=n.getContext("2d");return!i.naturalWidth||i.naturalWidth==i.width&&i.naturalHeight==i.height?(n.width=i.width,n.height=i.height):(n.width=i.naturalWidth,n.height=i.naturalHeight),o.drawImage(i,0,0),t.Promise.resolve(n)},resample:function(i,r,a){return Promise.resolve(i).then((function(i){var n=e(~~r,~~a);return n.getContext("2d").drawImage(i,0,0,i.width,i.height,0,0,n.width,n.height),t.Promise.resolve(n)}))},getData:function(t){return Promise.resolve(t).then((function(t){var e=t.getContext("2d").getImageData(0,0,t.width,t.height);return new n(t.width,t.height,e.data)}))}}}t.Promise="undefined"!=typeof Promise?Promise:function(){throw new Error("No native promises and smartcrop.Promise not set.")},t.DEFAULTS={width:0,height:0,aspect:0,cropWidth:0,cropHeight:0,detailWeight:.2,skinColor:[.78,.57,.44],skinBias:.01,skinBrightnessMin:.2,skinBrightnessMax:1,skinThreshold:.8,skinWeight:1.8,saturationBrightnessMin:.05,saturationBrightnessMax:.9,saturationThreshold:.4,saturationBias:.2,saturationWeight:.1,scoreDownSample:8,step:8,scaleStep:.1,minScale:1,maxScale:1,edgeRadius:.4,edgeWeight:-20,outsideImportance:-.5,boostWeight:100,ruleOfThirds:!0,prescale:!0,imageOperations:null,canvasFactory:h,debug:!1},t.crop=function(r,h,g){var c=f({},t.DEFAULTS,h);c.aspect&&(c.width=c.aspect,c.height=1),null===c.imageOperations&&(c.imageOperations=s(c.canvasFactory));var l=c.imageOperations,v=1,x=1;return l.open(r,c.input).then((function(t){return c.width&&c.height&&(v=d(t.width/c.width,t.height/c.height),c.cropWidth=~~(c.width*v),c.cropHeight=~~(c.height*v),c.minScale=d(c.maxScale,u(1/v,c.minScale)),!1!==c.prescale&&((x=d(u(256/t.width,256/t.height),1))<1?(t=l.resample(t,t.width*x,t.height*x),c.cropWidth=~~(c.cropWidth*x),c.cropHeight=~~(c.cropHeight*x),c.boost&&(c.boost=c.boost.map((function(t){return{x:~~(t.x*x),y:~~(t.y*x),width:~~(t.width*x),height:~~(t.height*x),weight:t.weight}})))):x=1)),t})).then((function(t){return l.getData(t).then((function(t){for(var r=function(t,r){var h={},s=new n(r.width,r.height);(function(t,e){for(var i=t.data,r=e.data,a=t.width,n=t.height,o=0;o<n;o++)for(var h=0;h<a;h++){var s,d=4*(o*a+h);s=0===h||h>=a-1||0===o||o>=n-1?w(i,d):4*w(i,d)-w(i,d-4*a)-w(i,d-4)-w(i,d+4)-w(i,d+4*a),r[d+1]=s}})(r,s),function(t,e,i){for(var r=e.data,n=i.data,o=e.width,h=e.height,s=0;s<h;s++)for(var d=0;d<o;d++){var u=4*(s*o+d),g=p(r[u],r[u+1],r[u+2])/255,c=a(t,r[u],r[u+1],r[u+2]),f=c>t.skinThreshold,l=g>=t.skinBrightnessMin&&g<=t.skinBrightnessMax;n[u]=f&&l?(c-t.skinThreshold)*(255/(1-t.skinThreshold)):0}}(t,r,s),function(t,e,i){for(var r=e.data,a=i.data,n=e.width,o=e.height,h=0;h<o;h++)for(var s=0;s<n;s++){var d=4*(h*n+s),u=p(r[d],r[d+1],r[d+2])/255,g=m(r[d],r[d+1],r[d+2]),c=g>t.saturationThreshold,f=u>=t.saturationBrightnessMin&&u<=t.saturationBrightnessMax;a[d+2]=f&&c?(g-t.saturationThreshold)*(255/(1-t.saturationThreshold)):0}}(t,r,s),function(t,i){if(t.boost){for(var r=i.data,a=0;a<i.width;a+=4)r[a+3]=0;for(a=0;a<t.boost.length;a++)e(t.boost[a],0,i)}}(t,s);for(var u=o(s,t.scoreDownSample),g=-1/0,c=null,l=function(t,e,i){for(var r=[],a=d(e,i),n=t.cropWidth||a,o=t.cropHeight||a,h=t.maxScale;h>=t.minScale;h-=t.scaleStep)for(var s=0;s+o*h<=i;s+=t.step)for(var u=0;u+n*h<=e;u+=t.step)r.push({x:u,y:s,width:n*h,height:o*h});return r}(t,r.width,r.height),v=0,x=l.length;v<x;v++){var y=l[v];y.score=i(t,u,y),y.score.total>g&&(c=y,g=y.score.total)}return h.topCrop=c,t.debug&&c&&(h.crops=l,h.debugOutput=s,h.debugOptions=t,h.debugTopCrop=f({},h.topCrop)),h}(c,t),h=r.crops||[r.topCrop],s=0,u=h.length;s<u;s++){var l=h[s];l.x=~~(l.x/x),l.y=~~(l.y/x),l.width=~~(l.width/x),l.height=~~(l.height/x)}return g&&g(r),r}))}))},t.isAvailable=function(e){return!!t.Promise&&!((e?e.canvasFactory:h)===h&&!document.createElement("canvas").getContext("2d"))},t.importance=r,t.ImgData=n,t._downSample=o,t._canvasImageOperations=s;var d=Math.min,u=Math.max,g=Math.abs,c=Math.sqrt;function f(t){for(var e=1,i=arguments.length;e<i;e++){var r=arguments[e];if(r)for(var a in r)t[a]=r[a]}return t}function l(t){return t=16*((t-1/3+1)%2*.5-.5),Math.max(1-t*t,0)}function p(t,e,i){return.5126*i+.7152*e+.0722*t}function w(t,e){return p(t[e],t[e+1],t[e+2])}function m(t,e,i){var r=u(t/255,e/255,i/255),a=d(t/255,e/255,i/255);if(r===a)return 0;var n=r-a;return(r+a)/2>.5?n/(2-r-a):n/(r+a)}"undefined"!=typeof define&&define.amd&&define((function(){return t})),"undefined"!=typeof exports?exports.smartcrop=t:"undefined"!=typeof navigator&&(window.SmartCrop=window.smartcrop=t),"undefined"!=typeof module&&(module.exports=t)}();