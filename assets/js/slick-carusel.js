"use strict";(self.webpackChunkbathe=self.webpackChunkbathe||[]).push([[408],{739:function(e,s,o){o.d(s,{A:function(){return a}});var a=Object.freeze({xs:0,sm:576,md:782,lg:992,xl:1200})},616:function(e,s,o){o.r(s),o(154),o(739);var a=o(755);({init:function(){this.homeHeroCarusel()},homeHeroCarusel:function(){var e=a("[data-slick='home-hero']");e.length&&e.slick({dots:!1,autoplay:!0,infinite:!0,speed:1e3,arrows:!1,slidesToShow:1,slidesToScroll:1,rows:0,fade:!0,draggable:!1,mobileFirst:!0,pauseOnFocus:!1,pauseOnHover:!1,autoplaySpeed:3e3})}}).init()}}]);