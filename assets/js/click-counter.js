(self.webpackChunkbathe=self.webpackChunkbathe||[]).push([[2],{639:function(){var e;e=new IntersectionObserver((function(e,n){e.forEach((function(e){if(e.isIntersecting||e.isVisible){var t=e.target.getAttribute("data-banner-id");fetch("/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:"action=update_view_count&post_id="+t}).then((function(e){return e.json()})).then((function(e){console.log(e)})),n.unobserve(e.target)}}))}),{threshold:0,rootMargin:"200px 0px 0px 0px"}),document.querySelectorAll(".js-banner").forEach((function(n){e.observe(n),n.addEventListener("click",(function(e){var t=n.getAttribute("data-banner-id");fetch("/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:"action=update_click_count&post_id="+t}).then((function(e){return e.json()})).then((function(e){console.log(e)}))}))}))}}]);