<?php
/**
 * The template for displaying single trainer.
 */

$context = Timber::get_context();
$post = Timber::query_post();
$context['post'] = $post;

// Post type slug for breadcrumbs
$post_type = get_post_type();
if ( $post_type ) {
    $post_type_data = get_post_type_object( $post_type );
    $post_type_slug = $post_type_data->rewrite['slug'];
	$context['post_type_slug'] = $post_type_slug;
}

// Archive url for breadcrumbs
$context['archive_title'] = __('Proffsstränare', 'solvalla');
$post_type_data = get_post_type_object( $post_type );
$post_type_slug = $post_type_data->rewrite['slug'];
$context['archive_url'] = site_url() . '/' . $post_type_slug . '/';

// Add ACF fields to context.
$context['fields'] = [
    'featured_image'    => get_field( 'featured_image' ),
    'address'           => get_field( 'address' ),
    'phone'             => get_field( 'phone' ),
    'mobile'            => get_field( 'mobile' ),
    'email'             => get_field( 'email' ),
    'website'           => get_field( 'website' ),
    'statistics'        => get_field( 'statistics' ),
    'training_list'     => get_field( 'training_list' ),
];

if ( post_password_required( $post->ID ) ) {
    Timber::render( 'single-password.twig', $context );
} else {
    Timber::render( 'single-trainers.twig', $context );
}
