<?php

/**
 * The Template for displaying all single posts
 *
 * Methods for <PERSON>berHelper can be found in the /lib sub-directory
 *
 * @package  WordPress
 * @subpackage  Timber
 * @since    Timber 0.1
 */

$context = Timber::get_context();
$post = new TimberPost();
$context['post'] = $post;

$related_posts = Timber::get_posts([
	'post_type' => $post->post_type,
	'posts_per_page' => '4',
	'post__not_in' => [$post->ID],
	'orderby' => 'date',
	'order' => 'DESC'
]);
$context['related_posts'] = $related_posts;

// Setup related posts custom fields, for example the custom image field
$related_posts_fields = [];
for ($i = 0; $i < 4; $i++) {
	$related_posts_fields[$related_posts[$i]->ID] = [
		'detail_image'  => get_field('detail_image', $related_posts[$i]->ID)
	];
}
$context['related_posts_fields'] = $related_posts_fields;

// Add ACF fields to context.
$fields = [
	'detail_image'  => get_field('detail_image'),
	'caption' 		=> get_field('image_caption'),
	'related_posts_fields' => $related_posts_fields,
];
$context['fields'] = $fields;

if ($post->post_type == 'post') {
	$context['archive_title'] = __('Nyheter', 'solvalla');
	$context['archive_url'] = get_site_url() . '/nyheter/';
} elseif ($post->post_type == 'hall-of-fame') {
	$context['archive_title'] = __('Solvalla i Hall of fame', 'solvalla');
	$context['archive_url'] = get_site_url() . '/sport/hall-of-fame/';
} elseif ($post->post_type == 'result-photos') {
	$context['archive_title'] = __('Målfoton', 'solvalla');
	$context['archive_url'] = get_site_url() . '/sport/resultat-malfoton/';
}

if ( post_password_required( $post->ID ) ) {
    Timber::render( 'single-password.twig', $context );
} else {
	Timber::render(array('single-' . $post->ID . '.twig', 'single-' . $post->post_type . '.twig', 'single.twig'), $context);
}
